{"version": "0.2.0", "configurations": [{"name": "Debug React Native Android", "type": "reactnative", "request": "launch", "platform": "android", "sourceMaps": true, "outDir": "${workspaceFolder}/react-native/.expo", "restart": true, "port": 8081, "address": "localhost", "localRoot": "${workspaceFolder}/react-native", "remoteRoot": "${workspaceFolder}/react-native", "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Backend Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/server.js", "console": "integratedTerminal", "restart": true, "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "Debug Full Stack", "configurations": ["Debug React Native Android", "Debug Backend Server"]}]}