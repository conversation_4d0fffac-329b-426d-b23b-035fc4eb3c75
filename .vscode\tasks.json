{"version": "2.0.0", "tasks": [{"label": "Start React Native Metro", "type": "shell", "command": "npx", "args": ["expo", "start", "--clear"], "options": {"cwd": "${workspaceFolder}/react-native"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "Install React Native Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/react-native"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start Backend Server", "type": "shell", "command": "node", "args": ["server.js"], "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start Full Project (Metro + Backend)", "dependsOrder": "parallel", "dependsOn": ["Start React Native Metro", "Start Backend Server"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}]}