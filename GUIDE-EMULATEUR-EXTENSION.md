# Guide d'utilisation de l'extension Android iOS Emulator

## 🎯 Objectif
Ce guide vous explique comment utiliser l'extension Android iOS Emulator avec votre projet React Native AquaTrack.

## 📋 Prérequis

### 1. Installation d'Android Studio
- Téléchargez et installez Android Studio
- Configurez le SDK Android (API 30 ou plus récent recommandé)
- Créez au moins un AVD (Android Virtual Device)

### 2. Variables d'environnement
Ajoutez ces variables à votre système :
```
ANDROID_HOME = C:\Users\<USER>\AppData\Local\Android\Sdk
JAVA_HOME = C:\Program Files\Android\Android Studio\jbr
```

### 3. Extension VS Code
- Installez l'extension "Android iOS Emulator" dans VS Code
- Redémarrez VS Code après installation

## 🚀 Utilisation de l'extension

### Méthode 1 : Palette de commandes
1. **Ouvrir la palette** : `Ctrl + Shift + P`
2. **Taper** : `Android Emulator`
3. **Sélectionner** : "Android Emulator: Start Android Emulator"
4. **Choisir** votre émulateur dans la liste

### Méthode 2 : Barre d'état
- Cliquez sur l'icône Android dans la barre d'état VS Code
- Sélectionnez votre émulateur

### Méthode 3 : Menu contextuel
- Clic droit dans l'explorateur de fichiers
- Sélectionnez "Start Android Emulator"

## 📱 Lancement du projet

### Étape 1 : Démarrer l'émulateur
Utilisez l'extension pour démarrer votre émulateur Android

### Étape 2 : Lancer le projet
```bash
# Option A : Script PowerShell automatisé
.\start-emulator-project.ps1

# Option B : Commandes manuelles
cd react-native
npx expo start
```

### Étape 3 : Connecter l'app à l'émulateur
1. Dans le terminal Metro, appuyez sur `a` pour Android
2. Ou scannez le QR code avec l'app Expo Go

## 🔧 Configuration avancée

### Paramètres de l'extension
Les paramètres sont configurés dans `.vscode/settings.json` :
- `emulator.emulatorPath` : Chemin vers l'émulateur
- `emulator.adbPath` : Chemin vers ADB
- `emulator.showQuickPick` : Afficher la liste des émulateurs

### Tâches VS Code
Utilisez `Ctrl + Shift + P` puis "Tasks: Run Task" :
- **Start React Native Metro** : Lance le serveur Metro
- **Start Backend Server** : Lance le serveur Node.js
- **Start Full Project** : Lance tout en parallèle

## 🐛 Résolution de problèmes

### Émulateur ne démarre pas
1. Vérifiez que Android Studio est installé
2. Vérifiez les variables d'environnement
3. Redémarrez VS Code
4. Vérifiez que l'AVD existe dans Android Studio

### Metro ne se connecte pas
1. Vérifiez que le port 8081 est libre
2. Redémarrez Metro avec `npx expo start --clear`
3. Vérifiez la connexion réseau de l'émulateur

### App ne s'installe pas
1. Vérifiez que l'émulateur est démarré
2. Vérifiez la connexion ADB : `adb devices`
3. Nettoyez le cache : `npx expo start --clear`

## 📂 Structure du projet

```
samle-react-app/
├── react-native/          # Code React Native
│   ├── AuthApp.js         # Point d'entrée principal
│   ├── screens/           # Écrans de l'app
│   └── package.json       # Dépendances RN
├── backend/               # Serveur Node.js
│   └── server.js          # API backend
├── .vscode/               # Configuration VS Code
│   ├── settings.json      # Paramètres extension
│   ├── tasks.json         # Tâches automatisées
│   └── launch.json        # Configuration debug
└── start-emulator-project.ps1  # Script de lancement
```

## 🎯 Workflow recommandé

1. **Ouvrir VS Code** dans le dossier du projet
2. **Démarrer l'émulateur** via l'extension
3. **Lancer les tâches** : `Ctrl + Shift + P` → "Tasks: Run Task" → "Start Full Project"
4. **Développer** : Les changements se rechargent automatiquement
5. **Déboguer** : Utilisez F5 pour le débogage

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez ce guide
2. Consultez les logs dans le terminal VS Code
3. Redémarrez VS Code et l'émulateur
4. Vérifiez la configuration Android Studio

## 🔄 Mise à jour

Pour mettre à jour l'extension :
1. Allez dans Extensions (`Ctrl + Shift + X`)
2. Recherchez "Android iOS Emulator"
3. Cliquez sur "Update" si disponible
