# Script de vérification de l'environnement pour l'extension Android iOS Emulator
# Utilisation: .\check-emulator-setup.ps1

Write-Host "🔍 Vérification de l'environnement pour l'extension Android iOS Emulator" -ForegroundColor Green
Write-Host "=================================================================" -ForegroundColor Green

$allGood = $true

# 1. Vérifier Android Studio / SDK
Write-Host "`n📱 Vérification d'Android Studio..." -ForegroundColor Yellow

$androidHome = $env:ANDROID_HOME
if (-not $androidHome) {
    $androidHome = "$env:LOCALAPPDATA\Android\Sdk"
}

if (Test-Path $androidHome) {
    Write-Host "✅ Android SDK trouvé: $androidHome" -ForegroundColor Green
    
    # Vérifier l'émulateur
    $emulatorPath = "$androidHome\emulator\emulator.exe"
    if (Test-Path $emulatorPath) {
        Write-Host "✅ Émulateur Android trouvé" -ForegroundColor Green
    } else {
        Write-Host "❌ Émulateur Android non trouvé dans: $emulatorPath" -ForegroundColor Red
        $allGood = $false
    }
    
    # Vérifier ADB
    $adbPath = "$androidHome\platform-tools\adb.exe"
    if (Test-Path $adbPath) {
        Write-Host "✅ ADB trouvé" -ForegroundColor Green
    } else {
        Write-Host "❌ ADB non trouvé dans: $adbPath" -ForegroundColor Red
        $allGood = $false
    }
} else {
    Write-Host "❌ Android SDK non trouvé. Installez Android Studio." -ForegroundColor Red
    $allGood = $false
}

# 2. Vérifier Node.js
Write-Host "`n🟢 Vérification de Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js installé: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js non installé" -ForegroundColor Red
    $allGood = $false
}

# 3. Vérifier npm
Write-Host "`n📦 Vérification de npm..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "✅ npm installé: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm non installé" -ForegroundColor Red
    $allGood = $false
}

# 4. Vérifier Expo CLI
Write-Host "`n🚀 Vérification d'Expo..." -ForegroundColor Yellow
try {
    $expoVersion = npx expo --version
    Write-Host "✅ Expo CLI disponible: $expoVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Expo CLI non installé globalement (normal avec npx)" -ForegroundColor Yellow
}

# 5. Vérifier les dépendances du projet
Write-Host "`n📂 Vérification du projet..." -ForegroundColor Yellow

if (Test-Path "react-native\package.json") {
    Write-Host "✅ Projet React Native trouvé" -ForegroundColor Green
    
    if (Test-Path "react-native\node_modules") {
        Write-Host "✅ Dépendances React Native installées" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Dépendances React Native non installées" -ForegroundColor Yellow
        Write-Host "   Exécutez: cd react-native && npm install" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Projet React Native non trouvé" -ForegroundColor Red
    $allGood = $false
}

if (Test-Path "backend\package.json") {
    Write-Host "✅ Backend trouvé" -ForegroundColor Green
    
    if (Test-Path "backend\node_modules") {
        Write-Host "✅ Dépendances backend installées" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Dépendances backend non installées" -ForegroundColor Yellow
        Write-Host "   Exécutez: cd backend && npm install" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Backend non trouvé" -ForegroundColor Red
    $allGood = $false
}

# 6. Vérifier la configuration VS Code
Write-Host "`n⚙️ Vérification de la configuration VS Code..." -ForegroundColor Yellow

if (Test-Path ".vscode\settings.json") {
    Write-Host "✅ Configuration VS Code trouvée" -ForegroundColor Green
} else {
    Write-Host "⚠️ Configuration VS Code manquante" -ForegroundColor Yellow
}

# 7. Lister les émulateurs disponibles
Write-Host "`n📱 Émulateurs Android disponibles..." -ForegroundColor Yellow
try {
    if (Test-Path $emulatorPath) {
        $emulators = & $emulatorPath -list-avds 2>$null
        if ($emulators) {
            Write-Host "✅ Émulateurs trouvés:" -ForegroundColor Green
            foreach ($emulator in $emulators) {
                Write-Host "   - $emulator" -ForegroundColor Cyan
            }
        } else {
            Write-Host "⚠️ Aucun émulateur configuré" -ForegroundColor Yellow
            Write-Host "   Créez un AVD dans Android Studio" -ForegroundColor Cyan
        }
    }
} catch {
    Write-Host "❌ Impossible de lister les émulateurs" -ForegroundColor Red
}

# Résumé final
Write-Host "`n" + "="*65 -ForegroundColor Green
if ($allGood) {
    Write-Host "🎉 Environnement prêt pour l'extension Android iOS Emulator !" -ForegroundColor Green
    Write-Host "`n📋 Prochaines étapes:" -ForegroundColor Cyan
    Write-Host "   1. Ouvrez VS Code dans ce dossier" -ForegroundColor White
    Write-Host "   2. Installez l'extension 'Android iOS Emulator'" -ForegroundColor White
    Write-Host "   3. Utilisez Ctrl+Shift+P → 'Android Emulator'" -ForegroundColor White
    Write-Host "   4. Lancez votre projet avec .\start-emulator-project.ps1" -ForegroundColor White
} else {
    Write-Host "❌ Problèmes détectés. Corrigez-les avant de continuer." -ForegroundColor Red
    Write-Host "`n📋 Actions requises:" -ForegroundColor Cyan
    Write-Host "   1. Installez Android Studio si nécessaire" -ForegroundColor White
    Write-Host "   2. Configurez les variables d'environnement" -ForegroundColor White
    Write-Host "   3. Créez un émulateur Android (AVD)" -ForegroundColor White
    Write-Host "   4. Installez les dépendances: npm install" -ForegroundColor White
}

Write-Host "`nAppuyez sur une touche pour fermer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
