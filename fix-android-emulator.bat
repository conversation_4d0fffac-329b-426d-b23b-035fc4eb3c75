@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    CORRECTION ANDROID EMULATOR
echo ========================================
echo.

echo [INFO] Resolution de l'erreur Android Emulator Extension
echo.

REM Définir les chemins corrects
set "ANDROID_SDK_ROOT=C:\Users\<USER>\AppData\Local\Android\Sdk"
set "EMULATOR_PATH=%ANDROID_SDK_ROOT%\emulator\emulator.exe"
set "AVDMANAGER_PATH=%ANDROID_SDK_ROOT%\cmdline-tools\latest\bin\avdmanager.bat"

echo [1/5] Verification des chemins Android SDK...
if exist "%ANDROID_SDK_ROOT%" (
    echo ✓ Android SDK trouve: %ANDROID_SDK_ROOT%
) else (
    echo ❌ Android SDK non trouve!
    echo Veuillez installer Android Studio ou Android SDK
    pause
    exit /b 1
)

if exist "%EMULATOR_PATH%" (
    echo ✓ Emulateur trouve: %EMULATOR_PATH%
) else (
    echo ❌ Emulateur non trouve!
    pause
    exit /b 1
)

echo.
echo [2/5] Configuration des variables d'environnement...

REM Ajouter Android SDK au PATH temporairement
set "PATH=%ANDROID_SDK_ROOT%\emulator;%ANDROID_SDK_ROOT%\platform-tools;%ANDROID_SDK_ROOT%\tools;%PATH%"

echo ✓ Variables configurees
echo.

echo [3/5] Verification des AVDs existants...
"%EMULATOR_PATH%" -list-avds > avds_list.txt 2>&1

REM Lire le fichier pour voir s'il y a des AVDs
set "avd_count=0"
for /f %%i in (avds_list.txt) do (
    set /a avd_count+=1
    echo   - %%i
)

if %avd_count% equ 0 (
    echo ⚠ Aucun AVD trouve
    echo.
    echo [4/5] Creation d'un AVD par defaut...
    
    REM Vérifier si cmdline-tools existe
    if exist "%AVDMANAGER_PATH%" (
        echo Creation d'un AVD Pixel 4...
        "%AVDMANAGER_PATH%" create avd -n "Pixel_4_API_30" -k "system-images;android-30;google_apis;x86_64" --force
        if %errorlevel% equ 0 (
            echo ✓ AVD cree avec succes
        ) else (
            echo ⚠ Echec de creation AVD - utilisez Android Studio
        )
    ) else (
        echo ⚠ cmdline-tools non trouve
        echo Veuillez creer un AVD via Android Studio:
        echo 1. Ouvrez Android Studio
        echo 2. Allez dans Tools ^> AVD Manager
        echo 3. Cliquez sur "Create Virtual Device"
        echo 4. Choisissez un appareil et une API
    )
) else (
    echo ✓ %avd_count% AVD(s) trouve(s)
)

echo.
echo [5/5] Configuration de l'extension VS Code...

REM Créer un fichier de configuration pour VS Code
echo {> .vscode\settings.json
echo   "emulate.androidHome": "%ANDROID_SDK_ROOT:\=\\%",>> .vscode\settings.json
echo   "emulate.androidEmulatorPath": "%EMULATOR_PATH:\=\\%">> .vscode\settings.json
echo }>> .vscode\settings.json

echo ✓ Configuration VS Code mise a jour
echo.

echo ========================================
echo RESOLUTION TERMINEE!
echo ========================================
echo.

echo CHEMINS CONFIGURES:
echo - Android SDK: %ANDROID_SDK_ROOT%
echo - Emulateur: %EMULATOR_PATH%
echo.

echo PROCHAINES ETAPES:
echo 1. Redemarrez VS Code
echo 2. Appuyez sur Ctrl + Shift + P
echo 3. Tapez "Emulate" et selectionnez "Emulate: Run Android"
echo 4. Choisissez votre AVD dans la liste
echo.

if %avd_count% equ 0 (
    echo IMPORTANT: Aucun AVD trouve!
    echo Creez un AVD via Android Studio:
    echo - Ouvrez Android Studio
    echo - Tools ^> AVD Manager
    echo - Create Virtual Device
    echo.
)

echo Voulez-vous ouvrir Android Studio pour creer un AVD? (O/N)
set /p choice="Votre choix: "
if /i "!choice!"=="O" (
    echo Ouverture d'Android Studio...
    start "" "C:\Program Files\Android\Android Studio\bin\studio64.exe" 2>nul
    if %errorlevel% neq 0 (
        echo Android Studio non trouve dans le chemin par defaut
        echo Ouvrez-le manuellement
    )
)

REM Nettoyer
del avds_list.txt 2>nul

echo.
echo Configuration terminee!
pause
