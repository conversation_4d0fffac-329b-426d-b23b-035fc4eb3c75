@echo off
echo ========================================
echo    CORRECTION ANDROID EMULATOR
echo ========================================
echo.

REM Créer le dossier .vscode s'il n'existe pas
if not exist ".vscode" mkdir .vscode

REM Créer le fichier de configuration VS Code
echo { > .vscode\settings.json
echo   "emulate.androidHome": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", >> .vscode\settings.json
echo   "emulate.androidEmulatorPath": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\emulator\\emulator.exe" >> .vscode\settings.json
echo } >> .vscode\settings.json

echo [INFO] Configuration VS Code mise a jour
echo.

echo [INFO] Verification des AVDs...
"C:\Users\<USER>\AppData\Local\Android\Sdk\emulator\emulator.exe" -list-avds

echo.
echo ========================================
echo SOLUTION POUR L'ERREUR
echo ========================================
echo.

echo PROBLEME: L'extension ne trouve pas les emulateurs Android
echo.

echo SOLUTIONS:
echo.
echo 1. CREER UN AVD (RECOMMANDE):
echo    - Ouvrez Android Studio
echo    - Allez dans Tools ^> AVD Manager
echo    - Cliquez sur "Create Virtual Device"
echo    - Choisissez un appareil (ex: Pixel 4)
echo    - Selectionnez une API (ex: API 30)
echo    - Cliquez sur "Finish"
echo.

echo 2. REDEMARRER VS CODE:
echo    - Fermez VS Code completement
echo    - Rouvrez VS Code
echo    - Essayez l'extension a nouveau
echo.

echo 3. ALTERNATIVE - UTILISER EXPO GO:
echo    - Installez "Expo Go" sur votre telephone
echo    - Lancez: npx expo start
echo    - Scannez le QR code avec Expo Go
echo.

echo ========================================
echo COMMANDES UTILES
echo ========================================
echo.

echo Pour lister les AVDs:
echo "C:\Users\<USER>\AppData\Local\Android\Sdk\emulator\emulator.exe" -list-avds
echo.

echo Pour demarrer un AVD manuellement:
echo "C:\Users\<USER>\AppData\Local\Android\Sdk\emulator\emulator.exe" -avd NOM_AVD
echo.

echo Voulez-vous ouvrir Android Studio pour creer un AVD? (O/N)
set /p choice="Votre choix: "
if /i "%choice%"=="O" (
    echo Tentative d'ouverture d'Android Studio...
    start "" "C:\Program Files\Android\Android Studio\bin\studio64.exe" 2>nul
    if errorlevel 1 (
        echo Android Studio non trouve. Ouvrez-le manuellement.
    )
)

echo.
echo Configuration terminee!
pause
