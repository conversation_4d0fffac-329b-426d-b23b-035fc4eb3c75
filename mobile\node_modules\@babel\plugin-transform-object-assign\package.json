{"name": "@babel/plugin-transform-object-assign", "version": "7.27.1", "description": "Replace Object.assign with an inline helper", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-object-assign"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-assign", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}