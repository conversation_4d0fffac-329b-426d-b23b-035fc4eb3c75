{"version": 3, "names": ["currentTypes", "wrapWithTypes", "types", "fn", "args", "oldTypes", "apply", "getTypes", "newHelpersAvailable", "exports", "file", "availableHelper", "isIdentifier", "addHelper", "name", "runtimeProperty", "t", "helper", "memberExpression", "isArrowFunctionExpression", "body", "callExpression", "identifier", "isReference", "path", "isReferenced", "parentPath", "isAssignmentExpression", "left", "node", "replaceWithOrRemove", "replacement", "replaceWith", "remove"], "sources": ["../../src/regenerator/util.ts"], "sourcesContent": ["import type { PluginPass } from \"@babel/core\";\n\nlet currentTypes: any = null;\n\nexport function wrapWithTypes<This, Args extends unknown[]>(\n  types: any,\n  fn: (this: This, ...args: Args) => unknown,\n) {\n  return function (this: This, ...args: Args) {\n    const oldTypes = currentTypes;\n    currentTypes = types;\n    try {\n      return fn.apply(this, args);\n    } finally {\n      currentTypes = oldTypes;\n    }\n  };\n}\n\nexport function getTypes() {\n  return currentTypes;\n}\n\nexport let newHelpersAvailable: (file: PluginPass) => boolean;\nif (!process.env.BABEL_8_BREAKING) {\n  newHelpersAvailable = (file: PluginPass) => {\n    if (!process.env.IS_PUBLISH && process.env.FORCE_OLD_REGENERATOR) {\n      // Only for testing purposes.\n      return false;\n    }\n    return (\n      file.availableHelper(\"regenerator\") &&\n      // At this point, we can safely try to inject the `regenerator` helper.\n      // If this plugin tries to inject any helper, than we are sure that\n      // `regenerator` is one of them.\n      !getTypes().isIdentifier(file.addHelper(\"regenerator\"), {\n        // This is a special marker returned by transform-runtime, which means\n        // \"the version of `@babel/runtime` does not have the helper\".\n        // Normally transform-runtime will fallback to just injecting the\n        // helper inline, but we special handle this case to instead be able\n        // to fallback to the old `regeneratorRuntime` helper\n        name: \"__interal_marker_fallback_regenerator__\",\n      })\n    );\n  };\n}\n\nexport let runtimeProperty: (file: PluginPass, name: any) => any;\nif (!process.env.BABEL_8_BREAKING) {\n  runtimeProperty = function (file, name) {\n    const t = getTypes();\n    const helper = file.addHelper(\"regeneratorRuntime\");\n    return t.memberExpression(\n      // In some cases, `helper` will be (() => regeneratorRuntime).\n      // Se the implementation in transform-runtime for more details.\n      t.isArrowFunctionExpression(helper) &&\n        t.isIdentifier((helper as any).body)\n        ? (helper as any).body\n        : t.callExpression(helper, []),\n      t.identifier(name),\n      false,\n    );\n  };\n}\n\nexport function isReference(path: any) {\n  return (\n    path.isReferenced() ||\n    path.parentPath.isAssignmentExpression({ left: path.node })\n  );\n}\n\nexport function replaceWithOrRemove(path: any, replacement: any) {\n  if (replacement) {\n    path.replaceWith(replacement);\n  } else {\n    path.remove();\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAIA,YAAiB,GAAG,IAAI;AAErB,SAASC,aAAaA,CAC3BC,KAAU,EACVC,EAA0C,EAC1C;EACA,OAAO,UAAsB,GAAGC,IAAU,EAAE;IAC1C,MAAMC,QAAQ,GAAGL,YAAY;IAC7BA,YAAY,GAAGE,KAAK;IACpB,IAAI;MACF,OAAOC,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IAC7B,CAAC,SAAS;MACRJ,YAAY,GAAGK,QAAQ;IACzB;EACF,CAAC;AACH;AAEO,SAASE,QAAQA,CAAA,EAAG;EACzB,OAAOP,YAAY;AACrB;AAEO,IAAIQ,mBAAkD,GAAAC,OAAA,CAAAD,mBAAA;AAC1B;EACjCC,OAAA,CAAAD,mBAAA,GAAAA,mBAAmB,GAAIE,IAAgB,IAAK;IAAA;IAK1C,OACEA,IAAI,CAACC,eAAe,CAAC,aAAa,CAAC,IAInC,CAACJ,QAAQ,CAAC,CAAC,CAACK,YAAY,CAACF,IAAI,CAACG,SAAS,CAAC,aAAa,CAAC,EAAE;MAMtDC,IAAI,EAAE;IACR,CAAC,CAAC;EAEN,CAAC;AACH;AAEO,IAAIC,eAAqD,GAAAN,OAAA,CAAAM,eAAA;AAC7B;EACjCN,OAAA,CAAAM,eAAA,GAAAA,eAAe,GAAG,SAAAA,CAAUL,IAAI,EAAEI,IAAI,EAAE;IACtC,MAAME,CAAC,GAAGT,QAAQ,CAAC,CAAC;IACpB,MAAMU,MAAM,GAAGP,IAAI,CAACG,SAAS,CAAC,oBAAoB,CAAC;IACnD,OAAOG,CAAC,CAACE,gBAAgB,CAGvBF,CAAC,CAACG,yBAAyB,CAACF,MAAM,CAAC,IACjCD,CAAC,CAACJ,YAAY,CAAEK,MAAM,CAASG,IAAI,CAAC,GACjCH,MAAM,CAASG,IAAI,GACpBJ,CAAC,CAACK,cAAc,CAACJ,MAAM,EAAE,EAAE,CAAC,EAChCD,CAAC,CAACM,UAAU,CAACR,IAAI,CAAC,EAClB,KACF,CAAC;EACH,CAAC;AACH;AAEO,SAASS,WAAWA,CAACC,IAAS,EAAE;EACrC,OACEA,IAAI,CAACC,YAAY,CAAC,CAAC,IACnBD,IAAI,CAACE,UAAU,CAACC,sBAAsB,CAAC;IAAEC,IAAI,EAAEJ,IAAI,CAACK;EAAK,CAAC,CAAC;AAE/D;AAEO,SAASC,mBAAmBA,CAACN,IAAS,EAAEO,WAAgB,EAAE;EAC/D,IAAIA,WAAW,EAAE;IACfP,IAAI,CAACQ,WAAW,CAACD,WAAW,CAAC;EAC/B,CAAC,MAAM;IACLP,IAAI,CAACS,MAAM,CAAC,CAAC;EACf;AACF", "ignoreList": []}