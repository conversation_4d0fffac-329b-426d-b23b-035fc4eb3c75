{"version": 3, "file": "hammer.js", "sources": ["../src/utils/assign.js", "../src/utils/utils-consts.js", "../src/utils/prefixed.js", "../src/browser.js", "../src/touchactionjs/get-touchaction-props.js", "../src/touchactionjs/touchaction-Consts.js", "../src/inputjs/input-consts.js", "../src/utils/each.js", "../src/utils/bool-or-fn.js", "../src/utils/in-str.js", "../src/touchactionjs/clean-touch-actions.js", "../src/touchactionjs/touchaction-constructor.js", "../src/utils/has-parent.js", "../src/inputjs/get-center.js", "../src/inputjs/simple-clone-input-data.js", "../src/inputjs/get-distance.js", "../src/inputjs/get-angle.js", "../src/inputjs/get-direction.js", "../src/inputjs/compute-delta-xy.js", "../src/inputjs/get-velocity.js", "../src/inputjs/get-scale.js", "../src/inputjs/get-rotation.js", "../src/inputjs/compute-interval-input-data.js", "../src/inputjs/compute-input-data.js", "../src/inputjs/input-handler.js", "../src/utils/split-str.js", "../src/utils/add-event-listeners.js", "../src/utils/remove-event-listeners.js", "../src/utils/get-window-for-element.js", "../src/inputjs/input-constructor.js", "../src/utils/in-array.js", "../src/input/pointerevent.js", "../src/utils/to-array.js", "../src/utils/unique-array.js", "../src/input/touch.js", "../src/input/mouse.js", "../src/input/touchmouse.js", "../src/inputjs/create-input-instance.js", "../src/utils/invoke-array-arg.js", "../src/recognizerjs/recognizer-consts.js", "../src/utils/unique-id.js", "../src/recognizerjs/get-recognizer-by-name-if-manager.js", "../src/recognizerjs/state-str.js", "../src/recognizerjs/recognizer-constructor.js", "../src/recognizers/tap.js", "../src/recognizers/attribute.js", "../src/recognizerjs/direction-str.js", "../src/recognizers/pan.js", "../src/recognizers/swipe.js", "../src/recognizers/pinch.js", "../src/recognizers/rotate.js", "../src/recognizers/press.js", "../src/defaults.js", "../src/manager.js", "../src/input/singletouch.js", "../src/utils/deprecate.js", "../src/utils/extend.js", "../src/utils/merge.js", "../src/utils/inherit.js", "../src/utils/bind-fn.js", "../src/hammer.js"], "sourcesContent": ["/**\n * @private\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} target\n * @param {...Object} objects_to_assign\n * @returns {Object} target\n */\nlet assign;\nif (typeof Object.assign !== 'function') {\n  assign = function assign(target) {\n    if (target === undefined || target === null) {\n      throw new TypeError('Cannot convert undefined or null to object');\n    }\n\n    let output = Object(target);\n    for (let index = 1; index < arguments.length; index++) {\n      const source = arguments[index];\n      if (source !== undefined && source !== null) {\n        for (const nextKey in source) {\n          if (source.hasOwnProperty(nextKey)) {\n            output[nextKey] = source[nextKey];\n          }\n        }\n      }\n    }\n    return output;\n  };\n} else {\n  assign = Object.assign;\n}\n\nexport default assign;", "\nconst VENDOR_PREFIXES = ['', 'webkit', 'Moz', 'MS', 'ms', 'o'];\nconst TEST_ELEMENT = typeof document === \"undefined\" ? {style: {}} : document.createElement('div');\n\nconst TYPE_FUNCTION = 'function';\n\nconst { round, abs } = Math;\nconst { now } = Date;\n\nexport {\n    VENDOR_PREFIXES,\n    TEST_ELEMENT,\n    TYPE_FUNCTION,\n    round,\n    abs,\n    now\n};\n", "import { VENDOR_PREFIXES } from './utils-consts';\n/**\n * @private\n * get the prefixed property\n * @param {Object} obj\n * @param {String} property\n * @returns {String|Undefined} prefixed\n */\nexport default function prefixed(obj, property) {\n  let prefix;\n  let prop;\n  let camelProp = property[0].toUpperCase() + property.slice(1);\n\n  let i = 0;\n  while (i < VENDOR_PREFIXES.length) {\n    prefix = VENDOR_PREFIXES[i];\n    prop = (prefix) ? prefix + camelProp : property;\n\n    if (prop in obj) {\n      return prop;\n    }\n    i++;\n  }\n  return undefined;\n}\n", "/* eslint-disable no-new-func, no-nested-ternary */\n\nlet win;\n\nif (typeof window === \"undefined\") {\n\t// window is undefined in node.js\n\twin = {};\n} else {\n\twin = window;\n}\n/* eslint-enable no-new-func, no-nested-ternary */\n\nexport {win as window};\n", "import prefixed from '../utils/prefixed';\nimport { TEST_ELEMENT } from '../utils/utils-consts';\nimport {window} from '../browser';\n\nexport const PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, 'touchAction');\nexport const NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== undefined;\n\nexport default function getTouchActionProps() {\n  if (!NATIVE_TOUCH_ACTION) {\n    return false;\n  }\n  let touchMap = {};\n  let cssSupports = window.CSS && window.CSS.supports;\n  ['auto', 'manipulation', 'pan-y', 'pan-x', 'pan-x pan-y', 'none'].forEach((val) => {\n\n    // If css.supports is not supported but there is native touch-action assume it supports\n    // all values. This is the case for IE 10 and 11.\n    return touchMap[val] = cssSupports ? window.CSS.supports('touch-action', val) : true;\n  });\n  return touchMap;\n}\n", "import getTouchActionProps from './get-touchaction-props';\n\n\n\n// magical touchAction value\nconst TOUCH_ACTION_COMPUTE = 'compute';\nconst TOUCH_ACTION_AUTO = 'auto';\nconst TOUCH_ACTION_MANIPULATION = 'manipulation'; // not implemented\nconst TOUCH_ACTION_NONE = 'none';\nconst TOUCH_ACTION_PAN_X = 'pan-x';\nconst TOUCH_ACTION_PAN_Y = 'pan-y';\nconst TOUCH_ACTION_MAP = getTouchActionProps();\n\nexport {\n  TOUCH_ACTION_AUTO,\n  TOUCH_ACTION_COMPUTE,\n  TOUCH_ACTION_MANIPULATION,\n  TOUCH_ACTION_NONE,\n  TOUCH_ACTION_PAN_X,\n  TOUCH_ACTION_PAN_Y,\n  TOUCH_ACTION_MAP\n};\n", "import prefixed from '../utils/prefixed';\nimport {window} from \"../browser\";\n\nconst MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;\n\nconst SUPPORT_TOUCH = ('ontouchstart' in window);\nconst SUPPORT_POINTER_EVENTS = prefixed(window, 'PointerEvent') !== undefined;\nconst SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);\n\nconst INPUT_TYPE_TOUCH = 'touch';\nconst INPUT_TYPE_PEN = 'pen';\nconst INPUT_TYPE_MOUSE = 'mouse';\nconst INPUT_TYPE_KINECT = 'kinect';\n\nconst COMPUTE_INTERVAL = 25;\n\nconst INPUT_START = 1;\nconst INPUT_MOVE = 2;\nconst INPUT_END = 4;\nconst INPUT_CANCEL = 8;\n\nconst DIRECTION_NONE = 1;\nconst DIRECTION_LEFT = 2;\nconst DIRECTION_RIGHT = 4;\nconst DIRECTION_UP = 8;\nconst DIRECTION_DOWN = 16;\n\nconst DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;\nconst DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;\nconst DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;\n\nconst PROPS_XY = ['x', 'y'];\nconst PROPS_CLIENT_XY = ['clientX', 'clientY'];\n\nexport {\n    MOBILE_REGEX,\n    SUPPORT_ONLY_TOUCH,\n    SUPPORT_POINTER_EVENTS,\n    SUPPORT_TOUCH,\n    INPUT_TYPE_KINECT,\n    INPUT_TYPE_MOUSE,\n    INPUT_TYPE_PEN,\n    INPUT_TYPE_TOUCH,\n    COMPUTE_INTERVAL,\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_CANCEL,\n    DIRECTION_NONE,\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT,\n    DIRECTION_UP,\n    DIRECTION_DOWN,\n    DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL,\n    DIRECTION_ALL,\n    PROPS_XY,\n    PROPS_CLIENT_XY\n};\n", "/**\n * @private\n * walk objects and arrays\n * @param {Object} obj\n * @param {Function} iterator\n * @param {Object} context\n */\nexport default function each(obj, iterator, context) {\n  let i;\n\n  if (!obj) {\n    return;\n  }\n\n  if (obj.forEach) {\n    obj.forEach(iterator, context);\n  } else if (obj.length !== undefined) {\n    i = 0;\n    while (i < obj.length) {\n      iterator.call(context, obj[i], i, obj);\n      i++;\n    }\n  } else {\n    for (i in obj) {\n      obj.hasOwnProperty(i) && iterator.call(context, obj[i], i, obj);\n    }\n  }\n}\n", "import { TYPE_FUNCTION } from './utils-consts';\n/**\n * @private\n * let a boolean value also be a function that must return a boolean\n * this first item in args will be used as the context\n * @param {Boolean|Function} val\n * @param {Array} [args]\n * @returns {Boolean}\n */\nexport default function boolOrFn(val, args) {\n  if (typeof val === TYPE_FUNCTION) {\n    return val.apply(args ? args[0] || undefined : undefined, args);\n  }\n  return val;\n}\n", "/**\n * @private\n * small indexOf wrapper\n * @param {String} str\n * @param {String} find\n * @returns {Boolean} found\n */\nexport default function inStr(str, find) {\n  return str.indexOf(find) > -1;\n}\n", "import inStr from '../utils/in-str';\nimport {\n    TOUCH_ACTION_NONE,\n    TOUCH_ACTION_PAN_X,\n    TOUCH_ACTION_PAN_Y,\n    TOUCH_ACTION_MANIPULATION,\n    TOUCH_ACTION_AUTO\n} from './touchaction-Consts';\n\n/**\n * @private\n * when the touchActions are collected they are not a valid value, so we need to clean things up. *\n * @param {String} actions\n * @returns {*}\n */\nexport default function cleanTouchActions(actions) {\n  // none\n  if (inStr(actions, TOUCH_ACTION_NONE)) {\n    return TOUCH_ACTION_NONE;\n  }\n\n  let hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);\n  let hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y);\n\n  // if both pan-x and pan-y are set (different recognizers\n  // for different directions, e.g. horizontal pan but vertical swipe?)\n  // we need none (as otherwise with pan-x pan-y combined none of these\n  // recognizers will work, since the browser would handle all panning\n  if (hasPanX && hasPanY) {\n    return TOUCH_ACTION_NONE;\n  }\n\n  // pan-x OR pan-y\n  if (hasPanX || hasPanY) {\n    return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;\n  }\n\n  // manipulation\n  if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {\n    return TOUCH_ACTION_MANIPULATION;\n  }\n\n  return TOUCH_ACTION_AUTO;\n}\n", "import {\n    TOUCH_ACTION_COMPUTE,\n    TOUCH_ACTION_MAP,\n    TOUCH_ACTION_NONE,\n    TOUCH_ACTION_PAN_X,\n    TOUCH_ACTION_PAN_Y\n} from './touchaction-Consts';\nimport {\n  NATIVE_TOUCH_ACTION,\n  PREFIXED_TOUCH_ACTION,\n} from \"./get-touchaction-props\";\nimport {\n    DIRECTION_VERTICAL,\n    DIRECTION_HORIZONTAL\n} from '../inputjs/input-consts';\nimport each from '../utils/each';\nimport boolOrFn from '../utils/bool-or-fn';\nimport inStr from '../utils/in-str';\nimport cleanTouchActions from './clean-touch-actions';\n\n/**\n * @private\n * Touch Action\n * sets the touchAction property or uses the js alternative\n * @param {Manager} manager\n * @param {String} value\n * @constructor\n */\nexport default class TouchAction {\n  constructor(manager, value) {\n    this.manager = manager;\n    this.set(value);\n  }\n\n  /**\n   * @private\n   * set the touchAction value on the element or enable the polyfill\n   * @param {String} value\n   */\n  set(value) {\n    // find out the touch-action by the event handlers\n    if (value === TOUCH_ACTION_COMPUTE) {\n      value = this.compute();\n    }\n\n    if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {\n      this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;\n    }\n    this.actions = value.toLowerCase().trim();\n  }\n\n  /**\n   * @private\n   * just re-set the touchAction value\n   */\n  update() {\n    this.set(this.manager.options.touchAction);\n  }\n\n  /**\n   * @private\n   * compute the value for the touchAction property based on the recognizer's settings\n   * @returns {String} value\n   */\n  compute() {\n    let actions = [];\n    each(this.manager.recognizers, (recognizer) => {\n      if (boolOrFn(recognizer.options.enable, [recognizer])) {\n        actions = actions.concat(recognizer.getTouchAction());\n      }\n    });\n    return cleanTouchActions(actions.join(' '));\n  }\n\n  /**\n   * @private\n   * this method is called on each input cycle and provides the preventing of the browser behavior\n   * @param {Object} input\n   */\n  preventDefaults(input) {\n    let { srcEvent } = input;\n    let direction = input.offsetDirection;\n\n    // if the touch action did prevented once this session\n    if (this.manager.session.prevented) {\n      srcEvent.preventDefault();\n      return;\n    }\n\n    let { actions } = this;\n    let hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];\n    let hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];\n    let hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];\n\n    if (hasNone) {\n      // do not prevent defaults if this is a tap gesture\n      let isTapPointer = input.pointers.length === 1;\n      let isTapMovement = input.distance < 2;\n      let isTapTouchTime = input.deltaTime < 250;\n\n      if (isTapPointer && isTapMovement && isTapTouchTime) {\n        return;\n      }\n    }\n\n    if (hasPanX && hasPanY) {\n      // `pan-x pan-y` means browser handles all scrolling/panning, do not prevent\n      return;\n    }\n\n    if (hasNone ||\n        (hasPanY && direction & DIRECTION_HORIZONTAL) ||\n        (hasPanX && direction & DIRECTION_VERTICAL)) {\n      return this.preventSrc(srcEvent);\n    }\n  }\n\n  /**\n   * @private\n   * call preventDefault to prevent the browser's default behavior (scrolling in most cases)\n   * @param {Object} srcEvent\n   */\n  preventSrc(srcEvent) {\n    this.manager.session.prevented = true;\n    srcEvent.preventDefault();\n  }\n}\n", "/**\n * @private\n * find if a node is in the given parent\n * @method hasParent\n * @param {HTMLElement} node\n * @param {HTMLElement} parent\n * @return {<PERSON>olean} found\n */\nexport default function hasParent(node, parent) {\n  while (node) {\n    if (node === parent) {\n      return true;\n    }\n    node = node.parentNode;\n  }\n  return false;\n}\n", "import { round } from '../utils/utils-consts';\n\n/**\n * @private\n * get the center of all the pointers\n * @param {Array} pointers\n * @return {Object} center contains `x` and `y` properties\n */\nexport default function getCenter(pointers) {\n  let pointersLength = pointers.length;\n\n  // no need to loop when only one touch\n  if (pointersLength === 1) {\n    return {\n      x: round(pointers[0].clientX),\n      y: round(pointers[0].clientY)\n    };\n  }\n\n  let x = 0;\n  let y = 0;\n  let i = 0;\n  while (i < pointersLength) {\n    x += pointers[i].clientX;\n    y += pointers[i].clientY;\n    i++;\n  }\n\n  return {\n    x: round(x / pointersLength),\n    y: round(y / pointersLength)\n  };\n}\n", "import { now,round } from '../utils/utils-consts';\nimport getCenter from './get-center';\n\n/**\n * @private\n * create a simple clone from the input used for storage of firstInput and firstMultiple\n * @param {Object} input\n * @returns {Object} clonedInputData\n */\nexport default function simpleCloneInputData(input) {\n  // make a simple copy of the pointers because we will get a reference if we don't\n  // we only need clientXY for the calculations\n  let pointers = [];\n  let i = 0;\n  while (i < input.pointers.length) {\n    pointers[i] = {\n      clientX: round(input.pointers[i].clientX),\n      clientY: round(input.pointers[i].clientY)\n    };\n    i++;\n  }\n\n  return {\n    timeStamp: now(),\n    pointers,\n    center: getCenter(pointers),\n    deltaX: input.deltaX,\n    deltaY: input.deltaY\n  };\n}\n", "import { PROPS_XY } from './input-consts';\n\n/**\n * @private\n * calculate the absolute distance between two points\n * @param {Object} p1 {x, y}\n * @param {Object} p2 {x, y}\n * @param {Array} [props] containing x and y keys\n * @return {Number} distance\n */\nexport default function getDistance(p1, p2, props) {\n  if (!props) {\n    props = PROPS_XY;\n  }\n  let x = p2[props[0]] - p1[props[0]];\n  let y = p2[props[1]] - p1[props[1]];\n\n  return Math.sqrt((x * x) + (y * y));\n}\n", "import { PROPS_XY } from './input-consts';\n\n/**\n * @private\n * calculate the angle between two coordinates\n * @param {Object} p1\n * @param {Object} p2\n * @param {Array} [props] containing x and y keys\n * @return {Number} angle\n */\nexport default function getAngle(p1, p2, props) {\n  if (!props) {\n    props = PROPS_XY;\n  }\n  let x = p2[props[0]] - p1[props[0]];\n  let y = p2[props[1]] - p1[props[1]];\n  return Math.atan2(y, x) * 180 / Math.PI;\n}\n", "import { abs } from '../utils/utils-consts';\nimport { DIRECTION_NONE,DIRECTION_LEFT,DIRECTION_RIGHT,DIRECTION_UP,DIRECTION_DOWN } from './input-consts';\n\n/**\n * @private\n * get the direction between two points\n * @param {Number} x\n * @param {Number} y\n * @return {Number} direction\n */\nexport default function getDirection(x, y) {\n  if (x === y) {\n    return DIRECTION_NONE;\n  }\n\n  if (abs(x) >= abs(y)) {\n    return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n  }\n  return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n}\n", "import { INPUT_START, INPUT_END } from './input-consts';\n\nexport default function computeDeltaXY(session, input) {\n  let { center } = input;\n  // let { offsetDelta:offset = {}, prevDelta = {}, prevInput = {} } = session;\n  // jscs throwing error on defalut destructured values and without defaults tests fail\n  let offset = session.offsetDelta || {};\n  let prevDelta = session.prevDelta || {};\n  let prevInput = session.prevInput || {};\n\n  if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {\n    prevDelta = session.prevDelta = {\n      x: prevInput.deltaX || 0,\n      y: prevInput.deltaY || 0\n    };\n\n    offset = session.offsetDelta = {\n      x: center.x,\n      y: center.y\n    };\n  }\n\n  input.deltaX = prevDelta.x + (center.x - offset.x);\n  input.deltaY = prevDelta.y + (center.y - offset.y);\n}\n", "/**\n * @private\n * calculate the velocity between two points. unit is in px per ms.\n * @param {Number} deltaTime\n * @param {Number} x\n * @param {Number} y\n * @return {Object} velocity `x` and `y`\n */\nexport default function getVelocity(deltaTime, x, y) {\n  return {\n    x: x / deltaTime || 0,\n    y: y / deltaTime || 0\n  };\n}\n", "import { PROPS_CLIENT_XY } from './input-consts';\nimport getDistance from './get-distance';\n/**\n * @private\n * calculate the scale factor between two pointersets\n * no scale is 1, and goes down to 0 when pinched together, and bigger when pinched out\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} scale\n */\nexport default function getScale(start, end) {\n  return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);\n}\n", "import getAngle from './get-angle';\nimport { PROPS_CLIENT_XY } from './input-consts';\n\n/**\n * @private\n * calculate the rotation degrees between two pointersets\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} rotation\n */\nexport default function getRotation(start, end) {\n  return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);\n}\n", "import { INPUT_CANCEL,COMPUTE_INTERVAL } from './input-consts';\nimport { abs } from '../utils/utils-consts';\nimport getVelocity from './get-velocity';\nimport getDirection from './get-direction';\n\n/**\n * @private\n * velocity is calculated every x ms\n * @param {Object} session\n * @param {Object} input\n */\nexport default function computeIntervalInputData(session, input) {\n  let last = session.lastInterval || input;\n  let deltaTime = input.timeStamp - last.timeStamp;\n  let velocity;\n  let velocityX;\n  let velocityY;\n  let direction;\n\n  if (input.eventType !== INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === undefined)) {\n    let deltaX = input.deltaX - last.deltaX;\n    let deltaY = input.deltaY - last.deltaY;\n\n    let v = getVelocity(deltaTime, deltaX, deltaY);\n    velocityX = v.x;\n    velocityY = v.y;\n    velocity = (abs(v.x) > abs(v.y)) ? v.x : v.y;\n    direction = getDirection(deltaX, deltaY);\n\n    session.lastInterval = input;\n  } else {\n    // use latest velocity info if it doesn't overtake a minimum period\n    velocity = last.velocity;\n    velocityX = last.velocityX;\n    velocityY = last.velocityY;\n    direction = last.direction;\n  }\n\n  input.velocity = velocity;\n  input.velocityX = velocityX;\n  input.velocityY = velocityY;\n  input.direction = direction;\n}\n", "import { now } from '../utils/utils-consts';\nimport { abs } from '../utils/utils-consts';\nimport hasParent from '../utils/has-parent';\nimport simpleCloneInputData from './simple-clone-input-data';\nimport getCenter from './get-center';\nimport getDistance from './get-distance';\nimport getAngle from './get-angle';\nimport getDirection from './get-direction';\nimport computeDeltaXY from './compute-delta-xy';\nimport getVelocity from './get-velocity';\nimport getScale from './get-scale';\nimport getRotation from './get-rotation';\nimport computeIntervalInputData from './compute-interval-input-data';\n\n/**\n* @private\n * extend the data with some usable properties like scale, rotate, velocity etc\n * @param {Object} manager\n * @param {Object} input\n */\nexport default function computeInputData(manager, input) {\n  let { session } = manager;\n  let { pointers } = input;\n  let { length:pointersLength } = pointers;\n\n  // store the first input to calculate the distance and direction\n  if (!session.firstInput) {\n    session.firstInput = simpleCloneInputData(input);\n  }\n\n  // to compute scale and rotation we need to store the multiple touches\n  if (pointersLength > 1 && !session.firstMultiple) {\n    session.firstMultiple = simpleCloneInputData(input);\n  } else if (pointersLength === 1) {\n    session.firstMultiple = false;\n  }\n\n  let { firstInput, firstMultiple } = session;\n  let offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;\n\n  let center = input.center = getCenter(pointers);\n  input.timeStamp = now();\n  input.deltaTime = input.timeStamp - firstInput.timeStamp;\n\n  input.angle = getAngle(offsetCenter, center);\n  input.distance = getDistance(offsetCenter, center);\n\n  computeDeltaXY(session, input);\n  input.offsetDirection = getDirection(input.deltaX, input.deltaY);\n\n  let overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);\n  input.overallVelocityX = overallVelocity.x;\n  input.overallVelocityY = overallVelocity.y;\n  input.overallVelocity = (abs(overallVelocity.x) > abs(overallVelocity.y)) ? overallVelocity.x : overallVelocity.y;\n\n  input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;\n  input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;\n\n  input.maxPointers = !session.prevInput ? input.pointers.length : ((input.pointers.length >\n  session.prevInput.maxPointers) ? input.pointers.length : session.prevInput.maxPointers);\n\n  computeIntervalInputData(session, input);\n\n  // find the correct target\n  let target = manager.element;\n  const srcEvent = input.srcEvent;\n  let srcEventTarget;\n\n  if (srcEvent.composedPath) {\n    srcEventTarget = srcEvent.composedPath()[0];\n  } else if (srcEvent.path) {\n    srcEventTarget = srcEvent.path[0];\n  } else {\n    srcEventTarget = srcEvent.target;\n  }\n\n  if (hasParent(srcEventTarget, target)) {\n    target = srcEventTarget;\n  }\n  input.target = target;\n}\n", "import { INPUT_START,INPUT_END,INPUT_CANCEL } from './input-consts';\nimport computeInputData from './compute-input-data';\n\n/**\n * @private\n * handle input events\n * @param {Manager} manager\n * @param {String} eventType\n * @param {Object} input\n */\nexport default function inputHandler(manager, eventType, input) {\n  let pointersLen = input.pointers.length;\n  let changedPointersLen = input.changedPointers.length;\n  let isFirst = (eventType & INPUT_START && (pointersLen - changedPointersLen === 0));\n  let isFinal = (eventType & (INPUT_END | INPUT_CANCEL) && (pointersLen - changedPointersLen === 0));\n\n  input.isFirst = !!isFirst;\n  input.isFinal = !!isFinal;\n\n  if (isFirst) {\n    manager.session = {};\n  }\n\n  // source event is the normalized value of the domEvents\n  // like 'touchstart, mouseup, pointerdown'\n  input.eventType = eventType;\n\n  // compute scale, rotation etc\n  computeInputData(manager, input);\n\n  // emit secret event\n  manager.emit('hammer.input', input);\n\n  manager.recognize(input);\n  manager.session.prevInput = input;\n}\n", "/**\n * @private\n * split string on whitespace\n * @param {String} str\n * @returns {Array} words\n */\n\nexport default function splitStr(str) {\n  return str.trim().split(/\\s+/g);\n}\n", "import each from './each';\nimport splitStr from './split-str';\n/**\n * @private\n * addEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nexport default function addEventListeners(target, types, handler) {\n  each(splitStr(types), (type) => {\n    target.addEventListener(type, handler, false);\n  });\n}\n", "import each from './each';\nimport splitStr from './split-str';\n/**\n * @private\n * removeEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nexport default function removeEventListeners(target, types, handler) {\n  each(splitStr(types), (type) => {\n    target.removeEventListener(type, handler, false);\n  });\n}\n", "/**\n * @private\n * get the window object of an element\n * @param {HTMLElement} element\n * @returns {DocumentView|Window}\n */\nexport default function getWindowForElement(element) {\n  let doc = element.ownerDocument || element;\n  return (doc.defaultView || doc.parentWindow || window);\n}\n", "import boolOrFn from '../utils/bool-or-fn';\nimport addEventListeners from '../utils/add-event-listeners';\nimport removeEventListeners from '../utils/remove-event-listeners';\nimport getWindowForElement from '../utils/get-window-for-element';\n\n/**\n * @private\n * create new input type manager\n * @param {Manager} manager\n * @param {Function} callback\n * @returns {Input}\n * @constructor\n */\nexport default class Input {\n  constructor(manager, callback) {\n    let self = this;\n    this.manager = manager;\n    this.callback = callback;\n    this.element = manager.element;\n    this.target = manager.options.inputTarget;\n\n    // smaller wrapper around the handler, for the scope and the enabled state of the manager,\n    // so when disabled the input events are completely bypassed.\n    this.domHandler = function(ev) {\n      if (boolOrFn(manager.options.enable, [manager])) {\n        self.handler(ev);\n      }\n    };\n\n    this.init();\n\n  }\n  /**\n   * @private\n   * should handle the inputEvent data and trigger the callback\n   * @virtual\n   */\n  handler() { }\n\n  /**\n   * @private\n   * bind the events\n   */\n  init() {\n    this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);\n    this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);\n    this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n  }\n\n  /**\n   * @private\n   * unbind the events\n   */\n  destroy() {\n    this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);\n    this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);\n    this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n  }\n}\n", "/**\n * @private\n * find if a array contains the object using indexOf or a simple polyFill\n * @param {Array} src\n * @param {String} find\n * @param {String} [findBy<PERSON><PERSON>]\n * @return {Boolean|Number} false when not found, or the index\n */\nexport default function inArray(src, find, findByKey) {\n  if (src.indexOf && !findByKey) {\n    return src.indexOf(find);\n  } else {\n    let i = 0;\n    while (i < src.length) {\n      if ((findByKey && src[i][findByKey] == find) || (!findByKey && src[i] === find)) {// do not use === here, test fails\n        return i;\n      }\n      i++;\n    }\n    return -1;\n  }\n}\n", "import {\n    INPUT_START,\n    INPUT_END,\n    INPUT_CANCEL,\n    INPUT_MOVE,\n    INPUT_TYPE_TOUCH,\n    INPUT_TYPE_MOUSE,\n    INPUT_TYPE_PEN,\n    INPUT_TYPE_KINECT\n} from '../inputjs/input-consts';\nimport {window} from \"../browser\";\nimport Input from '../inputjs/input-constructor';\nimport inArray from '../utils/in-array';\n\nconst POINTER_INPUT_MAP = {\n  pointerdown: INPUT_START,\n  pointermove: INPUT_MOVE,\n  pointerup: INPUT_END,\n  pointercancel: INPUT_CANCEL,\n  pointerout: INPUT_CANCEL\n};\n\n// in IE10 the pointer types is defined as an enum\nconst IE10_POINTER_TYPE_ENUM = {\n  2: INPUT_TYPE_TOUCH,\n  3: INPUT_TYPE_PEN,\n  4: INPUT_TYPE_MOUSE,\n  5: INPUT_TYPE_KINECT // see https://twitter.com/jacobrossi/status/480596438489890816\n};\n\nlet POINTER_ELEMENT_EVENTS = 'pointerdown';\nlet POINTER_WINDOW_EVENTS = 'pointermove pointerup pointercancel';\n\n// IE10 has prefixed support, and case-sensitive\nif (window.MSPointerEvent && !window.PointerEvent) {\n  POINTER_ELEMENT_EVENTS = 'MSPointerDown';\n  POINTER_WINDOW_EVENTS = 'MSPointerMove MSPointerUp MSPointerCancel';\n}\n\n/**\n * @private\n * Pointer events input\n * @constructor\n * @extends Input\n */\nexport default class PointerEventInput extends Input {\n  constructor() {\n    var proto = PointerEventInput.prototype;\n\n    proto.evEl = POINTER_ELEMENT_EVENTS;\n    proto.evWin = POINTER_WINDOW_EVENTS;\n    super(...arguments);\n    this.store = (this.manager.session.pointerEvents = []);\n  }\n\n  /**\n   * @private\n   * handle mouse events\n   * @param {Object} ev\n   */\n  handler(ev) {\n    let { store } = this;\n    let removePointer = false;\n\n    let eventTypeNormalized = ev.type.toLowerCase().replace('ms', '');\n    let eventType = POINTER_INPUT_MAP[eventTypeNormalized];\n    let pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;\n\n    let isTouch = (pointerType === INPUT_TYPE_TOUCH);\n\n    // get index of the event in the store\n    let storeIndex = inArray(store, ev.pointerId, 'pointerId');\n\n    // start and mouse must be down\n    if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {\n      if (storeIndex < 0) {\n        store.push(ev);\n        storeIndex = store.length - 1;\n      }\n    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n      removePointer = true;\n    }\n\n    // it not found, so the pointer hasn't been down (so it's probably a hover)\n    if (storeIndex < 0) {\n      return;\n    }\n\n    // update the event in the store\n    store[storeIndex] = ev;\n\n    this.callback(this.manager, eventType, {\n      pointers: store,\n      changedPointers: [ev],\n      pointerType,\n      srcEvent: ev\n    });\n\n    if (removePointer) {\n      // remove from the store\n      store.splice(storeIndex, 1);\n    }\n  }\n}\n", "/**\n * @private\n * convert array-like objects to real arrays\n * @param {Object} obj\n * @returns {Array}\n */\nexport default function toArray(obj) {\n  return Array.prototype.slice.call(obj, 0);\n}\n", "import inArray from './in-array';\n\n/**\n * @private\n * unique array with objects based on a key (like 'id') or just by the array's value\n * @param {Array} src [{id:1},{id:2},{id:1}]\n * @param {String} [key]\n * @param {Boolean} [sort=False]\n * @returns {Array} [{id:1},{id:2}]\n */\nexport default function uniqueArray(src, key, sort) {\n  let results = [];\n  let values = [];\n  let i = 0;\n\n  while (i < src.length) {\n    let val = key ? src[i][key] : src[i];\n    if (inArray(values, val) < 0) {\n      results.push(src[i]);\n    }\n    values[i] = val;\n    i++;\n  }\n\n  if (sort) {\n    if (!key) {\n      results = results.sort();\n    } else {\n      results = results.sort((a, b) => {\n        return a[key] > b[key];\n      });\n    }\n  }\n\n  return results;\n}\n", "import {\n  INPUT_START,\n  INPUT_MOVE,\n  INPUT_END,\n  INPUT_CANCEL,\n  INPUT_TYPE_TOUCH\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\nimport toArray from '../utils/to-array';\nimport hasParent from '../utils/has-parent';\nimport uniqueArray from '../utils/unique-array';\n\nconst TOUCH_INPUT_MAP = {\n  touchstart: INPUT_START,\n  touchmove: INPUT_MOVE,\n  touchend: INPUT_END,\n  touchcancel: INPUT_CANCEL\n};\n\nconst TOUCH_TARGET_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * @private\n * Multi-user touch events input\n * @constructor\n * @extends Input\n */\nexport default class TouchInput extends Input {\n  constructor() {\n    TouchInput.prototype.evTarget = TOUCH_TARGET_EVENTS;\n    super(...arguments);\n    this.targetIds = {};\n    // this.evTarget = TOUCH_TARGET_EVENTS;\n  }\n  handler(ev) {\n    let type = TOUCH_INPUT_MAP[ev.type];\n    let touches = getTouches.call(this, ev, type);\n    if (!touches) {\n      return;\n    }\n\n    this.callback(this.manager, type, {\n      pointers: touches[0],\n      changedPointers: touches[1],\n      pointerType: INPUT_TYPE_TOUCH,\n      srcEvent: ev\n    });\n  }\n}\n\n/**\n * @private\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction getTouches(ev, type) {\n  let allTouches = toArray(ev.touches);\n  let { targetIds } = this;\n\n  // when there is only one touch, the process can be simplified\n  if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {\n    targetIds[allTouches[0].identifier] = true;\n    return [allTouches, allTouches];\n  }\n\n  let i;\n  let targetTouches;\n  let changedTouches = toArray(ev.changedTouches);\n  let changedTargetTouches = [];\n  let { target } = this;\n\n  // get target touches from touches\n  targetTouches = allTouches.filter((touch) => {\n    return hasParent(touch.target, target);\n  });\n\n  // collect touches\n  if (type === INPUT_START) {\n    i = 0;\n    while (i < targetTouches.length) {\n      targetIds[targetTouches[i].identifier] = true;\n      i++;\n    }\n  }\n\n  // filter changed touches to only contain touches that exist in the collected target ids\n  i = 0;\n  while (i < changedTouches.length) {\n    if (targetIds[changedTouches[i].identifier]) {\n      changedTargetTouches.push(changedTouches[i]);\n    }\n\n    // cleanup removed touches\n    if (type & (INPUT_END | INPUT_CANCEL)) {\n      delete targetIds[changedTouches[i].identifier];\n    }\n    i++;\n  }\n\n  if (!changedTargetTouches.length) {\n    return;\n  }\n\n  return [\n    // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'\n    uniqueArray(targetTouches.concat(changedTargetTouches), 'identifier', true),\n    changedTargetTouches\n  ];\n}\n", "import {\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_TYPE_MOUSE\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\n\nconst MOUSE_INPUT_MAP = {\n  mousedown: INPUT_START,\n  mousemove: INPUT_MOVE,\n  mouseup: INPUT_END\n};\n\nconst MOUSE_ELEMENT_EVENTS = 'mousedown';\nconst MOUSE_WINDOW_EVENTS = 'mousemove mouseup';\n\n/**\n * @private\n * Mouse events input\n * @constructor\n * @extends Input\n */\nexport default class MouseInput extends Input {\n  constructor() {\n    var proto = MouseInput.prototype;\n    proto.evEl = MOUSE_ELEMENT_EVENTS;\n    proto.evWin = MOUSE_WINDOW_EVENTS;\n\n    super(...arguments);\n    this.pressed = false; // mousedown state\n  }\n\n  /**\n   * @private\n   * handle mouse events\n   * @param {Object} ev\n   */\n  handler(ev) {\n    let eventType = MOUSE_INPUT_MAP[ev.type];\n\n    // on start we want to have the left mouse button down\n    if (eventType & INPUT_START && ev.button === 0) {\n      this.pressed = true;\n    }\n\n    if (eventType & INPUT_MOVE && ev.which !== 1) {\n      eventType = INPUT_END;\n    }\n\n    // mouse must be down\n    if (!this.pressed) {\n      return;\n    }\n\n    if (eventType & INPUT_END) {\n      this.pressed = false;\n    }\n\n    this.callback(this.manager, eventType, {\n      pointers: [ev],\n      changedPointers: [ev],\n      pointerType: INPUT_TYPE_MOUSE,\n      srcEvent: ev\n    });\n  }\n}\n", "import Input from \"../inputjs/input-constructor\";\nimport TouchInput from \"./touch\";\nimport MouseInput from \"./mouse\";\nimport {\n\tINPUT_START,\n\tINPUT_END,\n\tINPUT_CANCEL,\n\tINPUT_TYPE_TOUCH,\n\tINPUT_TYPE_MOUSE,\n} from \"../inputjs/input-consts\";\n\n/**\n * @private\n * Combined touch and mouse input\n *\n * Touch has a higher priority then mouse, and while touching no mouse events are allowed.\n * This because touch devices also emit mouse events while doing a touch.\n *\n * @constructor\n * @extends Input\n */\n\nconst DEDUP_TIMEOUT = 2500;\nconst DEDUP_DISTANCE = 25;\n\nfunction setLastTouch(eventData) {\n\tconst { changedPointers: [touch] } = eventData;\n\n\tif (touch.identifier === this.primaryTouch) {\n\t\tconst lastTouch = { x: touch.clientX, y: touch.clientY };\n\t\tconst lts = this.lastTouches;\n\n\t\tthis.lastTouches.push(lastTouch);\n\n\n\t\tconst removeLastTouch = function() {\n\t\t\tconst i = lts.indexOf(lastTouch);\n\n\t\t\tif (i > -1) {\n\t\t\t\tlts.splice(i, 1);\n\t\t\t}\n\t\t};\n\n\t\tsetTimeout(removeLastTouch, DEDUP_TIMEOUT);\n\t}\n}\n\n\nfunction recordTouches(eventType, eventData) {\n\tif (eventType & INPUT_START) {\n\t\tthis.primaryTouch = eventData.changedPointers[0].identifier;\n\t\tsetLastTouch.call(this, eventData);\n\t} else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n\t\tsetLastTouch.call(this, eventData);\n\t}\n}\nfunction isSyntheticEvent(eventData) {\n\tconst x = eventData.srcEvent.clientX;\n\tconst y = eventData.srcEvent.clientY;\n\n\tfor (let i = 0; i < this.lastTouches.length; i++) {\n\t\tconst t = this.lastTouches[i];\n\t\tconst dx = Math.abs(x - t.x);\n\t\tconst dy = Math.abs(y - t.y);\n\n\t\tif (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {\n\t\t\treturn true;\n\t\t}\n\t}\n\treturn false;\n}\n\n\nexport default class TouchMouseInput extends Input {\n\tconstructor(manager, callback) {\n\t\tsuper(manager, callback);\n\n\t\tthis.touch = new TouchInput(this.manager, this.handler);\n\t\tthis.mouse = new MouseInput(this.manager, this.handler);\n\t\tthis.primaryTouch = null;\n\t\tthis.lastTouches = [];\n\t}\n\n\t/**\n\t * @private\n\t * handle mouse and touch events\n\t * @param {Hammer} manager\n\t * @param {String} inputEvent\n\t * @param {Object} inputData\n\t */\n\thandler = (manager, inputEvent, inputData) => {\n\t\tconst isTouch = (inputData.pointerType === INPUT_TYPE_TOUCH);\n\t\tconst isMouse = (inputData.pointerType === INPUT_TYPE_MOUSE);\n\n\t\tif (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {\n\t\t\treturn;\n\t\t}\n\n\t\t// when we're in a touch event, record touches to  de-dupe synthetic mouse event\n\t\tif (isTouch) {\n\t\t\trecordTouches.call(this, inputEvent, inputData);\n\t\t} else if (isMouse && isSyntheticEvent.call(this, inputData)) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.callback(manager, inputEvent, inputData);\n\t}\n\n\t/**\n\t * @private\n\t * remove the event listeners\n\t */\n\tdestroy() {\n\t\tthis.touch.destroy();\n\t\tthis.mouse.destroy();\n\t}\n}\n", "import { SUPPORT_POINTER_EVENTS,SUPPORT_ONLY_TOUCH,SUPPORT_TOUCH } from './input-consts';\nimport inputHandler from './input-handler';\nimport PointerEventInput from '../input/pointerevent';\nimport TouchInput from '../input/touch';\nimport MouseInput from '../input/mouse';\nimport TouchMouseInput from '../input/touchmouse';\n\n/**\n * @private\n * create new input type manager\n * called by the Manager constructor\n * @param {Hammer} manager\n * @returns {Input}\n */\nexport default function createInputInstance(manager) {\n  let Type;\n  // let inputClass = manager.options.inputClass;\n  let { options:{ inputClass } } = manager;\n  if (inputClass) {\n    Type = inputClass;\n  } else if (SUPPORT_POINTER_EVENTS) {\n    Type = PointerEventInput;\n  } else if (SUPPORT_ONLY_TOUCH) {\n    Type = TouchInput;\n  } else if (!SUPPORT_TOUCH) {\n    Type = MouseInput;\n  } else {\n    Type = TouchMouseInput;\n  }\n  return new (Type)(manager, inputHandler);\n}\n", "import each from './each';\n/**\n * @private\n * if the argument is an array, we want to execute the fn on each entry\n * if it aint an array we don't want to do a thing.\n * this is used by all the methods that accept a single and array argument.\n * @param {*|Array} arg\n * @param {String} fn\n * @param {Object} [context]\n * @returns {Boolean}\n */\nexport default function invokeArrayArg(arg, fn, context) {\n  if (Array.isArray(arg)) {\n    each(arg, context[fn], context);\n    return true;\n  }\n  return false;\n}\n", "const STATE_POSSIBLE = 1;\nconst STATE_BEGAN = 2;\nconst STATE_CHANGED = 4;\nconst STATE_ENDED = 8;\nconst STATE_RECOGNIZED = STATE_ENDED;\nconst STATE_CANCELLED = 16;\nconst STATE_FAILED = 32;\n\nexport {\n    STATE_POSSIBLE,\n    STATE_BEGAN,\n    STATE_CHANGED,\n    STATE_ENDED,\n    STATE_RECOGNIZED,\n    STATE_CANCELLED,\n    STATE_FAILED\n};\n", "/**\n * @private\n * get a unique id\n * @returns {number} uniqueId\n */\nlet _uniqueId = 1;\nexport default function uniqueId() {\n  return _uniqueId++;\n}\n", "/**\n * @private\n * get a recognizer by name if it is bound to a manager\n * @param {Recognizer|String} otherRecognizer\n * @param {Recognizer} recognizer\n * @returns {Recognizer}\n */\nexport default function getRecognizerByNameIfManager(otherRecognizer, recognizer) {\n  let { manager } = recognizer;\n  if (manager) {\n    return manager.get(otherRecognizer);\n  }\n  return otherRecognizer;\n}\n", "import {\n    STATE_CANCELLED,\n    STATE_ENDED,\n    STATE_CHANGED,\n    STATE_BEGAN\n} from './recognizer-consts';\n\n/**\n * @private\n * get a usable string, used as event postfix\n * @param {constant} state\n * @returns {String} state\n */\nexport default function stateStr(state) {\n  if (state & STATE_CANCELLED) {\n    return 'cancel';\n  } else if (state & STATE_ENDED) {\n    return 'end';\n  } else if (state & STATE_CHANGED) {\n    return 'move';\n  } else if (state & STATE_BEGAN) {\n    return 'start';\n  }\n  return '';\n}\n", "import {\n    STATE_POSSIBLE,\n    STATE_ENDED,\n    STATE_FAILED,\n    STATE_RECOGNIZED,\n    STATE_CANCELLED,\n    STATE_BEGAN,\n    STATE_CHANGED\n} from './recognizer-consts';\nimport assign from '../utils/assign';\nimport uniqueId from '../utils/unique-id';\nimport invokeArrayArg from '../utils/invoke-array-arg';\nimport inArray from '../utils/in-array';\nimport boolOrFn from '../utils/bool-or-fn';\nimport getRecognizerByNameIfManager from './get-recognizer-by-name-if-manager';\nimport stateStr from './state-str';\n\n/**\n * @private\n * Recognizer flow explained; *\n * All recognizers have the initial state of POSSIBLE when a input session starts.\n * The definition of a input session is from the first input until the last input, with all it's movement in it. *\n * Example session for mouse-input: mousedown -> mousemove -> mouseup\n *\n * On each recognizing cycle (see Manager.recognize) the .recognize() method is executed\n * which determines with state it should be.\n *\n * If the recognizer has the state FAILED, <PERSON>NC<PERSON>LED or RECOGNIZED (equals ENDED), it is reset to\n * POSSIBLE to give it another change on the next cycle.\n *\n *               Possible\n *                  |\n *            +-----+---------------+\n *            |                     |\n *      +-----+-----+               |\n *      |           |               |\n *   Failed      Cancelled          |\n *                          +-------+------+\n *                          |              |\n *                      Recognized       Began\n *                                         |\n *                                      Changed\n *                                         |\n *                                  Ended/Recognized\n */\n\n/**\n * @private\n * Recognizer\n * Every recognizer needs to extend from this class.\n * @constructor\n * @param {Object} options\n */\nexport default class Recognizer {\n  constructor(options = {}) {\n    this.options = {\n      enable: true,\n      ...options,\n    };\n\n    this.id = uniqueId();\n\n    this.manager = null;\n\n    // default is enable true\n    this.state = STATE_POSSIBLE;\n    this.simultaneous = {};\n    this.requireFail = [];\n  }\n\n  /**\n   * @private\n   * set options\n   * @param {Object} options\n   * @return {Recognizer}\n   */\n  set(options) {\n    assign(this.options, options);\n\n    // also update the touchAction, in case something changed about the directions/enabled state\n    this.manager && this.manager.touchAction.update();\n    return this;\n  }\n\n  /**\n   * @private\n   * recognize simultaneous with an other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  recognizeWith(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'recognizeWith', this)) {\n      return this;\n    }\n\n    let { simultaneous } = this;\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    if (!simultaneous[otherRecognizer.id]) {\n      simultaneous[otherRecognizer.id] = otherRecognizer;\n      otherRecognizer.recognizeWith(this);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * drop the simultaneous link. it doesnt remove the link on the other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  dropRecognizeWith(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'dropRecognizeWith', this)) {\n      return this;\n    }\n\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    delete this.simultaneous[otherRecognizer.id];\n    return this;\n  }\n\n  /**\n   * @private\n   * recognizer can only run when an other is failing\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  requireFailure(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'requireFailure', this)) {\n      return this;\n    }\n\n    let { requireFail } = this;\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    if (inArray(requireFail, otherRecognizer) === -1) {\n      requireFail.push(otherRecognizer);\n      otherRecognizer.requireFailure(this);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * drop the requireFailure link. it does not remove the link on the other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  dropRequireFailure(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'dropRequireFailure', this)) {\n      return this;\n    }\n\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    let index = inArray(this.requireFail, otherRecognizer);\n    if (index > -1) {\n      this.requireFail.splice(index, 1);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * has require failures boolean\n   * @returns {boolean}\n   */\n  hasRequireFailures() {\n    return this.requireFail.length > 0;\n  }\n\n  /**\n   * @private\n   * if the recognizer can recognize simultaneous with an other recognizer\n   * @param {Recognizer} otherRecognizer\n   * @returns {Boolean}\n   */\n  canRecognizeWith(otherRecognizer) {\n    return !!this.simultaneous[otherRecognizer.id];\n  }\n\n  /**\n   * @private\n   * You should use `tryEmit` instead of `emit` directly to check\n   * that all the needed recognizers has failed before emitting.\n   * @param {Object} input\n   */\n  emit(input) {\n    let self = this;\n    let { state } = this;\n\n    function emit(event) {\n      self.manager.emit(event, input);\n    }\n\n    // 'panstart' and 'panmove'\n    if (state < STATE_ENDED) {\n      emit(self.options.event + stateStr(state));\n    }\n\n    emit(self.options.event); // simple 'eventName' events\n\n    if (input.additionalEvent) { // additional event(panleft, panright, pinchin, pinchout...)\n      emit(input.additionalEvent);\n    }\n\n    // panend and pancancel\n    if (state >= STATE_ENDED) {\n      emit(self.options.event + stateStr(state));\n    }\n  }\n\n  /**\n   * @private\n   * Check that all the require failure recognizers has failed,\n   * if true, it emits a gesture event,\n   * otherwise, setup the state to FAILED.\n   * @param {Object} input\n   */\n  tryEmit(input) {\n    if (this.canEmit()) {\n      return this.emit(input);\n    }\n    // it's failing anyway\n    this.state = STATE_FAILED;\n  }\n\n  /**\n   * @private\n   * can we emit?\n   * @returns {boolean}\n   */\n  canEmit() {\n    let i = 0;\n    while (i < this.requireFail.length) {\n      if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {\n        return false;\n      }\n      i++;\n    }\n    return true;\n  }\n\n  /**\n   * @private\n   * update the recognizer\n   * @param {Object} inputData\n   */\n  recognize(inputData) {\n    // make a new copy of the inputData\n    // so we can change the inputData without messing up the other recognizers\n    let inputDataClone = assign({}, inputData);\n\n    // is is enabled and allow recognizing?\n    if (!boolOrFn(this.options.enable, [this, inputDataClone])) {\n      this.reset();\n      this.state = STATE_FAILED;\n      return;\n    }\n\n    // reset when we've reached the end\n    if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {\n      this.state = STATE_POSSIBLE;\n    }\n\n    this.state = this.process(inputDataClone);\n\n    // the recognizer has recognized a gesture\n    // so trigger an event\n    if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {\n      this.tryEmit(inputDataClone);\n    }\n  }\n\n  /**\n   * @private\n   * return the state of the recognizer\n   * the actual recognizing happens in this method\n   * @virtual\n   * @param {Object} inputData\n   * @returns {constant} STATE\n   */\n\n  /* jshint ignore:start */\n  process(inputData) { }\n  /* jshint ignore:end */\n\n  /**\n   * @private\n   * return the preferred touch-action\n   * @virtual\n   * @returns {Array}\n   */\n  getTouchAction() { }\n\n  /**\n   * @private\n   * called when the gesture isn't allowed to recognize\n   * like when another is being recognized or it is disabled\n   * @virtual\n   */\n  reset() { }\n}", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport { TOUCH_ACTION_MANIPULATION } from '../touchactionjs/touchaction-Consts';\nimport {INPUT_START,INPUT_END } from '../inputjs/input-consts';\nimport {\n    STATE_RECOGNIZED,\n    STATE_BEGAN,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport getDistance from '../inputjs/get-distance';\n\n/**\n * @private\n * A tap is recognized when the pointer is doing a small tap/click. Multiple taps are recognized if they occur\n * between the given interval and position. The delay option can be used to recognize multi-taps without firing\n * a single tap.\n *\n * The eventData from the emitted event contains the property `tapCount`, which contains the amount of\n * multi-taps being recognized.\n * @constructor\n * @extends Recognizer\n */\nexport default class TapRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      event: 'tap',\n      pointers: 1,\n      taps: 1,\n      interval: 300, // max time between the multi-tap taps\n      time: 250, // max time of the pointer to be down (like finger on the screen)\n      threshold: 9, // a minimal movement is ok, but keep it low\n      posThreshold: 10, // a multi-tap can be a bit off the initial position\n      ...options,\n    });\n\n    // previous time and center,\n    // used for tap counting\n    this.pTime = false;\n    this.pCenter = false;\n\n    this._timer = null;\n    this._input = null;\n    this.count = 0;\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_MANIPULATION];\n  }\n\n  process(input) {\n    let { options } = this;\n\n    let validPointers = input.pointers.length === options.pointers;\n    let validMovement = input.distance < options.threshold;\n    let validTouchTime = input.deltaTime < options.time;\n\n    this.reset();\n\n    if ((input.eventType & INPUT_START) && (this.count === 0)) {\n      return this.failTimeout();\n    }\n\n    // we only allow little movement\n    // and we've reached an end event, so a tap is possible\n    if (validMovement && validTouchTime && validPointers) {\n      if (input.eventType !== INPUT_END) {\n        return this.failTimeout();\n      }\n\n      let validInterval = this.pTime ? (input.timeStamp - this.pTime < options.interval) : true;\n      let validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;\n\n      this.pTime = input.timeStamp;\n      this.pCenter = input.center;\n\n      if (!validMultiTap || !validInterval) {\n        this.count = 1;\n      } else {\n        this.count += 1;\n      }\n\n      this._input = input;\n\n      // if tap count matches we have recognized it,\n      // else it has began recognizing...\n      let tapCount = this.count % options.taps;\n      if (tapCount === 0) {\n        // no failing requirements, immediately trigger the tap event\n        // or wait as long as the multitap interval to trigger\n        if (!this.hasRequireFailures()) {\n          return STATE_RECOGNIZED;\n        } else {\n          this._timer = setTimeout(() => {\n            this.state = STATE_RECOGNIZED;\n            this.tryEmit();\n          }, options.interval);\n          return STATE_BEGAN;\n        }\n      }\n    }\n    return STATE_FAILED;\n  }\n\n  failTimeout() {\n    this._timer = setTimeout(() => {\n      this.state = STATE_FAILED;\n    }, this.options.interval);\n    return STATE_FAILED;\n  }\n\n  reset() {\n    clearTimeout(this._timer);\n  }\n\n  emit() {\n    if (this.state === STATE_RECOGNIZED) {\n      this._input.tapCount = this.count;\n      this.manager.emit(this.options.event, this._input);\n    }\n  }\n}\n", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport {\n    STATE_BEGAN,\n    STATE_CHANGED,\n    STATE_CANCELLED,\n    STATE_ENDED,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport {\n    INPUT_CANCEL,\n    INPUT_END\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * This recognizer is just used as a base for the simple attribute recognizers.\n * @constructor\n * @extends Recognizer\n */\nexport default class AttrRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      pointers: 1,\n      ...options,\n    });\n  }\n\n  /**\n   * @private\n   * Used to check if it the recognizer receives valid input, like input.distance > 10.\n   * @memberof AttrRecognizer\n   * @param {Object} input\n   * @returns {Boolean} recognized\n   */\n  attrTest(input) {\n    let optionPointers = this.options.pointers;\n    return optionPointers === 0 || input.pointers.length === optionPointers;\n  }\n\n  /**\n   * @private\n   * Process the input and return the state for the recognizer\n   * @memberof AttrRecognizer\n   * @param {Object} input\n   * @returns {*} State\n   */\n  process(input) {\n    let { state } = this;\n    let { eventType } = input;\n\n    let isRecognized = state & (STATE_BEGAN | STATE_CHANGED);\n    let isValid = this.attrTest(input);\n\n    // on cancel input and we've recognized before, return STATE_CANCELLED\n    if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {\n      return state | STATE_CANCELLED;\n    } else if (isRecognized || isValid) {\n      if (eventType & INPUT_END) {\n        return state | STATE_ENDED;\n      } else if (!(state & STATE_BEGAN)) {\n        return STATE_BEGAN;\n      }\n      return state | STATE_CHANGED;\n    }\n    return STATE_FAILED;\n  }\n}\n", "import {\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT,\n    DIRECTION_UP,\n    DIRECTION_DOWN\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * direction cons to string\n * @param {constant} direction\n * @returns {String}\n */\nexport default function directionStr(direction) {\n  if (direction === DIRECTION_DOWN) {\n    return 'down';\n  } else if (direction === DIRECTION_UP) {\n    return 'up';\n  } else if (direction === DIRECTION_LEFT) {\n    return 'left';\n  } else if (direction === DIRECTION_RIGHT) {\n    return 'right';\n  }\n  return '';\n}\n", "import  AttrR<PERSON>ognizer from './attribute';\nimport {\n    DIRECTION_ALL,\n    DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL,\n    DIRECTION_NONE,\n    DIRECTION_UP,\n    DIRECTION_DOWN,\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT\n} from '../inputjs/input-consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\nimport { TOUCH_ACTION_PAN_X,TOUCH_ACTION_PAN_Y } from '../touchactionjs/touchaction-Consts';\nimport directionStr from '../recognizerjs/direction-str';\n\n/**\n * @private\n * Pan\n * Recognized when the pointer is down and moved in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class PanRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'pan',\n      threshold: 10,\n      pointers: 1,\n      direction: DIRECTION_ALL,\n      ...options,\n    });\n    this.pX = null;\n    this.pY = null;\n  }\n\n  getTouchAction() {\n    let { options:{ direction } } = this;\n    let actions = [];\n    if (direction & DIRECTION_HORIZONTAL) {\n      actions.push(TOUCH_ACTION_PAN_Y);\n    }\n    if (direction & DIRECTION_VERTICAL) {\n      actions.push(TOUCH_ACTION_PAN_X);\n    }\n    return actions;\n  }\n\n  directionTest(input) {\n    let { options } = this;\n    let hasMoved = true;\n    let { distance } = input;\n    let { direction } = input;\n    let x = input.deltaX;\n    let y = input.deltaY;\n\n    // lock to axis?\n    if (!(direction & options.direction)) {\n      if (options.direction & DIRECTION_HORIZONTAL) {\n        direction = (x === 0) ? DIRECTION_NONE : (x < 0) ? DIRECTION_LEFT : DIRECTION_RIGHT;\n        hasMoved = x !== this.pX;\n        distance = Math.abs(input.deltaX);\n      } else {\n        direction = (y === 0) ? DIRECTION_NONE : (y < 0) ? DIRECTION_UP : DIRECTION_DOWN;\n        hasMoved = y !== this.pY;\n        distance = Math.abs(input.deltaY);\n      }\n    }\n    input.direction = direction;\n    return hasMoved && distance > options.threshold && direction & options.direction;\n  }\n\n  attrTest(input) {\n    return AttrRecognizer.prototype.attrTest.call(this, input) && // replace with a super call\n        (this.state & STATE_BEGAN || (!(this.state & STATE_BEGAN) && this.directionTest(input)));\n  }\n\n  emit(input) {\n\n    this.pX = input.deltaX;\n    this.pY = input.deltaY;\n\n    let direction = directionStr(input.direction);\n\n    if (direction) {\n      input.additionalEvent = this.options.event + direction;\n    }\n    super.emit(input);\n  }\n}\n", "import AttrRecognizer from '../recognizers/attribute';\nimport { abs } from '../utils/utils-consts';\nimport { DIRECTION_HORIZONTAL,DIRECTION_VERTICAL } from '../inputjs/input-consts';\nimport PanRecognizer from './pan';\nimport { INPUT_END } from '../inputjs/input-consts';\nimport directionStr from '../recognizerjs/direction-str';\n\n/**\n * @private\n * Swipe\n * Recognized when the pointer is moving fast (velocity), with enough distance in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class SwipeRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'swipe',\n      threshold: 10,\n      velocity: 0.3,\n      direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,\n      pointers: 1,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return PanRecognizer.prototype.getTouchAction.call(this);\n  }\n\n  attrTest(input) {\n    let { direction } = this.options;\n    let velocity;\n\n    if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {\n      velocity = input.overallVelocity;\n    } else if (direction & DIRECTION_HORIZONTAL) {\n      velocity = input.overallVelocityX;\n    } else if (direction & DIRECTION_VERTICAL) {\n      velocity = input.overallVelocityY;\n    }\n\n    return super.attrTest(input) &&\n        direction & input.offsetDirection &&\n        input.distance > this.options.threshold &&\n        input.maxPointers === this.options.pointers &&\n        abs(velocity) > this.options.velocity && input.eventType & INPUT_END;\n  }\n\n  emit(input) {\n    let direction = directionStr(input.offsetDirection);\n    if (direction) {\n      this.manager.emit(this.options.event + direction, input);\n    }\n\n    this.manager.emit(this.options.event, input);\n  }\n}\n", "import AttrRecognizer from './attribute';\nimport { TOUCH_ACTION_NONE } from '../touchactionjs/touchaction-Consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\n\n/**\n * @private\n * Pinch\n * Recognized when two or more pointers are moving toward (zoom-in) or away from each other (zoom-out).\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class PinchRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'pinch',\n      threshold: 0,\n      pointers: 2,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_NONE];\n  }\n\n  attrTest(input) {\n    return super.attrTest(input) &&\n        (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);\n  }\n\n  emit(input) {\n    if (input.scale !== 1) {\n      let inOut = input.scale < 1 ? 'in' : 'out';\n      input.additionalEvent = this.options.event + inOut;\n    }\n    super.emit(input);\n  }\n}\n", "import AttrRecognizer from './attribute';\nimport { TOUCH_ACTION_NONE } from '../touchactionjs/touchaction-Consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\n\n/**\n * @private\n * Rotate\n * Recognized when two or more pointer are moving in a circular motion.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class RotateRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super( {\n      event: 'rotate',\n      threshold: 0,\n      pointers: 2,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_NONE];\n  }\n\n  attrTest(input) {\n    return super.attrTest(input) &&\n        (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);\n  }\n}", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport {\n    STATE_RECOGNIZED,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport { now } from '../utils/utils-consts';\nimport { TOUCH_ACTION_AUTO } from '../touchactionjs/touchaction-Consts';\nimport {\n    INPUT_START,\n    INPUT_END,\n    INPUT_CANCEL\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * Press\n * Recognized when the pointer is down for x ms without any movement.\n * @constructor\n * @extends Recognizer\n */\nexport default class PressRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      event: 'press',\n      pointers: 1,\n      time: 251, // minimal time of the pointer to be pressed\n      threshold: 9, // a minimal movement is ok, but keep it low\n      ...options,\n    });\n    this._timer = null;\n    this._input = null;\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_AUTO];\n  }\n\n  process(input) {\n    let { options } = this;\n    let validPointers = input.pointers.length === options.pointers;\n    let validMovement = input.distance < options.threshold;\n    let validTime = input.deltaTime > options.time;\n\n    this._input = input;\n\n    // we only allow little movement\n    // and we've reached an end event, so a tap is possible\n    if (!validMovement || !validPointers || (input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime)) {\n      this.reset();\n    } else if (input.eventType & INPUT_START) {\n      this.reset();\n      this._timer = setTimeout(() => {\n        this.state = STATE_RECOGNIZED;\n        this.tryEmit();\n      }, options.time);\n    } else if (input.eventType & INPUT_END) {\n      return STATE_RECOGNIZED;\n    }\n    return STATE_FAILED;\n  }\n\n  reset() {\n    clearTimeout(this._timer);\n  }\n\n  emit(input) {\n    if (this.state !== STATE_RECOGNIZED) {\n      return;\n    }\n\n    if (input && (input.eventType & INPUT_END)) {\n      this.manager.emit(`${this.options.event}up`, input);\n    } else {\n      this._input.timeStamp = now();\n      this.manager.emit(this.options.event, this._input);\n    }\n  }\n}\n\n", "import { TOUCH_ACTION_COMPUTE } from \"./touchactionjs/touchaction-Consts\";\nimport <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from \"./recognizers/tap\";\nimport PanRecognizer from \"./recognizers/pan\";\nimport <PERSON>wipe<PERSON><PERSON>ognizer from \"./recognizers/swipe\";\nimport <PERSON>nch<PERSON><PERSON>ognizer from \"./recognizers/pinch\";\nimport <PERSON><PERSON><PERSON><PERSON><PERSON>ognizer from \"./recognizers/rotate\";\nimport <PERSON><PERSON><PERSON>ognizer from \"./recognizers/press\";\nimport {DIRECTION_HORIZONTAL} from \"./inputjs/input-consts\";\n\nexport default {\n\t/**\n\t * @private\n\t * set if DOM events are being triggered.\n\t * But this is slower and unused by simple implementations, so disabled by default.\n\t * @type {Boolean}\n\t * @default false\n\t */\n\tdomEvents: false,\n\n\t/**\n\t * @private\n\t * The value for the touchAction property/fallback.\n\t * When set to `compute` it will magically set the correct value based on the added recognizers.\n\t * @type {String}\n\t * @default compute\n\t */\n\ttouchAction: TOUCH_ACTION_COMPUTE,\n\n\t/**\n\t * @private\n\t * @type {Boolean}\n\t * @default true\n\t */\n\tenable: true,\n\n\t/**\n\t * @private\n\t * EXPERIMENTAL FEATURE -- can be removed/changed\n\t * Change the parent input target element.\n\t * If Null, then it is being set the to main element.\n\t * @type {Null|EventTarget}\n\t * @default null\n\t */\n\tinputTarget: null,\n\n\t/**\n\t * @private\n\t * force an input class\n\t * @type {Null|Function}\n\t * @default null\n\t */\n\tinputClass: null,\n\n\t/**\n\t * @private\n\t * Some CSS properties can be used to improve the working of Hammer.\n\t * Add them to this method and they will be set when creating a new Manager.\n\t * @namespace\n\t */\n\tcssProps: {\n\t\t/**\n\t\t * @private\n\t\t * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tuserSelect: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Disable the Windows Phone grippers when pressing an element.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\ttouchSelect: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Disables the default callout shown when you touch and hold a touch target.\n\t\t * On iOS, when you touch and hold a touch target such as a link, Safari displays\n\t\t * a callout containing information about the link. This property allows you to disable that callout.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\ttouchCallout: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Specifies whether zooming is enabled. Used by IE10>\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tcontentZooming: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tuserDrag: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Overrides the highlight color shown when the user taps a link or a JavaScript\n\t\t * clickable element in iOS. This property obeys the alpha value, if specified.\n\t\t * @type {String}\n\t\t * @default 'rgba(0,0,0,0)'\n\t\t */\n\t\ttapHighlightColor: \"rgba(0,0,0,0)\",\n\t},\n};\n\n/**\n * @private\n * Default recognizer setup when calling `Hammer()`\n * When creating a new Manager these will be skipped.\n * This is separated with other defaults because of tree-shaking.\n * @type {Array}\n */\nexport const preset = [\n  [RotateRecognizer, { enable: false }],\n  [PinchRecognizer, { enable: false }, ['rotate']],\n  [SwipeRecognizer, { direction: DIRECTION_HORIZONTAL }],\n  [PanRecognizer, { direction: DIRECTION_HORIZONTAL }, ['swipe']],\n  [TapRecognizer],\n  [TapRecognizer, { event: 'doubletap', taps: 2 }, ['tap']],\n  [PressRecognizer]\n];\n", "import assign from \"./utils/assign\";\nimport TouchAction from \"./touchactionjs/touchaction-constructor\";\nimport createInputInstance from \"./inputjs/create-input-instance\";\nimport each from \"./utils/each\";\nimport inArray from \"./utils/in-array\";\nimport invokeArrayArg from \"./utils/invoke-array-arg\";\nimport splitStr from \"./utils/split-str\";\nimport prefixed from \"./utils/prefixed\";\nimport Recognizer from \"./recognizerjs/recognizer-constructor\";\nimport {\n  STATE_BEGAN,\n  STATE_ENDED,\n  STATE_CHANGED,\n  STATE_RECOGNIZED,\n} from \"./recognizerjs/recognizer-consts\";\nimport defaults from \"./defaults\";\n\nconst STOP = 1;\nconst FORCED_STOP = 2;\n\n\n/**\n * @private\n * add/remove the css properties as defined in manager.options.cssProps\n * @param {Manager} manager\n * @param {Boolean} add\n */\nfunction toggleCssProps(manager, add) {\n  const { element } = manager;\n\n  if (!element.style) {\n    return;\n  }\n  let prop;\n\n  each(manager.options.cssProps, (value, name) => {\n    prop = prefixed(element.style, name);\n    if (add) {\n      manager.oldCssProps[prop] = element.style[prop];\n      element.style[prop] = value;\n    } else {\n      element.style[prop] = manager.oldCssProps[prop] || \"\";\n    }\n  });\n  if (!add) {\n    manager.oldCssProps = {};\n  }\n}\n\n/**\n * @private\n * trigger dom event\n * @param {String} event\n * @param {Object} data\n */\nfunction triggerDomEvent(event, data) {\n  const gestureEvent = document.createEvent(\"Event\");\n\n  gestureEvent.initEvent(event, true, true);\n  gestureEvent.gesture = data;\n  data.target.dispatchEvent(gestureEvent);\n}\n\n\n/**\n* @private\n * Manager\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nexport default class Manager {\n  constructor(element, options) {\n    this.options = assign({}, defaults, options || {});\n\n    this.options.inputTarget = this.options.inputTarget || element;\n\n    this.handlers = {};\n    this.session = {};\n    this.recognizers = [];\n    this.oldCssProps = {};\n\n    this.element = element;\n    this.input = createInputInstance(this);\n    this.touchAction = new TouchAction(this, this.options.touchAction);\n\n    toggleCssProps(this, true);\n\n    each(this.options.recognizers, item => {\n      const recognizer = this.add(new (item[0])(item[1]));\n\n      item[2] && recognizer.recognizeWith(item[2]);\n      item[3] && recognizer.requireFailure(item[3]);\n    }, this);\n  }\n\n\t/**\n\t * @private\n\t * set options\n\t * @param {Object} options\n\t * @returns {Manager}\n\t */\n  set(options) {\n    assign(this.options, options);\n\n    // Options that need a little more setup\n    if (options.touchAction) {\n      this.touchAction.update();\n    }\n    if (options.inputTarget) {\n      // Clean up existing event listeners and reinitialize\n      this.input.destroy();\n      this.input.target = options.inputTarget;\n      this.input.init();\n    }\n    return this;\n  }\n\n\t/**\n\t * @private\n\t * stop recognizing for this session.\n\t * This session will be discarded, when a new [input]start event is fired.\n\t * When forced, the recognizer cycle is stopped immediately.\n\t * @param {Boolean} [force]\n\t */\n  stop(force) {\n    this.session.stopped = force ? FORCED_STOP : STOP;\n  }\n\n\t/**\n\t * @private\n\t * run the recognizers!\n\t * called by the inputHandler function on every movement of the pointers (touches)\n\t * it walks through all the recognizers and tries to detect the gesture that is being made\n\t * @param {Object} inputData\n\t */\n  recognize(inputData) {\n    const { session } = this;\n\n    if (session.stopped) {\n      return;\n    }\n\n    // run the touch-action polyfill\n    this.touchAction.preventDefaults(inputData);\n\n    let recognizer;\n    const { recognizers } = this;\n\n    // this holds the recognizer that is being recognized.\n    // so the recognizer's state needs to be BEGAN, CHANGED, ENDED or RECOGNIZED\n    // if no recognizer is detecting a thing, it is set to `null`\n    let { curRecognizer } = session;\n\n    // reset when the last recognizer is recognized\n    // or when we're in a new session\n    if (!curRecognizer || (curRecognizer && curRecognizer.state & STATE_RECOGNIZED)) {\n      session.curRecognizer = null;\n      curRecognizer = null;\n    }\n\n    let i = 0;\n\n    while (i < recognizers.length) {\n      recognizer = recognizers[i];\n\n      // find out if we are allowed try to recognize the input for this one.\n      // 1.   allow if the session is NOT forced stopped (see the .stop() method)\n      // 2.   allow if we still haven't recognized a gesture in this session, or the this recognizer is the one\n      //      that is being recognized.\n      // 3.   allow if the recognizer is allowed to run simultaneous with the current recognized recognizer.\n      //      this can be setup with the `recognizeWith()` method on the recognizer.\n      if (session.stopped !== FORCED_STOP && (// 1\n        !curRecognizer || recognizer === curRecognizer || // 2\n        recognizer.canRecognizeWith(curRecognizer))) { // 3\n        recognizer.recognize(inputData);\n      } else {\n        recognizer.reset();\n      }\n\n      // if the recognizer has been recognizing the input as a valid gesture, we want to store this one as the\n      // current active recognizer. but only if we don't already have an active recognizer\n      if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {\n        session.curRecognizer = recognizer;\n        curRecognizer = recognizer;\n      }\n      i++;\n    }\n  }\n\n\t/**\n\t * @private\n\t * get a recognizer by its event name.\n\t * @param {Recognizer|String} recognizer\n\t * @returns {Recognizer|Null}\n\t */\n  get(recognizer) {\n    if (recognizer instanceof Recognizer) {\n      return recognizer;\n    }\n\n    const { recognizers } = this;\n\n    for (let i = 0; i < recognizers.length; i++) {\n      if (recognizers[i].options.event === recognizer) {\n        return recognizers[i];\n      }\n    }\n    return null;\n  }\n\n\t/**\n\t * @private add a recognizer to the manager\n\t * existing recognizers with the same event name will be removed\n\t * @param {Recognizer} recognizer\n\t * @returns {Recognizer|Manager}\n\t */\n  add(recognizer) {\n    if (invokeArrayArg(recognizer, \"add\", this)) {\n      return this;\n    }\n\n    // remove existing\n    const existing = this.get(recognizer.options.event);\n\n    if (existing) {\n      this.remove(existing);\n    }\n\n    this.recognizers.push(recognizer);\n    recognizer.manager = this;\n\n    this.touchAction.update();\n    return recognizer;\n  }\n\n\t/**\n\t * @private\n\t * remove a recognizer by name or instance\n\t * @param {Recognizer|String} recognizer\n\t * @returns {Manager}\n\t */\n  remove(recognizer) {\n    if (invokeArrayArg(recognizer, \"remove\", this)) {\n      return this;\n    }\n\n    const targetRecognizer = this.get(recognizer);\n\n    // let's make sure this recognizer exists\n    if (recognizer) {\n      const { recognizers } = this;\n      const index = inArray(recognizers, targetRecognizer);\n\n      if (index !== -1) {\n        recognizers.splice(index, 1);\n        this.touchAction.update();\n      }\n    }\n\n    return this;\n  }\n\n\t/**\n\t * @private\n\t * bind event\n\t * @param {String} events\n\t * @param {Function} handler\n\t * @returns {EventEmitter} this\n\t */\n  on(events, handler) {\n    if (events === undefined || handler === undefined) {\n      return this;\n    }\n\n    const { handlers } = this;\n\n    each(splitStr(events), event => {\n      handlers[event] = handlers[event] || [];\n      handlers[event].push(handler);\n    });\n    return this;\n  }\n\n\t/**\n\t * @private unbind event, leave emit blank to remove all handlers\n\t * @param {String} events\n\t * @param {Function} [handler]\n\t * @returns {EventEmitter} this\n\t */\n  off(events, handler) {\n    if (events === undefined) {\n      return this;\n    }\n\n    const { handlers } = this;\n\n    each(splitStr(events), event => {\n      if (!handler) {\n        delete handlers[event];\n      } else {\n        handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);\n      }\n    });\n    return this;\n  }\n\n\t/**\n\t * @private emit event to the listeners\n\t * @param {String} event\n\t * @param {Object} data\n\t */\n  emit(event, data) {\n    // we also want to trigger dom events\n    if (this.options.domEvents) {\n      triggerDomEvent(event, data);\n    }\n\n    // no handlers, so skip it all\n    const handlers = this.handlers[event] && this.handlers[event].slice();\n\n    if (!handlers || !handlers.length) {\n      return;\n    }\n\n    data.type = event;\n    data.preventDefault = function () {\n      data.srcEvent.preventDefault();\n    };\n\n    let i = 0;\n\n    while (i < handlers.length) {\n      handlers[i](data);\n      i++;\n    }\n  }\n\n\t/**\n\t * @private\n\t * destroy the manager and unbinds all events\n\t * it doesn't unbind dom events, that is the user own responsibility\n\t */\n  destroy() {\n    this.element && toggleCssProps(this, false);\n\n    this.handlers = {};\n    this.session = {};\n    this.input.destroy();\n    this.element = null;\n  }\n}\n", "import {\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_CANCEL,\n    INPUT_TYPE_TOUCH\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\nimport toArray from '../utils/to-array';\nimport uniqueArray from '../utils/unique-array';\n\nconst SINGLE_TOUCH_INPUT_MAP = {\n  touchstart: INPUT_START,\n  touchmove: INPUT_MOVE,\n  touchend: INPUT_END,\n  touchcancel: INPUT_CANCEL\n};\n\nconst SINGLE_TOUCH_TARGET_EVENTS = 'touchstart';\nconst SINGLE_TOUCH_WINDOW_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * @private\n * Touch events input\n * @constructor\n * @extends Input\n */\nexport default class SingleTouchInput extends Input {\n  constructor() {\n    var proto = SingleTouchInput.prototype;\n    proto.evTarget = SINGLE_TOUCH_TARGET_EVENTS;\n    proto.evWin = SINGLE_TOUCH_WINDOW_EVENTS;\n\n    super(...arguments);\n    this.started = false;\n  }\n\n  handler(ev) {\n    let type = SINGLE_TOUCH_INPUT_MAP[ev.type];\n\n    // should we handle the touch events?\n    if (type === INPUT_START) {\n      this.started = true;\n    }\n\n    if (!this.started) {\n      return;\n    }\n\n    let touches = normalizeSingleTouches.call(this, ev, type);\n\n    // when done, reset the started state\n    if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {\n      this.started = false;\n    }\n\n    this.callback(this.manager, type, {\n      pointers: touches[0],\n      changedPointers: touches[1],\n      pointerType: INPUT_TYPE_TOUCH,\n      srcEvent: ev\n    });\n  }\n}\n\n/**\n * @private\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction normalizeSingleTouches(ev, type) {\n  let all = toArray(ev.touches);\n  let changed = toArray(ev.changedTouches);\n\n  if (type & (INPUT_END | INPUT_CANCEL)) {\n    all = uniqueArray(all.concat(changed), 'identifier', true);\n  }\n\n  return [all, changed];\n}\n", "/**\n * @private\n * wrap a method with a deprecation warning and stack trace\n * @param {Function} method\n * @param {String} name\n * @param {String} message\n * @returns {Function} A new function wrapping the supplied method.\n */\nexport default function deprecate(method, name, message) {\n  let deprecationMessage = `DEPRECATED METHOD: ${name}\\n${message} AT \\n`;\n  return function() {\n    let e = new Error('get-stack-trace');\n    let stack = e && e.stack ? e.stack.replace(/^[^\\(]+?[\\n$]/gm, '')\n        .replace(/^\\s+at\\s+/gm, '')\n        .replace(/^Object.<anonymous>\\s*\\(/gm, '{anonymous}()@') : 'Unknown Stack Trace';\n\n    let log = window.console && (window.console.warn || window.console.log);\n    if (log) {\n      log.call(window.console, deprecationMessage, stack);\n    }\n    return method.apply(this, arguments);\n  };\n}\n", "import deprecate from './deprecate';\n/**\n * @private\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} dest\n * @param {Object} src\n * @param {Boolean} [merge=false]\n * @returns {Object} dest\n */\nconst extend = deprecate((dest, src, merge) => {\n  let keys = Object.keys(src);\n  let i = 0;\n  while (i < keys.length) {\n    if (!merge || (merge && dest[keys[i]] === undefined)) {\n      dest[keys[i]] = src[keys[i]];\n    }\n    i++;\n  }\n  return dest;\n}, 'extend', 'Use `assign`.');\n\nexport default extend;\n", "import deprecate from './deprecate';\nimport extend from './extend';\n/**\n * @private\n * merge the values from src in the dest.\n * means that properties that exist in dest will not be overwritten by src\n * @param {Object} dest\n * @param {Object} src\n * @returns {Object} dest\n */\nconst merge = deprecate((dest, src) => {\n  return extend(dest, src, true);\n}, 'merge', 'Use `assign`.');\n\nexport default merge;\n", "import assign from './assign';\n/**\n * @private\n * simple class inheritance\n * @param {Function} child\n * @param {Function} base\n * @param {Object} [properties]\n */\nexport default function inherit(child, base, properties) {\n  let baseP = base.prototype;\n  let childP;\n\n  childP = child.prototype = Object.create(baseP);\n  childP.constructor = child;\n  childP._super = baseP;\n\n  if (properties) {\n    assign(childP, properties);\n  }\n}\n", "/**\n * @private\n * simple function bind\n * @param {Function} fn\n * @param {Object} context\n * @returns {Function}\n */\nexport default function bindFn(fn, context) {\n  return function boundFn() {\n    return fn.apply(context, arguments);\n  };\n}\n", "import Manager from \"./manager\";\nimport defaults, { preset } from \"./defaults\";\nimport assign from './utils/assign';\nimport {\n  INPUT_START,\n  INPUT_MOVE,\n  INPUT_END,\n  INPUT_CANCEL,\n  DIRECTION_NONE,\n  DIRECTION_LEFT,\n  DIRECTION_RIGHT,\n  DIRECTION_UP,\n  DIRECTION_DOWN,\n  DIRECTION_HORIZONTAL,\n  DIRECTION_VERTICAL,\n  DIRECTION_ALL,\n} from \"./inputjs/input-consts\";\nimport {\n  STATE_POSSIBLE,\n  STATE_BEGAN,\n  STATE_CHANGED,\n  STATE_ENDED,\n  STATE_RECOGNIZED,\n  STATE_CANCELLED,\n  STATE_FAILED,\n} from \"./recognizerjs/recognizer-consts\";\n\nimport Input from \"./inputjs/input-constructor\";\nimport TouchAction from \"./touchactionjs/touchaction-constructor\";\nimport TouchInput from \"./input/touch\";\nimport MouseInput from \"./input/mouse\";\nimport PointerEventInput from \"./input/pointerevent\";\nimport SingleTouchInput from \"./input/singletouch\";\nimport TouchMouseInput from \"./input/touchmouse\";\n\nimport Recognizer from \"./recognizerjs/recognizer-constructor\";\nimport AttrRecognizer from \"./recognizers/attribute\";\nimport TapRecognizer from \"./recognizers/tap\";\nimport PanRecognizer from \"./recognizers/pan\";\nimport SwipeRecognizer from \"./recognizers/swipe\";\nimport PinchRecognizer from \"./recognizers/pinch\";\nimport RotateRecognizer from \"./recognizers/rotate\";\nimport PressRecognizer from \"./recognizers/press\";\n\nimport addEventListeners from \"./utils/add-event-listeners\";\nimport removeEventListeners from \"./utils/remove-event-listeners\";\nimport each from \"./utils/each\";\nimport merge from \"./utils/merge\";\nimport extend from \"./utils/extend\";\nimport inherit from \"./utils/inherit\";\nimport bindFn from \"./utils/bind-fn\";\nimport prefixed from \"./utils/prefixed\";\nimport toArray from \"./utils/to-array\";\nimport uniqueArray from \"./utils/unique-array\";\nimport splitStr from \"./utils/split-str\";\nimport inArray from \"./utils/in-array\";\nimport boolOrFn from \"./utils/bool-or-fn\";\nimport hasParent from \"./utils/has-parent\";\n/**\n * @private\n * Simple way to create a manager with a default set of recognizers.\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nexport default class Hammer {\n\t/**\n   * @private\n   * @const {string}\n   */\n\tstatic VERSION = \"#__VERSION__#\";\n\tstatic DIRECTION_ALL = DIRECTION_ALL;\n\tstatic DIRECTION_DOWN = DIRECTION_DOWN;\n\tstatic DIRECTION_LEFT = DIRECTION_LEFT;\n\tstatic DIRECTION_RIGHT = DIRECTION_RIGHT;\n\tstatic DIRECTION_UP = DIRECTION_UP;\n\tstatic DIRECTION_HORIZONTAL = DIRECTION_HORIZONTAL;\n\tstatic DIRECTION_VERTICAL = DIRECTION_VERTICAL;\n\tstatic DIRECTION_NONE = DIRECTION_NONE;\n\tstatic DIRECTION_DOWN = DIRECTION_DOWN;\n\tstatic INPUT_START = INPUT_START;\n\tstatic INPUT_MOVE = INPUT_MOVE;\n  static INPUT_END = INPUT_END;\n\tstatic INPUT_CANCEL = INPUT_CANCEL;\n\tstatic STATE_POSSIBLE = STATE_POSSIBLE;\n\tstatic STATE_BEGAN = STATE_BEGAN;\n\tstatic STATE_CHANGED = STATE_CHANGED;\n\tstatic STATE_ENDED = STATE_ENDED;\n\tstatic STATE_RECOGNIZED = STATE_RECOGNIZED;\n\tstatic STATE_CANCELLED = STATE_CANCELLED;\n\tstatic STATE_FAILED = STATE_FAILED;\n\tstatic Manager = Manager;\n\tstatic Input = Input;\n\tstatic TouchAction = TouchAction;\n\tstatic TouchInput = TouchInput;\n\tstatic MouseInput = MouseInput;\n\tstatic PointerEventInput = PointerEventInput;\n\tstatic TouchMouseInput = TouchMouseInput;\n\tstatic SingleTouchInput = SingleTouchInput;\n\tstatic Recognizer = Recognizer;\n\tstatic AttrRecognizer = AttrRecognizer;\n\tstatic Tap = TapRecognizer;\n\tstatic Pan = PanRecognizer;\n\tstatic Swipe = SwipeRecognizer;\n\tstatic Pinch = PinchRecognizer;\n\tstatic Rotate = RotateRecognizer;\n\tstatic Press = PressRecognizer;\n\tstatic on = addEventListeners;\n\tstatic off = removeEventListeners;\n\tstatic each = each;\n\tstatic merge = merge;\n\tstatic extend = extend;\n\tstatic bindFn = bindFn;\n\tstatic assign = assign;\n\tstatic inherit = inherit;\n\tstatic bindFn = bindFn;\n\tstatic prefixed = prefixed;\n\tstatic toArray = toArray;\n\tstatic inArray = inArray;\n\tstatic uniqueArray = uniqueArray;\n\tstatic splitStr = splitStr;\n\tstatic boolOrFn = boolOrFn;\n\tstatic hasParent = hasParent;\n\tstatic addEventListeners = addEventListeners;\n\tstatic removeEventListeners = removeEventListeners;\n\tstatic defaults = assign({}, defaults, { preset });\n\tconstructor(element, options = {}) {\n\t\treturn new Manager(element, {\n\t\t\trecognizers: [\n        // RecognizerClass, options, [recognizeWith, ...], [requireFailure, ...]\n        ...preset\n\t\t\t],\n\t\t\t...options,\n\t\t});\n\t}\n}\n"], "names": ["assign", "Object", "target", "undefined", "TypeError", "output", "index", "arguments", "length", "source", "<PERSON><PERSON><PERSON>", "hasOwnProperty", "VENDOR_PREFIXES", "TEST_ELEMENT", "document", "style", "createElement", "TYPE_FUNCTION", "round", "Math", "abs", "now", "Date", "prefixed", "obj", "property", "prefix", "prop", "camelProp", "toUpperCase", "slice", "i", "win", "window", "PREFIXED_TOUCH_ACTION", "NATIVE_TOUCH_ACTION", "getTouchActionProps", "touchMap", "cssSupports", "CSS", "supports", "for<PERSON>ach", "val", "TOUCH_ACTION_COMPUTE", "TOUCH_ACTION_AUTO", "TOUCH_ACTION_MANIPULATION", "TOUCH_ACTION_NONE", "TOUCH_ACTION_PAN_X", "TOUCH_ACTION_PAN_Y", "TOUCH_ACTION_MAP", "MOBILE_REGEX", "SUPPORT_TOUCH", "SUPPORT_POINTER_EVENTS", "SUPPORT_ONLY_TOUCH", "test", "navigator", "userAgent", "INPUT_TYPE_TOUCH", "INPUT_TYPE_PEN", "INPUT_TYPE_MOUSE", "INPUT_TYPE_KINECT", "COMPUTE_INTERVAL", "INPUT_START", "INPUT_MOVE", "INPUT_END", "INPUT_CANCEL", "DIRECTION_NONE", "DIRECTION_LEFT", "DIRECTION_RIGHT", "DIRECTION_UP", "DIRECTION_DOWN", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_ALL", "PROPS_XY", "PROPS_CLIENT_XY", "each", "iterator", "context", "call", "boolOrFn", "args", "apply", "inStr", "str", "find", "indexOf", "cleanTouchActions", "actions", "hasPanX", "hasPanY", "TouchAction", "manager", "value", "set", "compute", "element", "toLowerCase", "trim", "update", "options", "touchAction", "recognizers", "recognizer", "enable", "concat", "getTouchAction", "join", "preventDefaults", "input", "srcEvent", "direction", "offsetDirection", "session", "prevented", "preventDefault", "hasNone", "isTapPointer", "pointers", "isTapMovement", "distance", "isTapTouchTime", "deltaTime", "preventSrc", "hasParent", "node", "parent", "parentNode", "getCenter", "pointers<PERSON><PERSON><PERSON>", "x", "clientX", "y", "clientY", "simpleCloneInputData", "timeStamp", "center", "deltaX", "deltaY", "getDistance", "p1", "p2", "props", "sqrt", "getAngle", "atan2", "PI", "getDirection", "computeDeltaXY", "offset", "offsetDelta", "prevDel<PERSON>", "prevInput", "eventType", "getVelocity", "getScale", "start", "end", "getRotation", "computeIntervalInputData", "last", "lastInterval", "velocity", "velocityX", "velocityY", "v", "computeInputData", "firstInput", "firstMultiple", "offsetCenter", "angle", "overallVelocity", "overallVelocityX", "overallVelocityY", "scale", "rotation", "maxPointers", "srcEventTarget", "<PERSON><PERSON><PERSON>", "path", "inputHandler", "pointersLen", "changedPointersLen", "changedPointers", "<PERSON><PERSON><PERSON><PERSON>", "isFinal", "emit", "recognize", "splitStr", "split", "addEventListeners", "types", "handler", "type", "addEventListener", "removeEventListeners", "removeEventListener", "getWindowForElement", "doc", "ownerDocument", "defaultView", "parentWindow", "Input", "callback", "self", "inputTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ev", "init", "evEl", "ev<PERSON><PERSON><PERSON>", "evWin", "destroy", "inArray", "src", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "POINTER_INPUT_MAP", "pointerdown", "pointermove", "pointerup", "pointercancel", "pointerout", "IE10_POINTER_TYPE_ENUM", "POINTER_ELEMENT_EVENTS", "POINTER_WINDOW_EVENTS", "MSPointerEvent", "PointerEvent", "PointerEventInput", "proto", "prototype", "store", "pointerEvents", "removePointer", "eventTypeNormalized", "replace", "pointerType", "is<PERSON><PERSON>ch", "storeIndex", "pointerId", "button", "push", "splice", "toArray", "Array", "uniqueArray", "key", "sort", "results", "values", "a", "b", "TOUCH_INPUT_MAP", "touchstart", "touchmove", "touchend", "touchcancel", "TOUCH_TARGET_EVENTS", "TouchInput", "targetIds", "touches", "getTouches", "allTouches", "identifier", "targetTouches", "changedTouches", "changedTargetTouches", "filter", "touch", "MOUSE_INPUT_MAP", "mousedown", "mousemove", "mouseup", "MOUSE_ELEMENT_EVENTS", "MOUSE_WINDOW_EVENTS", "MouseInput", "pressed", "which", "DEDUP_TIMEOUT", "DEDUP_DISTANCE", "setLastTouch", "eventData", "primaryTouch", "lastTouch", "lts", "lastTouches", "removeLastTouch", "setTimeout", "recordTouches", "isSyntheticEvent", "t", "dx", "dy", "TouchMouseInput", "inputEvent", "inputData", "isMouse", "sourceCapabilities", "firesTouchEvents", "mouse", "createInputInstance", "Type", "inputClass", "invokeArrayArg", "arg", "fn", "isArray", "STATE_POSSIBLE", "STATE_BEGAN", "STATE_CHANGED", "STATE_ENDED", "STATE_RECOGNIZED", "STATE_CANCELLED", "STATE_FAILED", "_uniqueId", "uniqueId", "getRecognizerByNameIfManager", "otherRecognizer", "get", "stateStr", "state", "Recognizer", "id", "simultaneous", "requireFail", "recognizeWith", "dropRecognizeWith", "requireFailure", "dropRequireFailure", "hasRequireFailures", "canRecognizeWith", "event", "additionalEvent", "tryEmit", "canEmit", "inputDataClone", "reset", "process", "TapRecognizer", "taps", "interval", "time", "threshold", "pos<PERSON><PERSON><PERSON><PERSON>", "pTime", "pCenter", "_timer", "_input", "count", "validPointers", "validMovement", "validTouchTime", "failTimeout", "validInterval", "validMultiTap", "tapCount", "clearTimeout", "AttrRecognizer", "attrTest", "optionPointers", "isRecognized", "<PERSON><PERSON><PERSON><PERSON>", "directionStr", "PanRecognizer", "pX", "pY", "directionTest", "hasMoved", "SwipeRecognizer", "PinchRecognizer", "inOut", "RotateRecognizer", "PressRecognizer", "validTime", "domEvents", "cssProps", "userSelect", "touchSelect", "touchCallout", "contentZooming", "userDrag", "tapHighlightColor", "preset", "STOP", "FORCED_STOP", "toggleCssProps", "add", "name", "oldCssProps", "triggerDomEvent", "data", "gestureEvent", "createEvent", "initEvent", "gesture", "dispatchEvent", "Manager", "defaults", "handlers", "item", "stop", "force", "stopped", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing", "remove", "targetRecognizer", "on", "events", "off", "SINGLE_TOUCH_INPUT_MAP", "SINGLE_TOUCH_TARGET_EVENTS", "SINGLE_TOUCH_WINDOW_EVENTS", "SingleTouchInput", "started", "normalizeSingleTouches", "all", "changed", "deprecate", "method", "message", "deprecationMessage", "e", "Error", "stack", "log", "console", "warn", "extend", "dest", "merge", "keys", "inherit", "child", "base", "properties", "baseP", "childP", "create", "constructor", "_super", "bindFn", "boundFn", "Hammer", "VERSION", "Tap", "Pan", "Swipe", "Pinch", "Rotate", "Press"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;EAQA,IAAIA,MAAJ;;EACA,IAAI,OAAOC,MAAM,CAACD,MAAd,KAAyB,UAA7B,EAAyC;EACvCA,EAAAA,MAAM,GAAG,SAASA,MAAT,CAAgBE,MAAhB,EAAwB;EAC/B,QAAIA,MAAM,KAAKC,SAAX,IAAwBD,MAAM,KAAK,IAAvC,EAA6C;EAC3C,YAAM,IAAIE,SAAJ,CAAc,4CAAd,CAAN;EACD;;EAED,QAAIC,MAAM,GAAGJ,MAAM,CAACC,MAAD,CAAnB;;EACA,SAAK,IAAII,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGC,SAAS,CAACC,MAAtC,EAA8CF,KAAK,EAAnD,EAAuD;EACrD,UAAMG,MAAM,GAAGF,SAAS,CAACD,KAAD,CAAxB;;EACA,UAAIG,MAAM,KAAKN,SAAX,IAAwBM,MAAM,KAAK,IAAvC,EAA6C;EAC3C,aAAK,IAAMC,OAAX,IAAsBD,MAAtB,EAA8B;EAC5B,cAAIA,MAAM,CAACE,cAAP,CAAsBD,OAAtB,CAAJ,EAAoC;EAClCL,YAAAA,MAAM,CAACK,OAAD,CAAN,GAAkBD,MAAM,CAACC,OAAD,CAAxB;EACD;EACF;EACF;EACF;;EACD,WAAOL,MAAP;EACD,GAjBD;EAkBD,CAnBD,MAmBO;EACLL,EAAAA,MAAM,GAAGC,MAAM,CAACD,MAAhB;EACD;;AAED,iBAAeA,MAAf;;EC/BA,IAAMY,eAAe,GAAG,CAAC,EAAD,EAAK,QAAL,EAAe,KAAf,EAAsB,IAAtB,EAA4B,IAA5B,EAAkC,GAAlC,CAAxB;EACA,IAAMC,YAAY,GAAG,OAAOC,QAAP,KAAoB,WAApB,GAAkC;EAACC,EAAAA,KAAK,EAAE;EAAR,CAAlC,GAAgDD,QAAQ,CAACE,aAAT,CAAuB,KAAvB,CAArE;EAEA,IAAMC,aAAa,GAAG,UAAtB;MAEQC,QAAeC,KAAfD;MAAOE,MAAQD,KAARC;MACPC,MAAQC,KAARD;;ECNR;;;;;;;;AAOA,EAAe,SAASE,QAAT,CAAkBC,GAAlB,EAAuBC,QAAvB,EAAiC;EAC9C,MAAIC,MAAJ;EACA,MAAIC,IAAJ;EACA,MAAIC,SAAS,GAAGH,QAAQ,CAAC,CAAD,CAAR,CAAYI,WAAZ,KAA4BJ,QAAQ,CAACK,KAAT,CAAe,CAAf,CAA5C;EAEA,MAAIC,CAAC,GAAG,CAAR;;EACA,SAAOA,CAAC,GAAGnB,eAAe,CAACJ,MAA3B,EAAmC;EACjCkB,IAAAA,MAAM,GAAGd,eAAe,CAACmB,CAAD,CAAxB;EACAJ,IAAAA,IAAI,GAAID,MAAD,GAAWA,MAAM,GAAGE,SAApB,GAAgCH,QAAvC;;EAEA,QAAIE,IAAI,IAAIH,GAAZ,EAAiB;EACf,aAAOG,IAAP;EACD;;EACDI,IAAAA,CAAC;EACF;;EACD,SAAO5B,SAAP;EACD;;ECxBD;EAEA,IAAI6B,GAAJ;;EAEA,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;EAClC;EACAD,EAAAA,GAAG,GAAG,EAAN;EACA,CAHD,MAGO;EACNA,EAAAA,GAAG,GAAGC,MAAN;EACA;;ECLM,IAAMC,qBAAqB,GAAGX,QAAQ,CAACV,YAAY,CAACE,KAAd,EAAqB,aAArB,CAAtC;AACP,EAAO,IAAMoB,mBAAmB,GAAGD,qBAAqB,KAAK/B,SAAtD;AAEP,EAAe,SAASiC,mBAAT,GAA+B;EAC5C,MAAI,CAACD,mBAAL,EAA0B;EACxB,WAAO,KAAP;EACD;;EACD,MAAIE,QAAQ,GAAG,EAAf;EACA,MAAIC,WAAW,GAAGL,GAAM,CAACM,GAAP,IAAcN,GAAM,CAACM,GAAP,CAAWC,QAA3C;EACA,GAAC,MAAD,EAAS,cAAT,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,aAA3C,EAA0D,MAA1D,EAAkEC,OAAlE,CAA0E,UAACC,GAAD,EAAS;EAEjF;EACA;EACA,WAAOL,QAAQ,CAACK,GAAD,CAAR,GAAgBJ,WAAW,GAAGL,GAAM,CAACM,GAAP,CAAWC,QAAX,CAAoB,cAApB,EAAoCE,GAApC,CAAH,GAA8C,IAAhF;EACD,GALD;EAMA,SAAOL,QAAP;EACD;;ECfD,IAAMM,oBAAoB,GAAG,SAA7B;EACA,IAAMC,iBAAiB,GAAG,MAA1B;EACA,IAAMC,yBAAyB,GAAG,cAAlC;;EACA,IAAMC,iBAAiB,GAAG,MAA1B;EACA,IAAMC,kBAAkB,GAAG,OAA3B;EACA,IAAMC,kBAAkB,GAAG,OAA3B;EACA,IAAMC,gBAAgB,GAAGb,mBAAmB,EAA5C;;ECRA,IAAMc,YAAY,GAAG,uCAArB;EAEA,IAAMC,aAAa,GAAI,kBAAkBlB,GAAzC;EACA,IAAMmB,sBAAsB,GAAG7B,QAAQ,CAACU,GAAD,EAAS,cAAT,CAAR,KAAqC9B,SAApE;EACA,IAAMkD,kBAAkB,GAAGF,aAAa,IAAID,YAAY,CAACI,IAAb,CAAkBC,SAAS,CAACC,SAA5B,CAA5C;EAEA,IAAMC,gBAAgB,GAAG,OAAzB;EACA,IAAMC,cAAc,GAAG,KAAvB;EACA,IAAMC,gBAAgB,GAAG,OAAzB;EACA,IAAMC,iBAAiB,GAAG,QAA1B;EAEA,IAAMC,gBAAgB,GAAG,EAAzB;EAEA,IAAMC,WAAW,GAAG,CAApB;EACA,IAAMC,UAAU,GAAG,CAAnB;EACA,IAAMC,SAAS,GAAG,CAAlB;EACA,IAAMC,YAAY,GAAG,CAArB;EAEA,IAAMC,cAAc,GAAG,CAAvB;EACA,IAAMC,cAAc,GAAG,CAAvB;EACA,IAAMC,eAAe,GAAG,CAAxB;EACA,IAAMC,YAAY,GAAG,CAArB;EACA,IAAMC,cAAc,GAAG,EAAvB;EAEA,IAAMC,oBAAoB,GAAGJ,cAAc,GAAGC,eAA9C;EACA,IAAMI,kBAAkB,GAAGH,YAAY,GAAGC,cAA1C;EACA,IAAMG,aAAa,GAAGF,oBAAoB,GAAGC,kBAA7C;EAEA,IAAME,QAAQ,GAAG,CAAC,GAAD,EAAM,GAAN,CAAjB;EACA,IAAMC,eAAe,GAAG,CAAC,SAAD,EAAY,SAAZ,CAAxB;;EChCA;;;;;;;AAOA,EAAe,SAASC,IAAT,CAAcpD,GAAd,EAAmBqD,QAAnB,EAA6BC,OAA7B,EAAsC;EACnD,MAAI/C,CAAJ;;EAEA,MAAI,CAACP,GAAL,EAAU;EACR;EACD;;EAED,MAAIA,GAAG,CAACiB,OAAR,EAAiB;EACfjB,IAAAA,GAAG,CAACiB,OAAJ,CAAYoC,QAAZ,EAAsBC,OAAtB;EACD,GAFD,MAEO,IAAItD,GAAG,CAAChB,MAAJ,KAAeL,SAAnB,EAA8B;EACnC4B,IAAAA,CAAC,GAAG,CAAJ;;EACA,WAAOA,CAAC,GAAGP,GAAG,CAAChB,MAAf,EAAuB;EACrBqE,MAAAA,QAAQ,CAACE,IAAT,CAAcD,OAAd,EAAuBtD,GAAG,CAACO,CAAD,CAA1B,EAA+BA,CAA/B,EAAkCP,GAAlC;EACAO,MAAAA,CAAC;EACF;EACF,GANM,MAMA;EACL,SAAKA,CAAL,IAAUP,GAAV,EAAe;EACbA,MAAAA,GAAG,CAACb,cAAJ,CAAmBoB,CAAnB,KAAyB8C,QAAQ,CAACE,IAAT,CAAcD,OAAd,EAAuBtD,GAAG,CAACO,CAAD,CAA1B,EAA+BA,CAA/B,EAAkCP,GAAlC,CAAzB;EACD;EACF;EACF;;EC1BD;;;;;;;;;AAQA,EAAe,SAASwD,QAAT,CAAkBtC,GAAlB,EAAuBuC,IAAvB,EAA6B;EAC1C,MAAI,OAAOvC,GAAP,KAAezB,aAAnB,EAAkC;EAChC,WAAOyB,GAAG,CAACwC,KAAJ,CAAUD,IAAI,GAAGA,IAAI,CAAC,CAAD,CAAJ,IAAW9E,SAAd,GAA0BA,SAAxC,EAAmD8E,IAAnD,CAAP;EACD;;EACD,SAAOvC,GAAP;EACD;;ECdD;;;;;;;AAOA,EAAe,SAASyC,KAAT,CAAeC,GAAf,EAAoBC,IAApB,EAA0B;EACvC,SAAOD,GAAG,CAACE,OAAJ,CAAYD,IAAZ,IAAoB,CAAC,CAA5B;EACD;;ECAD;;;;;;;AAMA,EAAe,SAASE,iBAAT,CAA2BC,OAA3B,EAAoC;EACjD;EACA,MAAIL,KAAK,CAACK,OAAD,EAAU1C,iBAAV,CAAT,EAAuC;EACrC,WAAOA,iBAAP;EACD;;EAED,MAAI2C,OAAO,GAAGN,KAAK,CAACK,OAAD,EAAUzC,kBAAV,CAAnB;EACA,MAAI2C,OAAO,GAAGP,KAAK,CAACK,OAAD,EAAUxC,kBAAV,CAAnB,CAPiD;EAUjD;EACA;EACA;;EACA,MAAIyC,OAAO,IAAIC,OAAf,EAAwB;EACtB,WAAO5C,iBAAP;EACD,GAfgD;;;EAkBjD,MAAI2C,OAAO,IAAIC,OAAf,EAAwB;EACtB,WAAOD,OAAO,GAAG1C,kBAAH,GAAwBC,kBAAtC;EACD,GApBgD;;;EAuBjD,MAAImC,KAAK,CAACK,OAAD,EAAU3C,yBAAV,CAAT,EAA+C;EAC7C,WAAOA,yBAAP;EACD;;EAED,SAAOD,iBAAP;EACD;;ECvBD;;;;;;;;;MAQqB+C;;;EACnB,uBAAYC,OAAZ,EAAqBC,KAArB,EAA4B;EAC1B,SAAKD,OAAL,GAAeA,OAAf;EACA,SAAKE,GAAL,CAASD,KAAT;EACD;EAED;;;;;;;;;WAKAC,mBAAID,OAAO;EACT;EACA,QAAIA,KAAK,KAAKlD,oBAAd,EAAoC;EAClCkD,MAAAA,KAAK,GAAG,KAAKE,OAAL,EAAR;EACD;;EAED,QAAI5D,mBAAmB,IAAI,KAAKyD,OAAL,CAAaI,OAAb,CAAqBjF,KAA5C,IAAqDkC,gBAAgB,CAAC4C,KAAD,CAAzE,EAAkF;EAChF,WAAKD,OAAL,CAAaI,OAAb,CAAqBjF,KAArB,CAA2BmB,qBAA3B,IAAoD2D,KAApD;EACD;;EACD,SAAKL,OAAL,GAAeK,KAAK,CAACI,WAAN,GAAoBC,IAApB,EAAf;EACD;EAED;;;;;;WAIAC,2BAAS;EACP,SAAKL,GAAL,CAAS,KAAKF,OAAL,CAAaQ,OAAb,CAAqBC,WAA9B;EACD;EAED;;;;;;;WAKAN,6BAAU;EACR,QAAIP,OAAO,GAAG,EAAd;EACAZ,IAAAA,IAAI,CAAC,KAAKgB,OAAL,CAAaU,WAAd,EAA2B,UAACC,UAAD,EAAgB;EAC7C,UAAIvB,QAAQ,CAACuB,UAAU,CAACH,OAAX,CAAmBI,MAApB,EAA4B,CAACD,UAAD,CAA5B,CAAZ,EAAuD;EACrDf,QAAAA,OAAO,GAAGA,OAAO,CAACiB,MAAR,CAAeF,UAAU,CAACG,cAAX,EAAf,CAAV;EACD;EACF,KAJG,CAAJ;EAKA,WAAOnB,iBAAiB,CAACC,OAAO,CAACmB,IAAR,CAAa,GAAb,CAAD,CAAxB;EACD;EAED;;;;;;;WAKAC,2CAAgBC,OAAO;EAAA,QACfC,QADe,GACFD,KADE,CACfC,QADe;EAErB,QAAIC,SAAS,GAAGF,KAAK,CAACG,eAAtB,CAFqB;;EAKrB,QAAI,KAAKpB,OAAL,CAAaqB,OAAb,CAAqBC,SAAzB,EAAoC;EAClCJ,MAAAA,QAAQ,CAACK,cAAT;EACA;EACD;;EARoB,QAUf3B,OAVe,GAUH,IAVG,CAUfA,OAVe;EAWrB,QAAI4B,OAAO,GAAGjC,KAAK,CAACK,OAAD,EAAU1C,iBAAV,CAAL,IAAqC,CAACG,gBAAgB,CAACH,iBAAD,CAApE;EACA,QAAI4C,OAAO,GAAGP,KAAK,CAACK,OAAD,EAAUxC,kBAAV,CAAL,IAAsC,CAACC,gBAAgB,CAACD,kBAAD,CAArE;EACA,QAAIyC,OAAO,GAAGN,KAAK,CAACK,OAAD,EAAUzC,kBAAV,CAAL,IAAsC,CAACE,gBAAgB,CAACF,kBAAD,CAArE;;EAEA,QAAIqE,OAAJ,EAAa;EACX;EACA,UAAIC,YAAY,GAAGR,KAAK,CAACS,QAAN,CAAe9G,MAAf,KAA0B,CAA7C;EACA,UAAI+G,aAAa,GAAGV,KAAK,CAACW,QAAN,GAAiB,CAArC;EACA,UAAIC,cAAc,GAAGZ,KAAK,CAACa,SAAN,GAAkB,GAAvC;;EAEA,UAAIL,YAAY,IAAIE,aAAhB,IAAiCE,cAArC,EAAqD;EACnD;EACD;EACF;;EAED,QAAIhC,OAAO,IAAIC,OAAf,EAAwB;EACtB;EACA;EACD;;EAED,QAAI0B,OAAO,IACN1B,OAAO,IAAIqB,SAAS,GAAGxC,oBADxB,IAECkB,OAAO,IAAIsB,SAAS,GAAGvC,kBAF5B,EAEiD;EAC/C,aAAO,KAAKmD,UAAL,CAAgBb,QAAhB,CAAP;EACD;EACF;EAED;;;;;;;WAKAa,iCAAWb,UAAU;EACnB,SAAKlB,OAAL,CAAaqB,OAAb,CAAqBC,SAArB,GAAiC,IAAjC;EACAJ,IAAAA,QAAQ,CAACK,cAAT;EACD;;;;;EC7HH;;;;;;;;AAQA,EAAe,SAASS,SAAT,CAAmBC,IAAnB,EAAyBC,MAAzB,EAAiC;EAC9C,SAAOD,IAAP,EAAa;EACX,QAAIA,IAAI,KAAKC,MAAb,EAAqB;EACnB,aAAO,IAAP;EACD;;EACDD,IAAAA,IAAI,GAAGA,IAAI,CAACE,UAAZ;EACD;;EACD,SAAO,KAAP;EACD;;ECdD;;;;;;;AAMA,EAAe,SAASC,SAAT,CAAmBV,QAAnB,EAA6B;EAC1C,MAAIW,cAAc,GAAGX,QAAQ,CAAC9G,MAA9B,CAD0C;;EAI1C,MAAIyH,cAAc,KAAK,CAAvB,EAA0B;EACxB,WAAO;EACLC,MAAAA,CAAC,EAAEhH,KAAK,CAACoG,QAAQ,CAAC,CAAD,CAAR,CAAYa,OAAb,CADH;EAELC,MAAAA,CAAC,EAAElH,KAAK,CAACoG,QAAQ,CAAC,CAAD,CAAR,CAAYe,OAAb;EAFH,KAAP;EAID;;EAED,MAAIH,CAAC,GAAG,CAAR;EACA,MAAIE,CAAC,GAAG,CAAR;EACA,MAAIrG,CAAC,GAAG,CAAR;;EACA,SAAOA,CAAC,GAAGkG,cAAX,EAA2B;EACzBC,IAAAA,CAAC,IAAIZ,QAAQ,CAACvF,CAAD,CAAR,CAAYoG,OAAjB;EACAC,IAAAA,CAAC,IAAId,QAAQ,CAACvF,CAAD,CAAR,CAAYsG,OAAjB;EACAtG,IAAAA,CAAC;EACF;;EAED,SAAO;EACLmG,IAAAA,CAAC,EAAEhH,KAAK,CAACgH,CAAC,GAAGD,cAAL,CADH;EAELG,IAAAA,CAAC,EAAElH,KAAK,CAACkH,CAAC,GAAGH,cAAL;EAFH,GAAP;EAID;;EC7BD;;;;;;;AAMA,EAAe,SAASK,oBAAT,CAA8BzB,KAA9B,EAAqC;EAClD;EACA;EACA,MAAIS,QAAQ,GAAG,EAAf;EACA,MAAIvF,CAAC,GAAG,CAAR;;EACA,SAAOA,CAAC,GAAG8E,KAAK,CAACS,QAAN,CAAe9G,MAA1B,EAAkC;EAChC8G,IAAAA,QAAQ,CAACvF,CAAD,CAAR,GAAc;EACZoG,MAAAA,OAAO,EAAEjH,KAAK,CAAC2F,KAAK,CAACS,QAAN,CAAevF,CAAf,EAAkBoG,OAAnB,CADF;EAEZE,MAAAA,OAAO,EAAEnH,KAAK,CAAC2F,KAAK,CAACS,QAAN,CAAevF,CAAf,EAAkBsG,OAAnB;EAFF,KAAd;EAIAtG,IAAAA,CAAC;EACF;;EAED,SAAO;EACLwG,IAAAA,SAAS,EAAElH,GAAG,EADT;EAELiG,IAAAA,QAAQ,EAARA,QAFK;EAGLkB,IAAAA,MAAM,EAAER,SAAS,CAACV,QAAD,CAHZ;EAILmB,IAAAA,MAAM,EAAE5B,KAAK,CAAC4B,MAJT;EAKLC,IAAAA,MAAM,EAAE7B,KAAK,CAAC6B;EALT,GAAP;EAOD;;EC3BD;;;;;;;;;AAQA,EAAe,SAASC,WAAT,CAAqBC,EAArB,EAAyBC,EAAzB,EAA6BC,KAA7B,EAAoC;EACjD,MAAI,CAACA,KAAL,EAAY;EACVA,IAAAA,KAAK,GAAGpE,QAAR;EACD;;EACD,MAAIwD,CAAC,GAAGW,EAAE,CAACC,KAAK,CAAC,CAAD,CAAN,CAAF,GAAeF,EAAE,CAACE,KAAK,CAAC,CAAD,CAAN,CAAzB;EACA,MAAIV,CAAC,GAAGS,EAAE,CAACC,KAAK,CAAC,CAAD,CAAN,CAAF,GAAeF,EAAE,CAACE,KAAK,CAAC,CAAD,CAAN,CAAzB;EAEA,SAAO3H,IAAI,CAAC4H,IAAL,CAAWb,CAAC,GAAGA,CAAL,GAAWE,CAAC,GAAGA,CAAzB,CAAP;EACD;;EChBD;;;;;;;;;AAQA,EAAe,SAASY,QAAT,CAAkBJ,EAAlB,EAAsBC,EAAtB,EAA0BC,KAA1B,EAAiC;EAC9C,MAAI,CAACA,KAAL,EAAY;EACVA,IAAAA,KAAK,GAAGpE,QAAR;EACD;;EACD,MAAIwD,CAAC,GAAGW,EAAE,CAACC,KAAK,CAAC,CAAD,CAAN,CAAF,GAAeF,EAAE,CAACE,KAAK,CAAC,CAAD,CAAN,CAAzB;EACA,MAAIV,CAAC,GAAGS,EAAE,CAACC,KAAK,CAAC,CAAD,CAAN,CAAF,GAAeF,EAAE,CAACE,KAAK,CAAC,CAAD,CAAN,CAAzB;EACA,SAAO3H,IAAI,CAAC8H,KAAL,CAAWb,CAAX,EAAcF,CAAd,IAAmB,GAAnB,GAAyB/G,IAAI,CAAC+H,EAArC;EACD;;ECdD;;;;;;;;AAOA,EAAe,SAASC,YAAT,CAAsBjB,CAAtB,EAAyBE,CAAzB,EAA4B;EACzC,MAAIF,CAAC,KAAKE,CAAV,EAAa;EACX,WAAOlE,cAAP;EACD;;EAED,MAAI9C,GAAG,CAAC8G,CAAD,CAAH,IAAU9G,GAAG,CAACgH,CAAD,CAAjB,EAAsB;EACpB,WAAOF,CAAC,GAAG,CAAJ,GAAQ/D,cAAR,GAAyBC,eAAhC;EACD;;EACD,SAAOgE,CAAC,GAAG,CAAJ,GAAQ/D,YAAR,GAAuBC,cAA9B;EACD;;ECjBc,SAAS8E,cAAT,CAAwBnC,OAAxB,EAAiCJ,KAAjC,EAAwC;EAAA,MAC/C2B,MAD+C,GACpC3B,KADoC,CAC/C2B,MAD+C;EAGrD;;EACA,MAAIa,MAAM,GAAGpC,OAAO,CAACqC,WAAR,IAAuB,EAApC;EACA,MAAIC,SAAS,GAAGtC,OAAO,CAACsC,SAAR,IAAqB,EAArC;EACA,MAAIC,SAAS,GAAGvC,OAAO,CAACuC,SAAR,IAAqB,EAArC;;EAEA,MAAI3C,KAAK,CAAC4C,SAAN,KAAoB3F,WAApB,IAAmC0F,SAAS,CAACC,SAAV,KAAwBzF,SAA/D,EAA0E;EACxEuF,IAAAA,SAAS,GAAGtC,OAAO,CAACsC,SAAR,GAAoB;EAC9BrB,MAAAA,CAAC,EAAEsB,SAAS,CAACf,MAAV,IAAoB,CADO;EAE9BL,MAAAA,CAAC,EAAEoB,SAAS,CAACd,MAAV,IAAoB;EAFO,KAAhC;EAKAW,IAAAA,MAAM,GAAGpC,OAAO,CAACqC,WAAR,GAAsB;EAC7BpB,MAAAA,CAAC,EAAEM,MAAM,CAACN,CADmB;EAE7BE,MAAAA,CAAC,EAAEI,MAAM,CAACJ;EAFmB,KAA/B;EAID;;EAEDvB,EAAAA,KAAK,CAAC4B,MAAN,GAAec,SAAS,CAACrB,CAAV,IAAeM,MAAM,CAACN,CAAP,GAAWmB,MAAM,CAACnB,CAAjC,CAAf;EACArB,EAAAA,KAAK,CAAC6B,MAAN,GAAea,SAAS,CAACnB,CAAV,IAAeI,MAAM,CAACJ,CAAP,GAAWiB,MAAM,CAACjB,CAAjC,CAAf;EACD;;ECxBD;;;;;;;;AAQA,EAAe,SAASsB,WAAT,CAAqBhC,SAArB,EAAgCQ,CAAhC,EAAmCE,CAAnC,EAAsC;EACnD,SAAO;EACLF,IAAAA,CAAC,EAAEA,CAAC,GAAGR,SAAJ,IAAiB,CADf;EAELU,IAAAA,CAAC,EAAEA,CAAC,GAAGV,SAAJ,IAAiB;EAFf,GAAP;EAID;;ECXD;;;;;;;;;AAQA,EAAe,SAASiC,QAAT,CAAkBC,KAAlB,EAAyBC,GAAzB,EAA8B;EAC3C,SAAOlB,WAAW,CAACkB,GAAG,CAAC,CAAD,CAAJ,EAASA,GAAG,CAAC,CAAD,CAAZ,EAAiBlF,eAAjB,CAAX,GAA+CgE,WAAW,CAACiB,KAAK,CAAC,CAAD,CAAN,EAAWA,KAAK,CAAC,CAAD,CAAhB,EAAqBjF,eAArB,CAAjE;EACD;;ECTD;;;;;;;;AAOA,EAAe,SAASmF,WAAT,CAAqBF,KAArB,EAA4BC,GAA5B,EAAiC;EAC9C,SAAOb,QAAQ,CAACa,GAAG,CAAC,CAAD,CAAJ,EAASA,GAAG,CAAC,CAAD,CAAZ,EAAiBlF,eAAjB,CAAR,GAA4CqE,QAAQ,CAACY,KAAK,CAAC,CAAD,CAAN,EAAWA,KAAK,CAAC,CAAD,CAAhB,EAAqBjF,eAArB,CAA3D;EACD;;ECPD;;;;;;;AAMA,EAAe,SAASoF,wBAAT,CAAkC9C,OAAlC,EAA2CJ,KAA3C,EAAkD;EAC/D,MAAImD,IAAI,GAAG/C,OAAO,CAACgD,YAAR,IAAwBpD,KAAnC;EACA,MAAIa,SAAS,GAAGb,KAAK,CAAC0B,SAAN,GAAkByB,IAAI,CAACzB,SAAvC;EACA,MAAI2B,QAAJ;EACA,MAAIC,SAAJ;EACA,MAAIC,SAAJ;EACA,MAAIrD,SAAJ;;EAEA,MAAIF,KAAK,CAAC4C,SAAN,KAAoBxF,YAApB,KAAqCyD,SAAS,GAAG7D,gBAAZ,IAAgCmG,IAAI,CAACE,QAAL,KAAkB/J,SAAvF,CAAJ,EAAuG;EACrG,QAAIsI,MAAM,GAAG5B,KAAK,CAAC4B,MAAN,GAAeuB,IAAI,CAACvB,MAAjC;EACA,QAAIC,MAAM,GAAG7B,KAAK,CAAC6B,MAAN,GAAesB,IAAI,CAACtB,MAAjC;EAEA,QAAI2B,CAAC,GAAGX,WAAW,CAAChC,SAAD,EAAYe,MAAZ,EAAoBC,MAApB,CAAnB;EACAyB,IAAAA,SAAS,GAAGE,CAAC,CAACnC,CAAd;EACAkC,IAAAA,SAAS,GAAGC,CAAC,CAACjC,CAAd;EACA8B,IAAAA,QAAQ,GAAI9I,GAAG,CAACiJ,CAAC,CAACnC,CAAH,CAAH,GAAW9G,GAAG,CAACiJ,CAAC,CAACjC,CAAH,CAAf,GAAwBiC,CAAC,CAACnC,CAA1B,GAA8BmC,CAAC,CAACjC,CAA3C;EACArB,IAAAA,SAAS,GAAGoC,YAAY,CAACV,MAAD,EAASC,MAAT,CAAxB;EAEAzB,IAAAA,OAAO,CAACgD,YAAR,GAAuBpD,KAAvB;EACD,GAXD,MAWO;EACL;EACAqD,IAAAA,QAAQ,GAAGF,IAAI,CAACE,QAAhB;EACAC,IAAAA,SAAS,GAAGH,IAAI,CAACG,SAAjB;EACAC,IAAAA,SAAS,GAAGJ,IAAI,CAACI,SAAjB;EACArD,IAAAA,SAAS,GAAGiD,IAAI,CAACjD,SAAjB;EACD;;EAEDF,EAAAA,KAAK,CAACqD,QAAN,GAAiBA,QAAjB;EACArD,EAAAA,KAAK,CAACsD,SAAN,GAAkBA,SAAlB;EACAtD,EAAAA,KAAK,CAACuD,SAAN,GAAkBA,SAAlB;EACAvD,EAAAA,KAAK,CAACE,SAAN,GAAkBA,SAAlB;EACD;;EC5BD;;;;;;;AAMA,EAAe,SAASuD,gBAAT,CAA0B1E,OAA1B,EAAmCiB,KAAnC,EAA0C;EAAA,MACjDI,OADiD,GACrCrB,OADqC,CACjDqB,OADiD;EAAA,MAEjDK,QAFiD,GAEpCT,KAFoC,CAEjDS,QAFiD;EAAA,MAG1CW,cAH0C,GAGvBX,QAHuB,CAGjD9G,MAHiD;;EAMvD,MAAI,CAACyG,OAAO,CAACsD,UAAb,EAAyB;EACvBtD,IAAAA,OAAO,CAACsD,UAAR,GAAqBjC,oBAAoB,CAACzB,KAAD,CAAzC;EACD,GARsD;;;EAWvD,MAAIoB,cAAc,GAAG,CAAjB,IAAsB,CAAChB,OAAO,CAACuD,aAAnC,EAAkD;EAChDvD,IAAAA,OAAO,CAACuD,aAAR,GAAwBlC,oBAAoB,CAACzB,KAAD,CAA5C;EACD,GAFD,MAEO,IAAIoB,cAAc,KAAK,CAAvB,EAA0B;EAC/BhB,IAAAA,OAAO,CAACuD,aAAR,GAAwB,KAAxB;EACD;;EAfsD,MAiBjDD,UAjBiD,GAiBnBtD,OAjBmB,CAiBjDsD,UAjBiD;EAAA,MAiBrCC,aAjBqC,GAiBnBvD,OAjBmB,CAiBrCuD,aAjBqC;EAkBvD,MAAIC,YAAY,GAAGD,aAAa,GAAGA,aAAa,CAAChC,MAAjB,GAA0B+B,UAAU,CAAC/B,MAArE;EAEA,MAAIA,MAAM,GAAG3B,KAAK,CAAC2B,MAAN,GAAeR,SAAS,CAACV,QAAD,CAArC;EACAT,EAAAA,KAAK,CAAC0B,SAAN,GAAkBlH,GAAG,EAArB;EACAwF,EAAAA,KAAK,CAACa,SAAN,GAAkBb,KAAK,CAAC0B,SAAN,GAAkBgC,UAAU,CAAChC,SAA/C;EAEA1B,EAAAA,KAAK,CAAC6D,KAAN,GAAc1B,QAAQ,CAACyB,YAAD,EAAejC,MAAf,CAAtB;EACA3B,EAAAA,KAAK,CAACW,QAAN,GAAiBmB,WAAW,CAAC8B,YAAD,EAAejC,MAAf,CAA5B;EAEAY,EAAAA,cAAc,CAACnC,OAAD,EAAUJ,KAAV,CAAd;EACAA,EAAAA,KAAK,CAACG,eAAN,GAAwBmC,YAAY,CAACtC,KAAK,CAAC4B,MAAP,EAAe5B,KAAK,CAAC6B,MAArB,CAApC;EAEA,MAAIiC,eAAe,GAAGjB,WAAW,CAAC7C,KAAK,CAACa,SAAP,EAAkBb,KAAK,CAAC4B,MAAxB,EAAgC5B,KAAK,CAAC6B,MAAtC,CAAjC;EACA7B,EAAAA,KAAK,CAAC+D,gBAAN,GAAyBD,eAAe,CAACzC,CAAzC;EACArB,EAAAA,KAAK,CAACgE,gBAAN,GAAyBF,eAAe,CAACvC,CAAzC;EACAvB,EAAAA,KAAK,CAAC8D,eAAN,GAAyBvJ,GAAG,CAACuJ,eAAe,CAACzC,CAAjB,CAAH,GAAyB9G,GAAG,CAACuJ,eAAe,CAACvC,CAAjB,CAA7B,GAAoDuC,eAAe,CAACzC,CAApE,GAAwEyC,eAAe,CAACvC,CAAhH;EAEAvB,EAAAA,KAAK,CAACiE,KAAN,GAAcN,aAAa,GAAGb,QAAQ,CAACa,aAAa,CAAClD,QAAf,EAAyBA,QAAzB,CAAX,GAAgD,CAA3E;EACAT,EAAAA,KAAK,CAACkE,QAAN,GAAiBP,aAAa,GAAGV,WAAW,CAACU,aAAa,CAAClD,QAAf,EAAyBA,QAAzB,CAAd,GAAmD,CAAjF;EAEAT,EAAAA,KAAK,CAACmE,WAAN,GAAoB,CAAC/D,OAAO,CAACuC,SAAT,GAAqB3C,KAAK,CAACS,QAAN,CAAe9G,MAApC,GAA+CqG,KAAK,CAACS,QAAN,CAAe9G,MAAf,GACnEyG,OAAO,CAACuC,SAAR,CAAkBwB,WADgD,GACjCnE,KAAK,CAACS,QAAN,CAAe9G,MADkB,GACTyG,OAAO,CAACuC,SAAR,CAAkBwB,WAD3E;EAGAjB,EAAAA,wBAAwB,CAAC9C,OAAD,EAAUJ,KAAV,CAAxB,CAzCuD;;EA4CvD,MAAI3G,MAAM,GAAG0F,OAAO,CAACI,OAArB;EACA,MAAMc,QAAQ,GAAGD,KAAK,CAACC,QAAvB;EACA,MAAImE,cAAJ;;EAEA,MAAInE,QAAQ,CAACoE,YAAb,EAA2B;EACzBD,IAAAA,cAAc,GAAGnE,QAAQ,CAACoE,YAAT,GAAwB,CAAxB,CAAjB;EACD,GAFD,MAEO,IAAIpE,QAAQ,CAACqE,IAAb,EAAmB;EACxBF,IAAAA,cAAc,GAAGnE,QAAQ,CAACqE,IAAT,CAAc,CAAd,CAAjB;EACD,GAFM,MAEA;EACLF,IAAAA,cAAc,GAAGnE,QAAQ,CAAC5G,MAA1B;EACD;;EAED,MAAI0H,SAAS,CAACqD,cAAD,EAAiB/K,MAAjB,CAAb,EAAuC;EACrCA,IAAAA,MAAM,GAAG+K,cAAT;EACD;;EACDpE,EAAAA,KAAK,CAAC3G,MAAN,GAAeA,MAAf;EACD;;EC7ED;;;;;;;;AAOA,EAAe,SAASkL,YAAT,CAAsBxF,OAAtB,EAA+B6D,SAA/B,EAA0C5C,KAA1C,EAAiD;EAC9D,MAAIwE,WAAW,GAAGxE,KAAK,CAACS,QAAN,CAAe9G,MAAjC;EACA,MAAI8K,kBAAkB,GAAGzE,KAAK,CAAC0E,eAAN,CAAsB/K,MAA/C;EACA,MAAIgL,OAAO,GAAI/B,SAAS,GAAG3F,WAAZ,IAA4BuH,WAAW,GAAGC,kBAAd,KAAqC,CAAhF;EACA,MAAIG,OAAO,GAAIhC,SAAS,IAAIzF,SAAS,GAAGC,YAAhB,CAAT,IAA2CoH,WAAW,GAAGC,kBAAd,KAAqC,CAA/F;EAEAzE,EAAAA,KAAK,CAAC2E,OAAN,GAAgB,CAAC,CAACA,OAAlB;EACA3E,EAAAA,KAAK,CAAC4E,OAAN,GAAgB,CAAC,CAACA,OAAlB;;EAEA,MAAID,OAAJ,EAAa;EACX5F,IAAAA,OAAO,CAACqB,OAAR,GAAkB,EAAlB;EACD,GAX6D;EAc9D;;;EACAJ,EAAAA,KAAK,CAAC4C,SAAN,GAAkBA,SAAlB,CAf8D;;EAkB9Da,EAAAA,gBAAgB,CAAC1E,OAAD,EAAUiB,KAAV,CAAhB,CAlB8D;;EAqB9DjB,EAAAA,OAAO,CAAC8F,IAAR,CAAa,cAAb,EAA6B7E,KAA7B;EAEAjB,EAAAA,OAAO,CAAC+F,SAAR,CAAkB9E,KAAlB;EACAjB,EAAAA,OAAO,CAACqB,OAAR,CAAgBuC,SAAhB,GAA4B3C,KAA5B;EACD;;ECnCD;;;;;;AAOA,EAAe,SAAS+E,QAAT,CAAkBxG,GAAlB,EAAuB;EACpC,SAAOA,GAAG,CAACc,IAAJ,GAAW2F,KAAX,CAAiB,MAAjB,CAAP;EACD;;ECPD;;;;;;;;AAOA,EAAe,SAASC,iBAAT,CAA2B5L,MAA3B,EAAmC6L,KAAnC,EAA0CC,OAA1C,EAAmD;EAChEpH,EAAAA,IAAI,CAACgH,QAAQ,CAACG,KAAD,CAAT,EAAkB,UAACE,IAAD,EAAU;EAC9B/L,IAAAA,MAAM,CAACgM,gBAAP,CAAwBD,IAAxB,EAA8BD,OAA9B,EAAuC,KAAvC;EACD,GAFG,CAAJ;EAGD;;ECXD;;;;;;;;AAOA,EAAe,SAASG,oBAAT,CAA8BjM,MAA9B,EAAsC6L,KAAtC,EAA6CC,OAA7C,EAAsD;EACnEpH,EAAAA,IAAI,CAACgH,QAAQ,CAACG,KAAD,CAAT,EAAkB,UAACE,IAAD,EAAU;EAC9B/L,IAAAA,MAAM,CAACkM,mBAAP,CAA2BH,IAA3B,EAAiCD,OAAjC,EAA0C,KAA1C;EACD,GAFG,CAAJ;EAGD;;ECbD;;;;;;AAMA,EAAe,SAASK,mBAAT,CAA6BrG,OAA7B,EAAsC;EACnD,MAAIsG,GAAG,GAAGtG,OAAO,CAACuG,aAAR,IAAyBvG,OAAnC;EACA,SAAQsG,GAAG,CAACE,WAAJ,IAAmBF,GAAG,CAACG,YAAvB,IAAuCxK,MAA/C;EACD;;ECJD;;;;;;;;;MAQqByK;;;EACnB,iBAAY9G,OAAZ,EAAqB+G,QAArB,EAA+B;EAC7B,QAAIC,IAAI,GAAG,IAAX;EACA,SAAKhH,OAAL,GAAeA,OAAf;EACA,SAAK+G,QAAL,GAAgBA,QAAhB;EACA,SAAK3G,OAAL,GAAeJ,OAAO,CAACI,OAAvB;EACA,SAAK9F,MAAL,GAAc0F,OAAO,CAACQ,OAAR,CAAgByG,WAA9B,CAL6B;EAQ7B;;EACA,SAAKC,UAAL,GAAkB,UAASC,EAAT,EAAa;EAC7B,UAAI/H,QAAQ,CAACY,OAAO,CAACQ,OAAR,CAAgBI,MAAjB,EAAyB,CAACZ,OAAD,CAAzB,CAAZ,EAAiD;EAC/CgH,QAAAA,IAAI,CAACZ,OAAL,CAAae,EAAb;EACD;EACF,KAJD;;EAMA,SAAKC,IAAL;EAED;EACD;;;;;;;;;WAKAhB,6BAAU;EAEV;;;;;;WAIAgB,uBAAO;EACL,SAAKC,IAAL,IAAanB,iBAAiB,CAAC,KAAK9F,OAAN,EAAe,KAAKiH,IAApB,EAA0B,KAAKH,UAA/B,CAA9B;EACA,SAAKI,QAAL,IAAiBpB,iBAAiB,CAAC,KAAK5L,MAAN,EAAc,KAAKgN,QAAnB,EAA6B,KAAKJ,UAAlC,CAAlC;EACA,SAAKK,KAAL,IAAcrB,iBAAiB,CAACO,mBAAmB,CAAC,KAAKrG,OAAN,CAApB,EAAoC,KAAKmH,KAAzC,EAAgD,KAAKL,UAArD,CAA/B;EACD;EAED;;;;;;WAIAM,6BAAU;EACR,SAAKH,IAAL,IAAad,oBAAoB,CAAC,KAAKnG,OAAN,EAAe,KAAKiH,IAApB,EAA0B,KAAKH,UAA/B,CAAjC;EACA,SAAKI,QAAL,IAAiBf,oBAAoB,CAAC,KAAKjM,MAAN,EAAc,KAAKgN,QAAnB,EAA6B,KAAKJ,UAAlC,CAArC;EACA,SAAKK,KAAL,IAAchB,oBAAoB,CAACE,mBAAmB,CAAC,KAAKrG,OAAN,CAApB,EAAoC,KAAKmH,KAAzC,EAAgD,KAAKL,UAArD,CAAlC;EACD;;;;;ECzDH;;;;;;;;AAQA,EAAe,SAASO,OAAT,CAAiBC,GAAjB,EAAsBjI,IAAtB,EAA4BkI,SAA5B,EAAuC;EACpD,MAAID,GAAG,CAAChI,OAAJ,IAAe,CAACiI,SAApB,EAA+B;EAC7B,WAAOD,GAAG,CAAChI,OAAJ,CAAYD,IAAZ,CAAP;EACD,GAFD,MAEO;EACL,QAAItD,CAAC,GAAG,CAAR;;EACA,WAAOA,CAAC,GAAGuL,GAAG,CAAC9M,MAAf,EAAuB;EACrB,UAAK+M,SAAS,IAAID,GAAG,CAACvL,CAAD,CAAH,CAAOwL,SAAP,KAAqBlI,IAAnC,IAA6C,CAACkI,SAAD,IAAcD,GAAG,CAACvL,CAAD,CAAH,KAAWsD,IAA1E,EAAiF;EAAC;EAChF,eAAOtD,CAAP;EACD;;EACDA,MAAAA,CAAC;EACF;;EACD,WAAO,CAAC,CAAR;EACD;EACF;;ECPD,IAAMyL,iBAAiB,GAAG;EACxBC,EAAAA,WAAW,EAAE3J,WADW;EAExB4J,EAAAA,WAAW,EAAE3J,UAFW;EAGxB4J,EAAAA,SAAS,EAAE3J,SAHa;EAIxB4J,EAAAA,aAAa,EAAE3J,YAJS;EAKxB4J,EAAAA,UAAU,EAAE5J;EALY,CAA1B;;EASA,IAAM6J,sBAAsB,GAAG;EAC7B,KAAGrK,gBAD0B;EAE7B,KAAGC,cAF0B;EAG7B,KAAGC,gBAH0B;EAI7B,KAAGC,iBAJ0B;;EAAA,CAA/B;EAOA,IAAImK,sBAAsB,GAAG,aAA7B;EACA,IAAIC,qBAAqB,GAAG,qCAA5B;;EAGA,IAAI/L,GAAM,CAACgM,cAAP,IAAyB,CAAChM,GAAM,CAACiM,YAArC,EAAmD;EACjDH,EAAAA,sBAAsB,GAAG,eAAzB;EACAC,EAAAA,qBAAqB,GAAG,2CAAxB;EACD;EAED;;;;;;;;MAMqBG;;;;;EACnB,+BAAc;EAAA;;EACZ,QAAIC,KAAK,GAAGD,iBAAiB,CAACE,SAA9B;EAEAD,IAAAA,KAAK,CAACnB,IAAN,GAAac,sBAAb;EACAK,IAAAA,KAAK,CAACjB,KAAN,GAAca,qBAAd;EACA,+BAASzN,SAAT;EACA,UAAK+N,KAAL,GAAc,MAAK1I,OAAL,CAAaqB,OAAb,CAAqBsH,aAArB,GAAqC,EAAnD;EANY;EAOb;EAED;;;;;;;;;WAKAvC,2BAAQe,IAAI;EAAA,QACJuB,KADI,GACM,IADN,CACJA,KADI;EAEV,QAAIE,aAAa,GAAG,KAApB;EAEA,QAAIC,mBAAmB,GAAG1B,EAAE,CAACd,IAAH,CAAQhG,WAAR,GAAsByI,OAAtB,CAA8B,IAA9B,EAAoC,EAApC,CAA1B;EACA,QAAIjF,SAAS,GAAG+D,iBAAiB,CAACiB,mBAAD,CAAjC;EACA,QAAIE,WAAW,GAAGb,sBAAsB,CAACf,EAAE,CAAC4B,WAAJ,CAAtB,IAA0C5B,EAAE,CAAC4B,WAA/D;EAEA,QAAIC,OAAO,GAAID,WAAW,KAAKlL,gBAA/B,CARU;;EAWV,QAAIoL,UAAU,GAAGxB,OAAO,CAACiB,KAAD,EAAQvB,EAAE,CAAC+B,SAAX,EAAsB,WAAtB,CAAxB,CAXU;;EAcV,QAAIrF,SAAS,GAAG3F,WAAZ,KAA4BiJ,EAAE,CAACgC,MAAH,KAAc,CAAd,IAAmBH,OAA/C,CAAJ,EAA6D;EAC3D,UAAIC,UAAU,GAAG,CAAjB,EAAoB;EAClBP,QAAAA,KAAK,CAACU,IAAN,CAAWjC,EAAX;EACA8B,QAAAA,UAAU,GAAGP,KAAK,CAAC9N,MAAN,GAAe,CAA5B;EACD;EACF,KALD,MAKO,IAAIiJ,SAAS,IAAIzF,SAAS,GAAGC,YAAhB,CAAb,EAA4C;EACjDuK,MAAAA,aAAa,GAAG,IAAhB;EACD,KArBS;;;EAwBV,QAAIK,UAAU,GAAG,CAAjB,EAAoB;EAClB;EACD,KA1BS;;;EA6BVP,IAAAA,KAAK,CAACO,UAAD,CAAL,GAAoB9B,EAApB;EAEA,SAAKJ,QAAL,CAAc,KAAK/G,OAAnB,EAA4B6D,SAA5B,EAAuC;EACrCnC,MAAAA,QAAQ,EAAEgH,KAD2B;EAErC/C,MAAAA,eAAe,EAAE,CAACwB,EAAD,CAFoB;EAGrC4B,MAAAA,WAAW,EAAXA,WAHqC;EAIrC7H,MAAAA,QAAQ,EAAEiG;EAJ2B,KAAvC;;EAOA,QAAIyB,aAAJ,EAAmB;EACjB;EACAF,MAAAA,KAAK,CAACW,MAAN,CAAaJ,UAAb,EAAyB,CAAzB;EACD;EACF;;;IAzD4CnC;;EC7C/C;;;;;;AAMA,EAAe,SAASwC,OAAT,CAAiB1N,GAAjB,EAAsB;EACnC,SAAO2N,KAAK,CAACd,SAAN,CAAgBvM,KAAhB,CAAsBiD,IAAtB,CAA2BvD,GAA3B,EAAgC,CAAhC,CAAP;EACD;;ECND;;;;;;;;;AAQA,EAAe,SAAS4N,WAAT,CAAqB9B,GAArB,EAA0B+B,GAA1B,EAA+BC,IAA/B,EAAqC;EAClD,MAAIC,OAAO,GAAG,EAAd;EACA,MAAIC,MAAM,GAAG,EAAb;EACA,MAAIzN,CAAC,GAAG,CAAR;;EAEA,SAAOA,CAAC,GAAGuL,GAAG,CAAC9M,MAAf,EAAuB;EACrB,QAAIkC,GAAG,GAAG2M,GAAG,GAAG/B,GAAG,CAACvL,CAAD,CAAH,CAAOsN,GAAP,CAAH,GAAiB/B,GAAG,CAACvL,CAAD,CAAjC;;EACA,QAAIsL,OAAO,CAACmC,MAAD,EAAS9M,GAAT,CAAP,GAAuB,CAA3B,EAA8B;EAC5B6M,MAAAA,OAAO,CAACP,IAAR,CAAa1B,GAAG,CAACvL,CAAD,CAAhB;EACD;;EACDyN,IAAAA,MAAM,CAACzN,CAAD,CAAN,GAAYW,GAAZ;EACAX,IAAAA,CAAC;EACF;;EAED,MAAIuN,IAAJ,EAAU;EACR,QAAI,CAACD,GAAL,EAAU;EACRE,MAAAA,OAAO,GAAGA,OAAO,CAACD,IAAR,EAAV;EACD,KAFD,MAEO;EACLC,MAAAA,OAAO,GAAGA,OAAO,CAACD,IAAR,CAAa,UAACG,CAAD,EAAIC,CAAJ,EAAU;EAC/B,eAAOD,CAAC,CAACJ,GAAD,CAAD,GAASK,CAAC,CAACL,GAAD,CAAjB;EACD,OAFS,CAAV;EAGD;EACF;;EAED,SAAOE,OAAP;EACD;;ECvBD,IAAMI,eAAe,GAAG;EACtBC,EAAAA,UAAU,EAAE9L,WADU;EAEtB+L,EAAAA,SAAS,EAAE9L,UAFW;EAGtB+L,EAAAA,QAAQ,EAAE9L,SAHY;EAItB+L,EAAAA,WAAW,EAAE9L;EAJS,CAAxB;EAOA,IAAM+L,mBAAmB,GAAG,2CAA5B;EAEA;;;;;;;MAMqBC;;;;;EACnB,wBAAc;EAAA;;EACZA,IAAAA,UAAU,CAAC5B,SAAX,CAAqBnB,QAArB,GAAgC8C,mBAAhC;EACA,+BAASzP,SAAT;EACA,UAAK2P,SAAL,GAAiB,EAAjB,CAHY;;EAAA;EAKb;;;;WACDlE,2BAAQe,IAAI;EACV,QAAId,IAAI,GAAG0D,eAAe,CAAC5C,EAAE,CAACd,IAAJ,CAA1B;EACA,QAAIkE,OAAO,GAAGC,UAAU,CAACrL,IAAX,CAAgB,IAAhB,EAAsBgI,EAAtB,EAA0Bd,IAA1B,CAAd;;EACA,QAAI,CAACkE,OAAL,EAAc;EACZ;EACD;;EAED,SAAKxD,QAAL,CAAc,KAAK/G,OAAnB,EAA4BqG,IAA5B,EAAkC;EAChC3E,MAAAA,QAAQ,EAAE6I,OAAO,CAAC,CAAD,CADe;EAEhC5E,MAAAA,eAAe,EAAE4E,OAAO,CAAC,CAAD,CAFQ;EAGhCxB,MAAAA,WAAW,EAAElL,gBAHmB;EAIhCqD,MAAAA,QAAQ,EAAEiG;EAJsB,KAAlC;EAMD;;;IApBqCL;AAuBxC;EAOA,SAAS0D,UAAT,CAAoBrD,EAApB,EAAwBd,IAAxB,EAA8B;EAC5B,MAAIoE,UAAU,GAAGnB,OAAO,CAACnC,EAAE,CAACoD,OAAJ,CAAxB;EAD4B,MAEtBD,SAFsB,GAER,IAFQ,CAEtBA,SAFsB;;EAK5B,MAAIjE,IAAI,IAAInI,WAAW,GAAGC,UAAlB,CAAJ,IAAqCsM,UAAU,CAAC7P,MAAX,KAAsB,CAA/D,EAAkE;EAChE0P,IAAAA,SAAS,CAACG,UAAU,CAAC,CAAD,CAAV,CAAcC,UAAf,CAAT,GAAsC,IAAtC;EACA,WAAO,CAACD,UAAD,EAAaA,UAAb,CAAP;EACD;;EAED,MAAItO,CAAJ;EACA,MAAIwO,aAAJ;EACA,MAAIC,cAAc,GAAGtB,OAAO,CAACnC,EAAE,CAACyD,cAAJ,CAA5B;EACA,MAAIC,oBAAoB,GAAG,EAA3B;EAb4B,MActBvQ,MAdsB,GAcX,IAdW,CActBA,MAdsB;;EAiB5BqQ,EAAAA,aAAa,GAAGF,UAAU,CAACK,MAAX,CAAkB,UAACC,KAAD,EAAW;EAC3C,WAAO/I,SAAS,CAAC+I,KAAK,CAACzQ,MAAP,EAAeA,MAAf,CAAhB;EACD,GAFe,CAAhB,CAjB4B;;EAsB5B,MAAI+L,IAAI,KAAKnI,WAAb,EAA0B;EACxB/B,IAAAA,CAAC,GAAG,CAAJ;;EACA,WAAOA,CAAC,GAAGwO,aAAa,CAAC/P,MAAzB,EAAiC;EAC/B0P,MAAAA,SAAS,CAACK,aAAa,CAACxO,CAAD,CAAb,CAAiBuO,UAAlB,CAAT,GAAyC,IAAzC;EACAvO,MAAAA,CAAC;EACF;EACF,GA5B2B;;;EA+B5BA,EAAAA,CAAC,GAAG,CAAJ;;EACA,SAAOA,CAAC,GAAGyO,cAAc,CAAChQ,MAA1B,EAAkC;EAChC,QAAI0P,SAAS,CAACM,cAAc,CAACzO,CAAD,CAAd,CAAkBuO,UAAnB,CAAb,EAA6C;EAC3CG,MAAAA,oBAAoB,CAACzB,IAArB,CAA0BwB,cAAc,CAACzO,CAAD,CAAxC;EACD,KAH+B;;;EAMhC,QAAIkK,IAAI,IAAIjI,SAAS,GAAGC,YAAhB,CAAR,EAAuC;EACrC,aAAOiM,SAAS,CAACM,cAAc,CAACzO,CAAD,CAAd,CAAkBuO,UAAnB,CAAhB;EACD;;EACDvO,IAAAA,CAAC;EACF;;EAED,MAAI,CAAC0O,oBAAoB,CAACjQ,MAA1B,EAAkC;EAChC;EACD;;EAED,SAAO;EAEL4O,EAAAA,WAAW,CAACmB,aAAa,CAAC9J,MAAd,CAAqBgK,oBAArB,CAAD,EAA6C,YAA7C,EAA2D,IAA3D,CAFN,EAGLA,oBAHK,CAAP;EAKD;;ECtGD,IAAMG,eAAe,GAAG;EACtBC,EAAAA,SAAS,EAAE/M,WADW;EAEtBgN,EAAAA,SAAS,EAAE/M,UAFW;EAGtBgN,EAAAA,OAAO,EAAE/M;EAHa,CAAxB;EAMA,IAAMgN,oBAAoB,GAAG,WAA7B;EACA,IAAMC,mBAAmB,GAAG,mBAA5B;EAEA;;;;;;;MAMqBC;;;;;EACnB,wBAAc;EAAA;;EACZ,QAAI9C,KAAK,GAAG8C,UAAU,CAAC7C,SAAvB;EACAD,IAAAA,KAAK,CAACnB,IAAN,GAAa+D,oBAAb;EACA5C,IAAAA,KAAK,CAACjB,KAAN,GAAc8D,mBAAd;EAEA,+BAAS1Q,SAAT;EACA,UAAK4Q,OAAL,GAAe,KAAf,CANY;;EAAA;EAOb;EAED;;;;;;;;;WAKAnF,2BAAQe,IAAI;EACV,QAAItD,SAAS,GAAGmH,eAAe,CAAC7D,EAAE,CAACd,IAAJ,CAA/B,CADU;;EAIV,QAAIxC,SAAS,GAAG3F,WAAZ,IAA2BiJ,EAAE,CAACgC,MAAH,KAAc,CAA7C,EAAgD;EAC9C,WAAKoC,OAAL,GAAe,IAAf;EACD;;EAED,QAAI1H,SAAS,GAAG1F,UAAZ,IAA0BgJ,EAAE,CAACqE,KAAH,KAAa,CAA3C,EAA8C;EAC5C3H,MAAAA,SAAS,GAAGzF,SAAZ;EACD,KAVS;;;EAaV,QAAI,CAAC,KAAKmN,OAAV,EAAmB;EACjB;EACD;;EAED,QAAI1H,SAAS,GAAGzF,SAAhB,EAA2B;EACzB,WAAKmN,OAAL,GAAe,KAAf;EACD;;EAED,SAAKxE,QAAL,CAAc,KAAK/G,OAAnB,EAA4B6D,SAA5B,EAAuC;EACrCnC,MAAAA,QAAQ,EAAE,CAACyF,EAAD,CAD2B;EAErCxB,MAAAA,eAAe,EAAE,CAACwB,EAAD,CAFoB;EAGrC4B,MAAAA,WAAW,EAAEhL,gBAHwB;EAIrCmD,MAAAA,QAAQ,EAAEiG;EAJ2B,KAAvC;EAMD;;;IA1CqCL;;ECZxC;;;;;;;;;;;EAWA,IAAM2E,aAAa,GAAG,IAAtB;EACA,IAAMC,cAAc,GAAG,EAAvB;;EAEA,SAASC,YAAT,CAAsBC,SAAtB,EAAiC;EAAA,8BACKA,SADL,CACxBjG,eADwB;EAAA,MACNoF,KADM;;EAGhC,MAAIA,KAAK,CAACL,UAAN,KAAqB,KAAKmB,YAA9B,EAA4C;EAC3C,QAAMC,SAAS,GAAG;EAAExJ,MAAAA,CAAC,EAAEyI,KAAK,CAACxI,OAAX;EAAoBC,MAAAA,CAAC,EAAEuI,KAAK,CAACtI;EAA7B,KAAlB;EACA,QAAMsJ,GAAG,GAAG,KAAKC,WAAjB;EAEA,SAAKA,WAAL,CAAiB5C,IAAjB,CAAsB0C,SAAtB;;EAGA,QAAMG,eAAe,GAAG,SAAlBA,eAAkB,GAAW;EAClC,UAAM9P,CAAC,GAAG4P,GAAG,CAACrM,OAAJ,CAAYoM,SAAZ,CAAV;;EAEA,UAAI3P,CAAC,GAAG,CAAC,CAAT,EAAY;EACX4P,QAAAA,GAAG,CAAC1C,MAAJ,CAAWlN,CAAX,EAAc,CAAd;EACA;EACD,KAND;;EAQA+P,IAAAA,UAAU,CAACD,eAAD,EAAkBR,aAAlB,CAAV;EACA;EACD;;EAGD,SAASU,aAAT,CAAuBtI,SAAvB,EAAkC+H,SAAlC,EAA6C;EAC5C,MAAI/H,SAAS,GAAG3F,WAAhB,EAA6B;EAC5B,SAAK2N,YAAL,GAAoBD,SAAS,CAACjG,eAAV,CAA0B,CAA1B,EAA6B+E,UAAjD;EACAiB,IAAAA,YAAY,CAACxM,IAAb,CAAkB,IAAlB,EAAwByM,SAAxB;EACA,GAHD,MAGO,IAAI/H,SAAS,IAAIzF,SAAS,GAAGC,YAAhB,CAAb,EAA4C;EAClDsN,IAAAA,YAAY,CAACxM,IAAb,CAAkB,IAAlB,EAAwByM,SAAxB;EACA;EACD;;EACD,SAASQ,gBAAT,CAA0BR,SAA1B,EAAqC;EACpC,MAAMtJ,CAAC,GAAGsJ,SAAS,CAAC1K,QAAV,CAAmBqB,OAA7B;EACA,MAAMC,CAAC,GAAGoJ,SAAS,CAAC1K,QAAV,CAAmBuB,OAA7B;;EAEA,OAAK,IAAItG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK6P,WAAL,CAAiBpR,MAArC,EAA6CuB,CAAC,EAA9C,EAAkD;EACjD,QAAMkQ,CAAC,GAAG,KAAKL,WAAL,CAAiB7P,CAAjB,CAAV;EACA,QAAMmQ,EAAE,GAAG/Q,IAAI,CAACC,GAAL,CAAS8G,CAAC,GAAG+J,CAAC,CAAC/J,CAAf,CAAX;EACA,QAAMiK,EAAE,GAAGhR,IAAI,CAACC,GAAL,CAASgH,CAAC,GAAG6J,CAAC,CAAC7J,CAAf,CAAX;;EAEA,QAAI8J,EAAE,IAAIZ,cAAN,IAAwBa,EAAE,IAAIb,cAAlC,EAAkD;EACjD,aAAO,IAAP;EACA;EACD;;EACD,SAAO,KAAP;EACA;;MAGoBc;;;QAAAA;;;;;EACpB,6BAAYxM,QAAZ,EAAqB+G,QAArB,EAA+B;EAAA;;EAC9B,gCAAM/G,QAAN,EAAe+G,QAAf;;EAD8B,YAgB/BX,OAhB+B,GAgBrB,UAACpG,OAAD,EAAUyM,UAAV,EAAsBC,SAAtB,EAAoC;EAC7C,YAAM1D,OAAO,GAAI0D,SAAS,CAAC3D,WAAV,KAA0BlL,gBAA3C;EACA,YAAM8O,OAAO,GAAID,SAAS,CAAC3D,WAAV,KAA0BhL,gBAA3C;;EAEA,YAAI4O,OAAO,IAAID,SAAS,CAACE,kBAArB,IAA2CF,SAAS,CAACE,kBAAV,CAA6BC,gBAA5E,EAA8F;EAC7F;EACA,SAN4C;;;EAS7C,YAAI7D,OAAJ,EAAa;EACZmD,UAAAA,aAAa,CAAChN,IAAd,wDAAyBsN,UAAzB,EAAqCC,SAArC;EACA,SAFD,MAEO,IAAIC,OAAO,IAAIP,gBAAgB,CAACjN,IAAjB,wDAA4BuN,SAA5B,CAAf,EAAuD;EAC7D;EACA;;EAED,cAAK3F,QAAL,CAAc/G,OAAd,EAAuByM,UAAvB,EAAmCC,SAAnC;EACA,OAhC8B;;EAG9B,YAAK3B,KAAL,GAAa,IAAIV,UAAJ,CAAe,MAAKrK,OAApB,EAA6B,MAAKoG,OAAlC,CAAb;EACA,YAAK0G,KAAL,GAAa,IAAIxB,UAAJ,CAAe,MAAKtL,OAApB,EAA6B,MAAKoG,OAAlC,CAAb;EACA,YAAKyF,YAAL,GAAoB,IAApB;EACA,YAAKG,WAAL,GAAmB,EAAnB;EAN8B;EAO9B;EAED;;;;;;;;;;;EAyBA;;;;aAIAxE,6BAAU;EACT,WAAKuD,KAAL,CAAWvD,OAAX;EACA,WAAKsF,KAAL,CAAWtF,OAAX;EACA;;;MA1C2CV;;WAAxB0F;;;EClErB;;;;;;;;AAOA,EAAe,SAASO,mBAAT,CAA6B/M,OAA7B,EAAsC;EACnD,MAAIgN,IAAJ,CADmD;;EAAA,MAGnCC,UAHmC,GAGlBjN,OAHkB,CAG7CQ,OAH6C,CAGnCyM,UAHmC;;EAInD,MAAIA,UAAJ,EAAgB;EACdD,IAAAA,IAAI,GAAGC,UAAP;EACD,GAFD,MAEO,IAAIzP,sBAAJ,EAA4B;EACjCwP,IAAAA,IAAI,GAAGzE,iBAAP;EACD,GAFM,MAEA,IAAI9K,kBAAJ,EAAwB;EAC7BuP,IAAAA,IAAI,GAAG3C,UAAP;EACD,GAFM,MAEA,IAAI,CAAC9M,aAAL,EAAoB;EACzByP,IAAAA,IAAI,GAAG1B,UAAP;EACD,GAFM,MAEA;EACL0B,IAAAA,IAAI,GAAGR,eAAP;EACD;;EACD,SAAO,IAAKQ,IAAL,CAAWhN,OAAX,EAAoBwF,YAApB,CAAP;EACD;;EC7BD;;;;;;;;;;;AAUA,EAAe,SAAS0H,cAAT,CAAwBC,GAAxB,EAA6BC,EAA7B,EAAiClO,OAAjC,EAA0C;EACvD,MAAIqK,KAAK,CAAC8D,OAAN,CAAcF,GAAd,CAAJ,EAAwB;EACtBnO,IAAAA,IAAI,CAACmO,GAAD,EAAMjO,OAAO,CAACkO,EAAD,CAAb,EAAmBlO,OAAnB,CAAJ;EACA,WAAO,IAAP;EACD;;EACD,SAAO,KAAP;EACD;;ECjBD,IAAMoO,cAAc,GAAG,CAAvB;EACA,IAAMC,WAAW,GAAG,CAApB;EACA,IAAMC,aAAa,GAAG,CAAtB;EACA,IAAMC,WAAW,GAAG,CAApB;EACA,IAAMC,gBAAgB,GAAGD,WAAzB;EACA,IAAME,eAAe,GAAG,EAAxB;EACA,IAAMC,YAAY,GAAG,EAArB;;ECNA;;;;;EAKA,IAAIC,SAAS,GAAG,CAAhB;AACA,EAAe,SAASC,QAAT,GAAoB;EACjC,SAAOD,SAAS,EAAhB;EACD;;ECRD;;;;;;;AAOA,EAAe,SAASE,4BAAT,CAAsCC,eAAtC,EAAuDrN,UAAvD,EAAmE;EAAA,MAC1EX,OAD0E,GAC9DW,UAD8D,CAC1EX,OAD0E;;EAEhF,MAAIA,OAAJ,EAAa;EACX,WAAOA,OAAO,CAACiO,GAAR,CAAYD,eAAZ,CAAP;EACD;;EACD,SAAOA,eAAP;EACD;;ECND;;;;;;;AAMA,EAAe,SAASE,QAAT,CAAkBC,KAAlB,EAAyB;EACtC,MAAIA,KAAK,GAAGR,eAAZ,EAA6B;EAC3B,WAAO,QAAP;EACD,GAFD,MAEO,IAAIQ,KAAK,GAAGV,WAAZ,EAAyB;EAC9B,WAAO,KAAP;EACD,GAFM,MAEA,IAAIU,KAAK,GAAGX,aAAZ,EAA2B;EAChC,WAAO,MAAP;EACD,GAFM,MAEA,IAAIW,KAAK,GAAGZ,WAAZ,EAAyB;EAC9B,WAAO,OAAP;EACD;;EACD,SAAO,EAAP;EACD;;ECPD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BA;;;;;;;;MAOqBa;;;EACnB,sBAAY5N,OAAZ,EAA0B;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EACxB,SAAKA,OAAL;EACEI,MAAAA,MAAM,EAAE;EADV,OAEKJ,OAFL;EAKA,SAAK6N,EAAL,GAAUP,QAAQ,EAAlB;EAEA,SAAK9N,OAAL,GAAe,IAAf,CARwB;;EAWxB,SAAKmO,KAAL,GAAab,cAAb;EACA,SAAKgB,YAAL,GAAoB,EAApB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACD;EAED;;;;;;;;;;WAMArO,mBAAIM,SAAS;EACXpG,IAAAA,QAAM,CAAC,KAAKoG,OAAN,EAAeA,OAAf,CAAN,CADW;;EAIX,SAAKR,OAAL,IAAgB,KAAKA,OAAL,CAAaS,WAAb,CAAyBF,MAAzB,EAAhB;EACA,WAAO,IAAP;EACD;EAED;;;;;;;;WAMAiO,uCAAcR,iBAAiB;EAC7B,QAAId,cAAc,CAACc,eAAD,EAAkB,eAAlB,EAAmC,IAAnC,CAAlB,EAA4D;EAC1D,aAAO,IAAP;EACD;;EAH4B,QAKvBM,YALuB,GAKN,IALM,CAKvBA,YALuB;EAM7BN,IAAAA,eAAe,GAAGD,4BAA4B,CAACC,eAAD,EAAkB,IAAlB,CAA9C;;EACA,QAAI,CAACM,YAAY,CAACN,eAAe,CAACK,EAAjB,CAAjB,EAAuC;EACrCC,MAAAA,YAAY,CAACN,eAAe,CAACK,EAAjB,CAAZ,GAAmCL,eAAnC;EACAA,MAAAA,eAAe,CAACQ,aAAhB,CAA8B,IAA9B;EACD;;EACD,WAAO,IAAP;EACD;EAED;;;;;;;;WAMAC,+CAAkBT,iBAAiB;EACjC,QAAId,cAAc,CAACc,eAAD,EAAkB,mBAAlB,EAAuC,IAAvC,CAAlB,EAAgE;EAC9D,aAAO,IAAP;EACD;;EAEDA,IAAAA,eAAe,GAAGD,4BAA4B,CAACC,eAAD,EAAkB,IAAlB,CAA9C;EACA,WAAO,KAAKM,YAAL,CAAkBN,eAAe,CAACK,EAAlC,CAAP;EACA,WAAO,IAAP;EACD;EAED;;;;;;;;WAMAK,yCAAeV,iBAAiB;EAC9B,QAAId,cAAc,CAACc,eAAD,EAAkB,gBAAlB,EAAoC,IAApC,CAAlB,EAA6D;EAC3D,aAAO,IAAP;EACD;;EAH6B,QAKxBO,WALwB,GAKR,IALQ,CAKxBA,WALwB;EAM9BP,IAAAA,eAAe,GAAGD,4BAA4B,CAACC,eAAD,EAAkB,IAAlB,CAA9C;;EACA,QAAIvG,OAAO,CAAC8G,WAAD,EAAcP,eAAd,CAAP,KAA0C,CAAC,CAA/C,EAAkD;EAChDO,MAAAA,WAAW,CAACnF,IAAZ,CAAiB4E,eAAjB;EACAA,MAAAA,eAAe,CAACU,cAAhB,CAA+B,IAA/B;EACD;;EACD,WAAO,IAAP;EACD;EAED;;;;;;;;WAMAC,iDAAmBX,iBAAiB;EAClC,QAAId,cAAc,CAACc,eAAD,EAAkB,oBAAlB,EAAwC,IAAxC,CAAlB,EAAiE;EAC/D,aAAO,IAAP;EACD;;EAEDA,IAAAA,eAAe,GAAGD,4BAA4B,CAACC,eAAD,EAAkB,IAAlB,CAA9C;EACA,QAAItT,KAAK,GAAG+M,OAAO,CAAC,KAAK8G,WAAN,EAAmBP,eAAnB,CAAnB;;EACA,QAAItT,KAAK,GAAG,CAAC,CAAb,EAAgB;EACd,WAAK6T,WAAL,CAAiBlF,MAAjB,CAAwB3O,KAAxB,EAA+B,CAA/B;EACD;;EACD,WAAO,IAAP;EACD;EAED;;;;;;;WAKAkU,mDAAqB;EACnB,WAAO,KAAKL,WAAL,CAAiB3T,MAAjB,GAA0B,CAAjC;EACD;EAED;;;;;;;;WAMAiU,6CAAiBb,iBAAiB;EAChC,WAAO,CAAC,CAAC,KAAKM,YAAL,CAAkBN,eAAe,CAACK,EAAlC,CAAT;EACD;EAED;;;;;;;;WAMAvI,qBAAK7E,OAAO;EACV,QAAI+F,IAAI,GAAG,IAAX;EADU,QAEJmH,KAFI,GAEM,IAFN,CAEJA,KAFI;;EAIV,aAASrI,IAAT,CAAcgJ,KAAd,EAAqB;EACnB9H,MAAAA,IAAI,CAAChH,OAAL,CAAa8F,IAAb,CAAkBgJ,KAAlB,EAAyB7N,KAAzB;EACD,KANS;;;EASV,QAAIkN,KAAK,GAAGV,WAAZ,EAAyB;EACvB3H,MAAAA,IAAI,CAACkB,IAAI,CAACxG,OAAL,CAAasO,KAAb,GAAqBZ,QAAQ,CAACC,KAAD,CAA9B,CAAJ;EACD;;EAEDrI,IAAAA,IAAI,CAACkB,IAAI,CAACxG,OAAL,CAAasO,KAAd,CAAJ,CAbU;;EAeV,QAAI7N,KAAK,CAAC8N,eAAV,EAA2B;EAAE;EAC3BjJ,MAAAA,IAAI,CAAC7E,KAAK,CAAC8N,eAAP,CAAJ;EACD,KAjBS;;;EAoBV,QAAIZ,KAAK,IAAIV,WAAb,EAA0B;EACxB3H,MAAAA,IAAI,CAACkB,IAAI,CAACxG,OAAL,CAAasO,KAAb,GAAqBZ,QAAQ,CAACC,KAAD,CAA9B,CAAJ;EACD;EACF;EAED;;;;;;;;;WAOAa,2BAAQ/N,OAAO;EACb,QAAI,KAAKgO,OAAL,EAAJ,EAAoB;EAClB,aAAO,KAAKnJ,IAAL,CAAU7E,KAAV,CAAP;EACD,KAHY;;;EAKb,SAAKkN,KAAL,GAAaP,YAAb;EACD;EAED;;;;;;;WAKAqB,6BAAU;EACR,QAAI9S,CAAC,GAAG,CAAR;;EACA,WAAOA,CAAC,GAAG,KAAKoS,WAAL,CAAiB3T,MAA5B,EAAoC;EAClC,UAAI,EAAE,KAAK2T,WAAL,CAAiBpS,CAAjB,EAAoBgS,KAApB,IAA6BP,YAAY,GAAGN,cAA5C,CAAF,CAAJ,EAAoE;EAClE,eAAO,KAAP;EACD;;EACDnR,MAAAA,CAAC;EACF;;EACD,WAAO,IAAP;EACD;EAED;;;;;;;WAKA4J,+BAAU2G,WAAW;EACnB;EACA;EACA,QAAIwC,cAAc,GAAG9U,QAAM,CAAC,EAAD,EAAKsS,SAAL,CAA3B,CAHmB;;EAMnB,QAAI,CAACtN,QAAQ,CAAC,KAAKoB,OAAL,CAAaI,MAAd,EAAsB,CAAC,IAAD,EAAOsO,cAAP,CAAtB,CAAb,EAA4D;EAC1D,WAAKC,KAAL;EACA,WAAKhB,KAAL,GAAaP,YAAb;EACA;EACD,KAVkB;;;EAanB,QAAI,KAAKO,KAAL,IAAcT,gBAAgB,GAAGC,eAAnB,GAAqCC,YAAnD,CAAJ,EAAsE;EACpE,WAAKO,KAAL,GAAab,cAAb;EACD;;EAED,SAAKa,KAAL,GAAa,KAAKiB,OAAL,CAAaF,cAAb,CAAb,CAjBmB;EAoBnB;;EACA,QAAI,KAAKf,KAAL,IAAcZ,WAAW,GAAGC,aAAd,GAA8BC,WAA9B,GAA4CE,eAA1D,CAAJ,EAAgF;EAC9E,WAAKqB,OAAL,CAAaE,cAAb;EACD;EACF;EAED;;;;;;;;;EASA;;;WACAE,2BAAQ1C,WAAW;EACnB;;EAEA;;;;;;;;WAMA5L,2CAAiB;EAEjB;;;;;;;;WAMAqO,yBAAQ;;;;;EChSV;;;;;;;;;;;;MAWqBE;;;;;EACnB,yBAAY7O,OAAZ,EAA0B;EAAA;;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EACxB;EACEsO,MAAAA,KAAK,EAAE,KADT;EAEEpN,MAAAA,QAAQ,EAAE,CAFZ;EAGE4N,MAAAA,IAAI,EAAE,CAHR;EAIEC,MAAAA,QAAQ,EAAE,GAJZ;EAIiB;EACfC,MAAAA,IAAI,EAAE,GALR;EAKa;EACXC,MAAAA,SAAS,EAAE,CANb;EAMgB;EACdC,MAAAA,YAAY,EAAE;EAPhB,OAQKlP,OARL,WADwB;EAaxB;;EACA,UAAKmP,KAAL,GAAa,KAAb;EACA,UAAKC,OAAL,GAAe,KAAf;EAEA,UAAKC,MAAL,GAAc,IAAd;EACA,UAAKC,MAAL,GAAc,IAAd;EACA,UAAKC,KAAL,GAAa,CAAb;EAnBwB;EAoBzB;;;;WAEDjP,2CAAiB;EACf,WAAO,CAAC7D,yBAAD,CAAP;EACD;;WAEDmS,2BAAQnO,OAAO;EAAA;;EAAA,QACPT,OADO,GACK,IADL,CACPA,OADO;EAGb,QAAIwP,aAAa,GAAG/O,KAAK,CAACS,QAAN,CAAe9G,MAAf,KAA0B4F,OAAO,CAACkB,QAAtD;EACA,QAAIuO,aAAa,GAAGhP,KAAK,CAACW,QAAN,GAAiBpB,OAAO,CAACiP,SAA7C;EACA,QAAIS,cAAc,GAAGjP,KAAK,CAACa,SAAN,GAAkBtB,OAAO,CAACgP,IAA/C;EAEA,SAAKL,KAAL;;EAEA,QAAKlO,KAAK,CAAC4C,SAAN,GAAkB3F,WAAnB,IAAoC,KAAK6R,KAAL,KAAe,CAAvD,EAA2D;EACzD,aAAO,KAAKI,WAAL,EAAP;EACD,KAXY;EAcb;;;EACA,QAAIF,aAAa,IAAIC,cAAjB,IAAmCF,aAAvC,EAAsD;EACpD,UAAI/O,KAAK,CAAC4C,SAAN,KAAoBzF,SAAxB,EAAmC;EACjC,eAAO,KAAK+R,WAAL,EAAP;EACD;;EAED,UAAIC,aAAa,GAAG,KAAKT,KAAL,GAAc1O,KAAK,CAAC0B,SAAN,GAAkB,KAAKgN,KAAvB,GAA+BnP,OAAO,CAAC+O,QAArD,GAAiE,IAArF;EACA,UAAIc,aAAa,GAAG,CAAC,KAAKT,OAAN,IAAiB7M,WAAW,CAAC,KAAK6M,OAAN,EAAe3O,KAAK,CAAC2B,MAArB,CAAX,GAA0CpC,OAAO,CAACkP,YAAvF;EAEA,WAAKC,KAAL,GAAa1O,KAAK,CAAC0B,SAAnB;EACA,WAAKiN,OAAL,GAAe3O,KAAK,CAAC2B,MAArB;;EAEA,UAAI,CAACyN,aAAD,IAAkB,CAACD,aAAvB,EAAsC;EACpC,aAAKL,KAAL,GAAa,CAAb;EACD,OAFD,MAEO;EACL,aAAKA,KAAL,IAAc,CAAd;EACD;;EAED,WAAKD,MAAL,GAAc7O,KAAd,CAjBoD;EAoBpD;;EACA,UAAIqP,QAAQ,GAAG,KAAKP,KAAL,GAAavP,OAAO,CAAC8O,IAApC;;EACA,UAAIgB,QAAQ,KAAK,CAAjB,EAAoB;EAClB;EACA;EACA,YAAI,CAAC,KAAK1B,kBAAL,EAAL,EAAgC;EAC9B,iBAAOlB,gBAAP;EACD,SAFD,MAEO;EACL,eAAKmC,MAAL,GAAc3D,UAAU,CAAC,YAAM;EAC7B,YAAA,MAAI,CAACiC,KAAL,GAAaT,gBAAb;;EACA,YAAA,MAAI,CAACsB,OAAL;EACD,WAHuB,EAGrBxO,OAAO,CAAC+O,QAHa,CAAxB;EAIA,iBAAOhC,WAAP;EACD;EACF;EACF;;EACD,WAAOK,YAAP;EACD;;WAEDuC,qCAAc;EAAA;;EACZ,SAAKN,MAAL,GAAc3D,UAAU,CAAC,YAAM;EAC7B,MAAA,MAAI,CAACiC,KAAL,GAAaP,YAAb;EACD,KAFuB,EAErB,KAAKpN,OAAL,CAAa+O,QAFQ,CAAxB;EAGA,WAAO3B,YAAP;EACD;;WAEDuB,yBAAQ;EACNoB,IAAAA,YAAY,CAAC,KAAKV,MAAN,CAAZ;EACD;;WAED/J,uBAAO;EACL,QAAI,KAAKqI,KAAL,KAAeT,gBAAnB,EAAqC;EACnC,WAAKoC,MAAL,CAAYQ,QAAZ,GAAuB,KAAKP,KAA5B;EACA,WAAK/P,OAAL,CAAa8F,IAAb,CAAkB,KAAKtF,OAAL,CAAasO,KAA/B,EAAsC,KAAKgB,MAA3C;EACD;EACF;;;IAjGwC1B;;ECR3C;;;;;;;MAMqBoC;;;;;EACnB,0BAAYhQ,OAAZ,EAA0B;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EAAA,WACxB;EACEkB,MAAAA,QAAQ,EAAE;EADZ,OAEKlB,OAFL,EADwB;EAKzB;EAED;;;;;;;;;;;WAOAiQ,6BAASxP,OAAO;EACd,QAAIyP,cAAc,GAAG,KAAKlQ,OAAL,CAAakB,QAAlC;EACA,WAAOgP,cAAc,KAAK,CAAnB,IAAwBzP,KAAK,CAACS,QAAN,CAAe9G,MAAf,KAA0B8V,cAAzD;EACD;EAED;;;;;;;;;WAOAtB,2BAAQnO,OAAO;EAAA,QACPkN,KADO,GACG,IADH,CACPA,KADO;EAAA,QAEPtK,SAFO,GAEO5C,KAFP,CAEP4C,SAFO;EAIb,QAAI8M,YAAY,GAAGxC,KAAK,IAAIZ,WAAW,GAAGC,aAAlB,CAAxB;EACA,QAAIoD,OAAO,GAAG,KAAKH,QAAL,CAAcxP,KAAd,CAAd,CALa;;EAQb,QAAI0P,YAAY,KAAK9M,SAAS,GAAGxF,YAAZ,IAA4B,CAACuS,OAAlC,CAAhB,EAA4D;EAC1D,aAAOzC,KAAK,GAAGR,eAAf;EACD,KAFD,MAEO,IAAIgD,YAAY,IAAIC,OAApB,EAA6B;EAClC,UAAI/M,SAAS,GAAGzF,SAAhB,EAA2B;EACzB,eAAO+P,KAAK,GAAGV,WAAf;EACD,OAFD,MAEO,IAAI,EAAEU,KAAK,GAAGZ,WAAV,CAAJ,EAA4B;EACjC,eAAOA,WAAP;EACD;;EACD,aAAOY,KAAK,GAAGX,aAAf;EACD;;EACD,WAAOI,YAAP;EACD;;;IA9CyCQ;;ECZ5C;;;;;;;AAMA,EAAe,SAASyC,YAAT,CAAsB1P,SAAtB,EAAiC;EAC9C,MAAIA,SAAS,KAAKzC,cAAlB,EAAkC;EAChC,WAAO,MAAP;EACD,GAFD,MAEO,IAAIyC,SAAS,KAAK1C,YAAlB,EAAgC;EACrC,WAAO,IAAP;EACD,GAFM,MAEA,IAAI0C,SAAS,KAAK5C,cAAlB,EAAkC;EACvC,WAAO,MAAP;EACD,GAFM,MAEA,IAAI4C,SAAS,KAAK3C,eAAlB,EAAmC;EACxC,WAAO,OAAP;EACD;;EACD,SAAO,EAAP;EACD;;ECTD;;;;;;;;MAOqBsS;;;;;EACnB,yBAAYtQ,OAAZ,EAA0B;EAAA;;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EACxB;EACEsO,MAAAA,KAAK,EAAE,KADT;EAEEW,MAAAA,SAAS,EAAE,EAFb;EAGE/N,MAAAA,QAAQ,EAAE,CAHZ;EAIEP,MAAAA,SAAS,EAAEtC;EAJb,OAKK2B,OALL;EAOA,UAAKuQ,EAAL,GAAU,IAAV;EACA,UAAKC,EAAL,GAAU,IAAV;EATwB;EAUzB;;;;WAEDlQ,2CAAiB;EAAA,QACCK,SADD,GACiB,IADjB,CACTX,OADS,CACCW,SADD;EAEf,QAAIvB,OAAO,GAAG,EAAd;;EACA,QAAIuB,SAAS,GAAGxC,oBAAhB,EAAsC;EACpCiB,MAAAA,OAAO,CAACwJ,IAAR,CAAahM,kBAAb;EACD;;EACD,QAAI+D,SAAS,GAAGvC,kBAAhB,EAAoC;EAClCgB,MAAAA,OAAO,CAACwJ,IAAR,CAAajM,kBAAb;EACD;;EACD,WAAOyC,OAAP;EACD;;WAEDqR,uCAAchQ,OAAO;EAAA,QACbT,OADa,GACD,IADC,CACbA,OADa;EAEnB,QAAI0Q,QAAQ,GAAG,IAAf;EAFmB,QAGbtP,QAHa,GAGAX,KAHA,CAGbW,QAHa;EAAA,QAIbT,SAJa,GAICF,KAJD,CAIbE,SAJa;EAKnB,QAAImB,CAAC,GAAGrB,KAAK,CAAC4B,MAAd;EACA,QAAIL,CAAC,GAAGvB,KAAK,CAAC6B,MAAd,CANmB;;EASnB,QAAI,EAAE3B,SAAS,GAAGX,OAAO,CAACW,SAAtB,CAAJ,EAAsC;EACpC,UAAIX,OAAO,CAACW,SAAR,GAAoBxC,oBAAxB,EAA8C;EAC5CwC,QAAAA,SAAS,GAAImB,CAAC,KAAK,CAAP,GAAYhE,cAAZ,GAA8BgE,CAAC,GAAG,CAAL,GAAU/D,cAAV,GAA2BC,eAApE;EACA0S,QAAAA,QAAQ,GAAG5O,CAAC,KAAK,KAAKyO,EAAtB;EACAnP,QAAAA,QAAQ,GAAGrG,IAAI,CAACC,GAAL,CAASyF,KAAK,CAAC4B,MAAf,CAAX;EACD,OAJD,MAIO;EACL1B,QAAAA,SAAS,GAAIqB,CAAC,KAAK,CAAP,GAAYlE,cAAZ,GAA8BkE,CAAC,GAAG,CAAL,GAAU/D,YAAV,GAAyBC,cAAlE;EACAwS,QAAAA,QAAQ,GAAG1O,CAAC,KAAK,KAAKwO,EAAtB;EACApP,QAAAA,QAAQ,GAAGrG,IAAI,CAACC,GAAL,CAASyF,KAAK,CAAC6B,MAAf,CAAX;EACD;EACF;;EACD7B,IAAAA,KAAK,CAACE,SAAN,GAAkBA,SAAlB;EACA,WAAO+P,QAAQ,IAAItP,QAAQ,GAAGpB,OAAO,CAACiP,SAA/B,IAA4CtO,SAAS,GAAGX,OAAO,CAACW,SAAvE;EACD;;WAEDsP,6BAASxP,OAAO;EACd,WAAOuP,cAAc,CAAC/H,SAAf,CAAyBgI,QAAzB,CAAkCtR,IAAlC,CAAuC,IAAvC,EAA6C8B,KAA7C;EACF,SAAKkN,KAAL,GAAaZ,WAAb,IAA6B,EAAE,KAAKY,KAAL,GAAaZ,WAAf,KAA+B,KAAK0D,aAAL,CAAmBhQ,KAAnB,CAD1D,CAAP;EAED;;WAED6E,qBAAK7E,OAAO;EAEV,SAAK8P,EAAL,GAAU9P,KAAK,CAAC4B,MAAhB;EACA,SAAKmO,EAAL,GAAU/P,KAAK,CAAC6B,MAAhB;EAEA,QAAI3B,SAAS,GAAG0P,YAAY,CAAC5P,KAAK,CAACE,SAAP,CAA5B;;EAEA,QAAIA,SAAJ,EAAe;EACbF,MAAAA,KAAK,CAAC8N,eAAN,GAAwB,KAAKvO,OAAL,CAAasO,KAAb,GAAqB3N,SAA7C;EACD;;EACD,8BAAM2E,IAAN,YAAW7E,KAAX;EACD;;;IAjEwCuP;;ECf3C;;;;;;;;MAOqBW;;;;;EACnB,2BAAY3Q,OAAZ,EAA0B;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EAAA,WACxB;EACEsO,MAAAA,KAAK,EAAE,OADT;EAEEW,MAAAA,SAAS,EAAE,EAFb;EAGEnL,MAAAA,QAAQ,EAAE,GAHZ;EAIEnD,MAAAA,SAAS,EAAExC,oBAAoB,GAAGC,kBAJpC;EAKE8C,MAAAA,QAAQ,EAAE;EALZ,OAMKlB,OANL,EADwB;EASzB;;;;WAEDM,2CAAiB;EACf,WAAOgQ,aAAa,CAACrI,SAAd,CAAwB3H,cAAxB,CAAuC3B,IAAvC,CAA4C,IAA5C,CAAP;EACD;;WAEDsR,6BAASxP,OAAO;EAAA,QACRE,SADQ,GACM,KAAKX,OADX,CACRW,SADQ;EAEd,QAAImD,QAAJ;;EAEA,QAAInD,SAAS,IAAIxC,oBAAoB,GAAGC,kBAA3B,CAAb,EAA6D;EAC3D0F,MAAAA,QAAQ,GAAGrD,KAAK,CAAC8D,eAAjB;EACD,KAFD,MAEO,IAAI5D,SAAS,GAAGxC,oBAAhB,EAAsC;EAC3C2F,MAAAA,QAAQ,GAAGrD,KAAK,CAAC+D,gBAAjB;EACD,KAFM,MAEA,IAAI7D,SAAS,GAAGvC,kBAAhB,EAAoC;EACzC0F,MAAAA,QAAQ,GAAGrD,KAAK,CAACgE,gBAAjB;EACD;;EAED,WAAO,0BAAMwL,QAAN,YAAexP,KAAf,KACHE,SAAS,GAAGF,KAAK,CAACG,eADf,IAEHH,KAAK,CAACW,QAAN,GAAiB,KAAKpB,OAAL,CAAaiP,SAF3B,IAGHxO,KAAK,CAACmE,WAAN,KAAsB,KAAK5E,OAAL,CAAakB,QAHhC,IAIHlG,GAAG,CAAC8I,QAAD,CAAH,GAAgB,KAAK9D,OAAL,CAAa8D,QAJ1B,IAIsCrD,KAAK,CAAC4C,SAAN,GAAkBzF,SAJ/D;EAKD;;WAED0H,qBAAK7E,OAAO;EACV,QAAIE,SAAS,GAAG0P,YAAY,CAAC5P,KAAK,CAACG,eAAP,CAA5B;;EACA,QAAID,SAAJ,EAAe;EACb,WAAKnB,OAAL,CAAa8F,IAAb,CAAkB,KAAKtF,OAAL,CAAasO,KAAb,GAAqB3N,SAAvC,EAAkDF,KAAlD;EACD;;EAED,SAAKjB,OAAL,CAAa8F,IAAb,CAAkB,KAAKtF,OAAL,CAAasO,KAA/B,EAAsC7N,KAAtC;EACD;;;IA1C0CuP;;ECV7C;;;;;;;;MAOqBY;;;;;EACnB,2BAAY5Q,OAAZ,EAA0B;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EAAA,WACxB;EACEsO,MAAAA,KAAK,EAAE,OADT;EAEEW,MAAAA,SAAS,EAAE,CAFb;EAGE/N,MAAAA,QAAQ,EAAE;EAHZ,OAIKlB,OAJL,EADwB;EAOzB;;;;WAEDM,2CAAiB;EACf,WAAO,CAAC5D,iBAAD,CAAP;EACD;;WAEDuT,6BAASxP,OAAO;EACd,WAAO,0BAAMwP,QAAN,YAAexP,KAAf,MACF1F,IAAI,CAACC,GAAL,CAASyF,KAAK,CAACiE,KAAN,GAAc,CAAvB,IAA4B,KAAK1E,OAAL,CAAaiP,SAAzC,IAAsD,KAAKtB,KAAL,GAAaZ,WADjE,CAAP;EAED;;WAEDzH,qBAAK7E,OAAO;EACV,QAAIA,KAAK,CAACiE,KAAN,KAAgB,CAApB,EAAuB;EACrB,UAAImM,KAAK,GAAGpQ,KAAK,CAACiE,KAAN,GAAc,CAAd,GAAkB,IAAlB,GAAyB,KAArC;EACAjE,MAAAA,KAAK,CAAC8N,eAAN,GAAwB,KAAKvO,OAAL,CAAasO,KAAb,GAAqBuC,KAA7C;EACD;;EACD,8BAAMvL,IAAN,YAAW7E,KAAX;EACD;;;IAzB0CuP;;ECP7C;;;;;;;;MAOqBc;;;;;EACnB,4BAAY9Q,OAAZ,EAA0B;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EAAA,WACxB;EACEsO,MAAAA,KAAK,EAAE,QADT;EAEEW,MAAAA,SAAS,EAAE,CAFb;EAGE/N,MAAAA,QAAQ,EAAE;EAHZ,OAIKlB,OAJL,EADwB;EAOzB;;;;WAEDM,2CAAiB;EACf,WAAO,CAAC5D,iBAAD,CAAP;EACD;;WAEDuT,6BAASxP,OAAO;EACd,WAAO,0BAAMwP,QAAN,YAAexP,KAAf,MACF1F,IAAI,CAACC,GAAL,CAASyF,KAAK,CAACkE,QAAf,IAA2B,KAAK3E,OAAL,CAAaiP,SAAxC,IAAqD,KAAKtB,KAAL,GAAaZ,WADhE,CAAP;EAED;;;IAjB2CiD;;ECE9C;;;;;;;;MAOqBe;;;;;EACnB,2BAAY/Q,OAAZ,EAA0B;EAAA;;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EACxB;EACEsO,MAAAA,KAAK,EAAE,OADT;EAEEpN,MAAAA,QAAQ,EAAE,CAFZ;EAGE8N,MAAAA,IAAI,EAAE,GAHR;EAGa;EACXC,MAAAA,SAAS,EAAE;EAJb,OAKKjP,OALL;EAOA,UAAKqP,MAAL,GAAc,IAAd;EACA,UAAKC,MAAL,GAAc,IAAd;EATwB;EAUzB;;;;WAEDhP,2CAAiB;EACf,WAAO,CAAC9D,iBAAD,CAAP;EACD;;WAEDoS,2BAAQnO,OAAO;EAAA;;EAAA,QACPT,OADO,GACK,IADL,CACPA,OADO;EAEb,QAAIwP,aAAa,GAAG/O,KAAK,CAACS,QAAN,CAAe9G,MAAf,KAA0B4F,OAAO,CAACkB,QAAtD;EACA,QAAIuO,aAAa,GAAGhP,KAAK,CAACW,QAAN,GAAiBpB,OAAO,CAACiP,SAA7C;EACA,QAAI+B,SAAS,GAAGvQ,KAAK,CAACa,SAAN,GAAkBtB,OAAO,CAACgP,IAA1C;EAEA,SAAKM,MAAL,GAAc7O,KAAd,CANa;EASb;;EACA,QAAI,CAACgP,aAAD,IAAkB,CAACD,aAAnB,IAAqC/O,KAAK,CAAC4C,SAAN,IAAmBzF,SAAS,GAAGC,YAA/B,KAAgD,CAACmT,SAA1F,EAAsG;EACpG,WAAKrC,KAAL;EACD,KAFD,MAEO,IAAIlO,KAAK,CAAC4C,SAAN,GAAkB3F,WAAtB,EAAmC;EACxC,WAAKiR,KAAL;EACA,WAAKU,MAAL,GAAc3D,UAAU,CAAC,YAAM;EAC7B,QAAA,MAAI,CAACiC,KAAL,GAAaT,gBAAb;;EACA,QAAA,MAAI,CAACsB,OAAL;EACD,OAHuB,EAGrBxO,OAAO,CAACgP,IAHa,CAAxB;EAID,KANM,MAMA,IAAIvO,KAAK,CAAC4C,SAAN,GAAkBzF,SAAtB,EAAiC;EACtC,aAAOsP,gBAAP;EACD;;EACD,WAAOE,YAAP;EACD;;WAEDuB,yBAAQ;EACNoB,IAAAA,YAAY,CAAC,KAAKV,MAAN,CAAZ;EACD;;WAED/J,qBAAK7E,OAAO;EACV,QAAI,KAAKkN,KAAL,KAAeT,gBAAnB,EAAqC;EACnC;EACD;;EAED,QAAIzM,KAAK,IAAKA,KAAK,CAAC4C,SAAN,GAAkBzF,SAAhC,EAA4C;EAC1C,WAAK4B,OAAL,CAAa8F,IAAb,CAAqB,KAAKtF,OAAL,CAAasO,KAAlC,SAA6C7N,KAA7C;EACD,KAFD,MAEO;EACL,WAAK6O,MAAL,CAAYnN,SAAZ,GAAwBlH,GAAG,EAA3B;EACA,WAAKuE,OAAL,CAAa8F,IAAb,CAAkB,KAAKtF,OAAL,CAAasO,KAA/B,EAAsC,KAAKgB,MAA3C;EACD;EACF;;;IAxD0C1B;;ACX7C,iBAAe;EACd;;;;;;;EAOAqD,EAAAA,SAAS,EAAE,KARG;;EAUd;;;;;;;EAOAhR,EAAAA,WAAW,EAAE1D,oBAjBC;;EAmBd;;;;;EAKA6D,EAAAA,MAAM,EAAE,IAxBM;;EA0Bd;;;;;;;;EAQAqG,EAAAA,WAAW,EAAE,IAlCC;;EAoCd;;;;;;EAMAgG,EAAAA,UAAU,EAAE,IA1CE;;EA4Cd;;;;;;EAMAyE,EAAAA,QAAQ,EAAE;EACT;;;;;;EAMAC,IAAAA,UAAU,EAAE,MAPH;;EAST;;;;;;EAMAC,IAAAA,WAAW,EAAE,MAfJ;;EAiBT;;;;;;;;EAQAC,IAAAA,YAAY,EAAE,MAzBL;;EA2BT;;;;;;EAMAC,IAAAA,cAAc,EAAE,MAjCP;;EAmCT;;;;;;EAMAC,IAAAA,QAAQ,EAAE,MAzCD;;EA2CT;;;;;;;EAOAC,IAAAA,iBAAiB,EAAE;EAlDV;EAlDI,CAAf;EAwGA;;;;;;;;AAOA,EAAO,IAAMC,MAAM,GAAG,CACpB,CAACX,gBAAD,EAAmB;EAAE1Q,EAAAA,MAAM,EAAE;EAAV,CAAnB,CADoB,EAEpB,CAACwQ,eAAD,EAAkB;EAAExQ,EAAAA,MAAM,EAAE;EAAV,CAAlB,EAAqC,CAAC,QAAD,CAArC,CAFoB,EAGpB,CAACuQ,eAAD,EAAkB;EAAEhQ,EAAAA,SAAS,EAAExC;EAAb,CAAlB,CAHoB,EAIpB,CAACmS,aAAD,EAAgB;EAAE3P,EAAAA,SAAS,EAAExC;EAAb,CAAhB,EAAqD,CAAC,OAAD,CAArD,CAJoB,EAKpB,CAAC0Q,aAAD,CALoB,EAMpB,CAACA,aAAD,EAAgB;EAAEP,EAAAA,KAAK,EAAE,WAAT;EAAsBQ,EAAAA,IAAI,EAAE;EAA5B,CAAhB,EAAiD,CAAC,KAAD,CAAjD,CANoB,EAOpB,CAACiC,eAAD,CAPoB,CAAf;;ECvGP,IAAMW,IAAI,GAAG,CAAb;EACA,IAAMC,WAAW,GAAG,CAApB;EAGA;;;;;;;EAMA,SAASC,cAAT,CAAwBpS,OAAxB,EAAiCqS,GAAjC,EAAsC;EAAA,MAC5BjS,OAD4B,GAChBJ,OADgB,CAC5BI,OAD4B;;EAGpC,MAAI,CAACA,OAAO,CAACjF,KAAb,EAAoB;EAClB;EACD;;EACD,MAAIY,IAAJ;EAEAiD,EAAAA,IAAI,CAACgB,OAAO,CAACQ,OAAR,CAAgBkR,QAAjB,EAA2B,UAACzR,KAAD,EAAQqS,IAAR,EAAiB;EAC9CvW,IAAAA,IAAI,GAAGJ,QAAQ,CAACyE,OAAO,CAACjF,KAAT,EAAgBmX,IAAhB,CAAf;;EACA,QAAID,GAAJ,EAAS;EACPrS,MAAAA,OAAO,CAACuS,WAAR,CAAoBxW,IAApB,IAA4BqE,OAAO,CAACjF,KAAR,CAAcY,IAAd,CAA5B;EACAqE,MAAAA,OAAO,CAACjF,KAAR,CAAcY,IAAd,IAAsBkE,KAAtB;EACD,KAHD,MAGO;EACLG,MAAAA,OAAO,CAACjF,KAAR,CAAcY,IAAd,IAAsBiE,OAAO,CAACuS,WAAR,CAAoBxW,IAApB,KAA6B,EAAnD;EACD;EACF,GARG,CAAJ;;EASA,MAAI,CAACsW,GAAL,EAAU;EACRrS,IAAAA,OAAO,CAACuS,WAAR,GAAsB,EAAtB;EACD;EACF;EAED;;;;;;;;EAMA,SAASC,eAAT,CAAyB1D,KAAzB,EAAgC2D,IAAhC,EAAsC;EACpC,MAAMC,YAAY,GAAGxX,QAAQ,CAACyX,WAAT,CAAqB,OAArB,CAArB;EAEAD,EAAAA,YAAY,CAACE,SAAb,CAAuB9D,KAAvB,EAA8B,IAA9B,EAAoC,IAApC;EACA4D,EAAAA,YAAY,CAACG,OAAb,GAAuBJ,IAAvB;EACAA,EAAAA,IAAI,CAACnY,MAAL,CAAYwY,aAAZ,CAA0BJ,YAA1B;EACD;EAGD;;;;;;;;;MAOqBK;;;EACnB,mBAAY3S,OAAZ,EAAqBI,OAArB,EAA8B;EAAA;;EAC5B,SAAKA,OAAL,GAAepG,QAAM,CAAC,EAAD,EAAK4Y,QAAL,EAAexS,OAAO,IAAI,EAA1B,CAArB;EAEA,SAAKA,OAAL,CAAayG,WAAb,GAA2B,KAAKzG,OAAL,CAAayG,WAAb,IAA4B7G,OAAvD;EAEA,SAAK6S,QAAL,GAAgB,EAAhB;EACA,SAAK5R,OAAL,GAAe,EAAf;EACA,SAAKX,WAAL,GAAmB,EAAnB;EACA,SAAK6R,WAAL,GAAmB,EAAnB;EAEA,SAAKnS,OAAL,GAAeA,OAAf;EACA,SAAKa,KAAL,GAAa8L,mBAAmB,CAAC,IAAD,CAAhC;EACA,SAAKtM,WAAL,GAAmB,IAAIV,WAAJ,CAAgB,IAAhB,EAAsB,KAAKS,OAAL,CAAaC,WAAnC,CAAnB;EAEA2R,IAAAA,cAAc,CAAC,IAAD,EAAO,IAAP,CAAd;EAEApT,IAAAA,IAAI,CAAC,KAAKwB,OAAL,CAAaE,WAAd,EAA2B,UAAAwS,IAAI,EAAI;EACrC,UAAMvS,UAAU,GAAG,KAAI,CAAC0R,GAAL,CAAS,IAAKa,IAAI,CAAC,CAAD,CAAT,CAAcA,IAAI,CAAC,CAAD,CAAlB,CAAT,CAAnB;;EAEAA,MAAAA,IAAI,CAAC,CAAD,CAAJ,IAAWvS,UAAU,CAAC6N,aAAX,CAAyB0E,IAAI,CAAC,CAAD,CAA7B,CAAX;EACAA,MAAAA,IAAI,CAAC,CAAD,CAAJ,IAAWvS,UAAU,CAAC+N,cAAX,CAA0BwE,IAAI,CAAC,CAAD,CAA9B,CAAX;EACD,KALG,EAKD,IALC,CAAJ;EAMD;EAEF;;;;;;;;;;WAMChT,mBAAIM,SAAS;EACXpG,IAAAA,QAAM,CAAC,KAAKoG,OAAN,EAAeA,OAAf,CAAN,CADW;;EAIX,QAAIA,OAAO,CAACC,WAAZ,EAAyB;EACvB,WAAKA,WAAL,CAAiBF,MAAjB;EACD;;EACD,QAAIC,OAAO,CAACyG,WAAZ,EAAyB;EACvB;EACA,WAAKhG,KAAL,CAAWuG,OAAX;EACA,WAAKvG,KAAL,CAAW3G,MAAX,GAAoBkG,OAAO,CAACyG,WAA5B;EACA,WAAKhG,KAAL,CAAWmG,IAAX;EACD;;EACD,WAAO,IAAP;EACD;EAEF;;;;;;;;;WAOC+L,qBAAKC,OAAO;EACV,SAAK/R,OAAL,CAAagS,OAAb,GAAuBD,KAAK,GAAGjB,WAAH,GAAiBD,IAA7C;EACD;EAEF;;;;;;;;;WAOCnM,+BAAU2G,WAAW;EAAA,QACXrL,OADW,GACC,IADD,CACXA,OADW;;EAGnB,QAAIA,OAAO,CAACgS,OAAZ,EAAqB;EACnB;EACD,KALkB;;;EAQnB,SAAK5S,WAAL,CAAiBO,eAAjB,CAAiC0L,SAAjC;EAEA,QAAI/L,UAAJ;EAVmB,QAWXD,WAXW,GAWK,IAXL,CAWXA,WAXW;EAcnB;EACA;;EAfmB,QAgBb4S,aAhBa,GAgBKjS,OAhBL,CAgBbiS,aAhBa;EAmBnB;;EACA,QAAI,CAACA,aAAD,IAAmBA,aAAa,IAAIA,aAAa,CAACnF,KAAd,GAAsBT,gBAA9D,EAAiF;EAC/ErM,MAAAA,OAAO,CAACiS,aAAR,GAAwB,IAAxB;EACAA,MAAAA,aAAa,GAAG,IAAhB;EACD;;EAED,QAAInX,CAAC,GAAG,CAAR;;EAEA,WAAOA,CAAC,GAAGuE,WAAW,CAAC9F,MAAvB,EAA+B;EAC7B+F,MAAAA,UAAU,GAAGD,WAAW,CAACvE,CAAD,CAAxB,CAD6B;EAI7B;EACA;EACA;EACA;EACA;;EACA,UAAIkF,OAAO,CAACgS,OAAR,KAAoBlB,WAApB;EACF,OAACmB,aAAD,IAAkB3S,UAAU,KAAK2S,aAAjC;EACA3S,MAAAA,UAAU,CAACkO,gBAAX,CAA4ByE,aAA5B,CAFE,CAAJ,EAE+C;EAAE;EAC/C3S,QAAAA,UAAU,CAACoF,SAAX,CAAqB2G,SAArB;EACD,OAJD,MAIO;EACL/L,QAAAA,UAAU,CAACwO,KAAX;EACD,OAf4B;EAkB7B;;;EACA,UAAI,CAACmE,aAAD,IAAkB3S,UAAU,CAACwN,KAAX,IAAoBZ,WAAW,GAAGC,aAAd,GAA8BC,WAAlD,CAAtB,EAAsF;EACpFpM,QAAAA,OAAO,CAACiS,aAAR,GAAwB3S,UAAxB;EACA2S,QAAAA,aAAa,GAAG3S,UAAhB;EACD;;EACDxE,MAAAA,CAAC;EACF;EACF;EAEF;;;;;;;;WAMC8R,mBAAItN,YAAY;EACd,QAAIA,UAAU,YAAYyN,UAA1B,EAAsC;EACpC,aAAOzN,UAAP;EACD;;EAHa,QAKND,WALM,GAKU,IALV,CAKNA,WALM;;EAOd,SAAK,IAAIvE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuE,WAAW,CAAC9F,MAAhC,EAAwCuB,CAAC,EAAzC,EAA6C;EAC3C,UAAIuE,WAAW,CAACvE,CAAD,CAAX,CAAeqE,OAAf,CAAuBsO,KAAvB,KAAiCnO,UAArC,EAAiD;EAC/C,eAAOD,WAAW,CAACvE,CAAD,CAAlB;EACD;EACF;;EACD,WAAO,IAAP;EACD;EAEF;;;;;;;;WAMCkW,mBAAI1R,YAAY;EACd,QAAIuM,cAAc,CAACvM,UAAD,EAAa,KAAb,EAAoB,IAApB,CAAlB,EAA6C;EAC3C,aAAO,IAAP;EACD,KAHa;;;EAMd,QAAM4S,QAAQ,GAAG,KAAKtF,GAAL,CAAStN,UAAU,CAACH,OAAX,CAAmBsO,KAA5B,CAAjB;;EAEA,QAAIyE,QAAJ,EAAc;EACZ,WAAKC,MAAL,CAAYD,QAAZ;EACD;;EAED,SAAK7S,WAAL,CAAiB0I,IAAjB,CAAsBzI,UAAtB;EACAA,IAAAA,UAAU,CAACX,OAAX,GAAqB,IAArB;EAEA,SAAKS,WAAL,CAAiBF,MAAjB;EACA,WAAOI,UAAP;EACD;EAEF;;;;;;;;WAMC6S,yBAAO7S,YAAY;EACjB,QAAIuM,cAAc,CAACvM,UAAD,EAAa,QAAb,EAAuB,IAAvB,CAAlB,EAAgD;EAC9C,aAAO,IAAP;EACD;;EAED,QAAM8S,gBAAgB,GAAG,KAAKxF,GAAL,CAAStN,UAAT,CAAzB,CALiB;;EAQjB,QAAIA,UAAJ,EAAgB;EAAA,UACND,WADM,GACU,IADV,CACNA,WADM;EAEd,UAAMhG,KAAK,GAAG+M,OAAO,CAAC/G,WAAD,EAAc+S,gBAAd,CAArB;;EAEA,UAAI/Y,KAAK,KAAK,CAAC,CAAf,EAAkB;EAChBgG,QAAAA,WAAW,CAAC2I,MAAZ,CAAmB3O,KAAnB,EAA0B,CAA1B;EACA,aAAK+F,WAAL,CAAiBF,MAAjB;EACD;EACF;;EAED,WAAO,IAAP;EACD;EAEF;;;;;;;;;WAOCmT,iBAAGC,QAAQvN,SAAS;EAClB,QAAIuN,MAAM,KAAKpZ,SAAX,IAAwB6L,OAAO,KAAK7L,SAAxC,EAAmD;EACjD,aAAO,IAAP;EACD;;EAHiB,QAKV0Y,QALU,GAKG,IALH,CAKVA,QALU;EAOlBjU,IAAAA,IAAI,CAACgH,QAAQ,CAAC2N,MAAD,CAAT,EAAmB,UAAA7E,KAAK,EAAI;EAC9BmE,MAAAA,QAAQ,CAACnE,KAAD,CAAR,GAAkBmE,QAAQ,CAACnE,KAAD,CAAR,IAAmB,EAArC;EACAmE,MAAAA,QAAQ,CAACnE,KAAD,CAAR,CAAgB1F,IAAhB,CAAqBhD,OAArB;EACD,KAHG,CAAJ;EAIA,WAAO,IAAP;EACD;EAEF;;;;;;;;WAMCwN,mBAAID,QAAQvN,SAAS;EACnB,QAAIuN,MAAM,KAAKpZ,SAAf,EAA0B;EACxB,aAAO,IAAP;EACD;;EAHkB,QAKX0Y,QALW,GAKE,IALF,CAKXA,QALW;EAOnBjU,IAAAA,IAAI,CAACgH,QAAQ,CAAC2N,MAAD,CAAT,EAAmB,UAAA7E,KAAK,EAAI;EAC9B,UAAI,CAAC1I,OAAL,EAAc;EACZ,eAAO6M,QAAQ,CAACnE,KAAD,CAAf;EACD,OAFD,MAEO;EACLmE,QAAAA,QAAQ,CAACnE,KAAD,CAAR,IAAmBmE,QAAQ,CAACnE,KAAD,CAAR,CAAgBzF,MAAhB,CAAuB5B,OAAO,CAACwL,QAAQ,CAACnE,KAAD,CAAT,EAAkB1I,OAAlB,CAA9B,EAA0D,CAA1D,CAAnB;EACD;EACF,KANG,CAAJ;EAOA,WAAO,IAAP;EACD;EAEF;;;;;;;WAKCN,qBAAKgJ,OAAO2D,MAAM;EAChB;EACA,QAAI,KAAKjS,OAAL,CAAaiR,SAAjB,EAA4B;EAC1Be,MAAAA,eAAe,CAAC1D,KAAD,EAAQ2D,IAAR,CAAf;EACD,KAJe;;;EAOhB,QAAMQ,QAAQ,GAAG,KAAKA,QAAL,CAAcnE,KAAd,KAAwB,KAAKmE,QAAL,CAAcnE,KAAd,EAAqB5S,KAArB,EAAzC;;EAEA,QAAI,CAAC+W,QAAD,IAAa,CAACA,QAAQ,CAACrY,MAA3B,EAAmC;EACjC;EACD;;EAED6X,IAAAA,IAAI,CAACpM,IAAL,GAAYyI,KAAZ;;EACA2D,IAAAA,IAAI,CAAClR,cAAL,GAAsB,YAAY;EAChCkR,MAAAA,IAAI,CAACvR,QAAL,CAAcK,cAAd;EACD,KAFD;;EAIA,QAAIpF,CAAC,GAAG,CAAR;;EAEA,WAAOA,CAAC,GAAG8W,QAAQ,CAACrY,MAApB,EAA4B;EAC1BqY,MAAAA,QAAQ,CAAC9W,CAAD,CAAR,CAAYsW,IAAZ;EACAtW,MAAAA,CAAC;EACF;EACF;EAEF;;;;;;;WAKCqL,6BAAU;EACR,SAAKpH,OAAL,IAAgBgS,cAAc,CAAC,IAAD,EAAO,KAAP,CAA9B;EAEA,SAAKa,QAAL,GAAgB,EAAhB;EACA,SAAK5R,OAAL,GAAe,EAAf;EACA,SAAKJ,KAAL,CAAWuG,OAAX;EACA,SAAKpH,OAAL,GAAe,IAAf;EACD;;;;;ECnVH,IAAMyT,sBAAsB,GAAG;EAC7B7J,EAAAA,UAAU,EAAE9L,WADiB;EAE7B+L,EAAAA,SAAS,EAAE9L,UAFkB;EAG7B+L,EAAAA,QAAQ,EAAE9L,SAHmB;EAI7B+L,EAAAA,WAAW,EAAE9L;EAJgB,CAA/B;EAOA,IAAMyV,0BAA0B,GAAG,YAAnC;EACA,IAAMC,0BAA0B,GAAG,2CAAnC;EAEA;;;;;;;MAMqBC;;;;;EACnB,8BAAc;EAAA;;EACZ,QAAIxL,KAAK,GAAGwL,gBAAgB,CAACvL,SAA7B;EACAD,IAAAA,KAAK,CAAClB,QAAN,GAAiBwM,0BAAjB;EACAtL,IAAAA,KAAK,CAACjB,KAAN,GAAcwM,0BAAd;EAEA,+BAASpZ,SAAT;EACA,UAAKsZ,OAAL,GAAe,KAAf;EANY;EAOb;;;;WAED7N,2BAAQe,IAAI;EACV,QAAId,IAAI,GAAGwN,sBAAsB,CAAC1M,EAAE,CAACd,IAAJ,CAAjC,CADU;;EAIV,QAAIA,IAAI,KAAKnI,WAAb,EAA0B;EACxB,WAAK+V,OAAL,GAAe,IAAf;EACD;;EAED,QAAI,CAAC,KAAKA,OAAV,EAAmB;EACjB;EACD;;EAED,QAAI1J,OAAO,GAAG2J,sBAAsB,CAAC/U,IAAvB,CAA4B,IAA5B,EAAkCgI,EAAlC,EAAsCd,IAAtC,CAAd,CAZU;;EAeV,QAAIA,IAAI,IAAIjI,SAAS,GAAGC,YAAhB,CAAJ,IAAqCkM,OAAO,CAAC,CAAD,CAAP,CAAW3P,MAAX,GAAoB2P,OAAO,CAAC,CAAD,CAAP,CAAW3P,MAA/B,KAA0C,CAAnF,EAAsF;EACpF,WAAKqZ,OAAL,GAAe,KAAf;EACD;;EAED,SAAKlN,QAAL,CAAc,KAAK/G,OAAnB,EAA4BqG,IAA5B,EAAkC;EAChC3E,MAAAA,QAAQ,EAAE6I,OAAO,CAAC,CAAD,CADe;EAEhC5E,MAAAA,eAAe,EAAE4E,OAAO,CAAC,CAAD,CAFQ;EAGhCxB,MAAAA,WAAW,EAAElL,gBAHmB;EAIhCqD,MAAAA,QAAQ,EAAEiG;EAJsB,KAAlC;EAMD;;;IAnC2CL;AAsC9C;EAOA,SAASoN,sBAAT,CAAgC/M,EAAhC,EAAoCd,IAApC,EAA0C;EACxC,MAAI8N,GAAG,GAAG7K,OAAO,CAACnC,EAAE,CAACoD,OAAJ,CAAjB;EACA,MAAI6J,OAAO,GAAG9K,OAAO,CAACnC,EAAE,CAACyD,cAAJ,CAArB;;EAEA,MAAIvE,IAAI,IAAIjI,SAAS,GAAGC,YAAhB,CAAR,EAAuC;EACrC8V,IAAAA,GAAG,GAAG3K,WAAW,CAAC2K,GAAG,CAACtT,MAAJ,CAAWuT,OAAX,CAAD,EAAsB,YAAtB,EAAoC,IAApC,CAAjB;EACD;;EAED,SAAO,CAACD,GAAD,EAAMC,OAAN,CAAP;EACD;;ECjFD;;;;;;;;AAQA,EAAe,SAASC,SAAT,CAAmBC,MAAnB,EAA2BhC,IAA3B,EAAiCiC,OAAjC,EAA0C;EACvD,MAAIC,kBAAkB,2BAAyBlC,IAAzB,UAAkCiC,OAAlC,WAAtB;EACA,SAAO,YAAW;EAChB,QAAIE,CAAC,GAAG,IAAIC,KAAJ,CAAU,iBAAV,CAAR;EACA,QAAIC,KAAK,GAAGF,CAAC,IAAIA,CAAC,CAACE,KAAP,GAAeF,CAAC,CAACE,KAAF,CAAQ7L,OAAR,CAAgB,iBAAhB,EAAmC,EAAnC,EACtBA,OADsB,CACd,aADc,EACC,EADD,EAEtBA,OAFsB,CAEd,4BAFc,EAEgB,gBAFhB,CAAf,GAEmD,qBAF/D;EAIA,QAAI8L,GAAG,GAAGvY,MAAM,CAACwY,OAAP,KAAmBxY,MAAM,CAACwY,OAAP,CAAeC,IAAf,IAAuBzY,MAAM,CAACwY,OAAP,CAAeD,GAAzD,CAAV;;EACA,QAAIA,GAAJ,EAAS;EACPA,MAAAA,GAAG,CAACzV,IAAJ,CAAS9C,MAAM,CAACwY,OAAhB,EAAyBL,kBAAzB,EAA6CG,KAA7C;EACD;;EACD,WAAOL,MAAM,CAAChV,KAAP,CAAa,IAAb,EAAmB3E,SAAnB,CAAP;EACD,GAXD;EAYD;;ECrBD;;;;;;;;;;EASA,IAAMoa,MAAM,GAAGV,SAAS,CAAC,UAACW,IAAD,EAAOtN,GAAP,EAAYuN,KAAZ,EAAsB;EAC7C,MAAIC,IAAI,GAAG7a,MAAM,CAAC6a,IAAP,CAAYxN,GAAZ,CAAX;EACA,MAAIvL,CAAC,GAAG,CAAR;;EACA,SAAOA,CAAC,GAAG+Y,IAAI,CAACta,MAAhB,EAAwB;EACtB,QAAI,CAACqa,KAAD,IAAWA,KAAK,IAAID,IAAI,CAACE,IAAI,CAAC/Y,CAAD,CAAL,CAAJ,KAAkB5B,SAA1C,EAAsD;EACpDya,MAAAA,IAAI,CAACE,IAAI,CAAC/Y,CAAD,CAAL,CAAJ,GAAgBuL,GAAG,CAACwN,IAAI,CAAC/Y,CAAD,CAAL,CAAnB;EACD;;EACDA,IAAAA,CAAC;EACF;;EACD,SAAO6Y,IAAP;EACD,CAVuB,EAUrB,QAVqB,EAUX,eAVW,CAAxB;;ECRA;;;;;;;;;EAQA,IAAMC,KAAK,GAAGZ,SAAS,CAAC,UAACW,IAAD,EAAOtN,GAAP,EAAe;EACrC,SAAOqN,MAAM,CAACC,IAAD,EAAOtN,GAAP,EAAY,IAAZ,CAAb;EACD,CAFsB,EAEpB,OAFoB,EAEX,eAFW,CAAvB;;ECTA;;;;;;;;AAOA,EAAe,SAASyN,OAAT,CAAiBC,KAAjB,EAAwBC,IAAxB,EAA8BC,UAA9B,EAA0C;EACvD,MAAIC,KAAK,GAAGF,IAAI,CAAC5M,SAAjB;EACA,MAAI+M,MAAJ;EAEAA,EAAAA,MAAM,GAAGJ,KAAK,CAAC3M,SAAN,GAAkBpO,MAAM,CAACob,MAAP,CAAcF,KAAd,CAA3B;EACAC,EAAAA,MAAM,CAACE,WAAP,GAAqBN,KAArB;EACAI,EAAAA,MAAM,CAACG,MAAP,GAAgBJ,KAAhB;;EAEA,MAAID,UAAJ,EAAgB;EACdlb,IAAAA,QAAM,CAACob,MAAD,EAASF,UAAT,CAAN;EACD;EACF;;ECnBD;;;;;;;AAOA,EAAe,SAASM,MAAT,CAAgBxI,EAAhB,EAAoBlO,OAApB,EAA6B;EAC1C,SAAO,SAAS2W,OAAT,GAAmB;EACxB,WAAOzI,EAAE,CAAC9N,KAAH,CAASJ,OAAT,EAAkBvE,SAAlB,CAAP;EACD,GAFD;EAGD;;EC+CD;;;;;;;;MAOqBmb;;;QAAAA;EACpB;;;;EA4DA,kBAAY1V,OAAZ,EAAqBI,OAArB,EAAmC;EAAA,QAAdA,OAAc;EAAdA,MAAAA,OAAc,GAAJ,EAAI;EAAA;;EAClC,WAAO,IAAIuS,OAAJ,CAAY3S,OAAZ;EACNM,MAAAA,WAAW,EAEHuR,MAFG;EADL,OAKHzR,OALG,EAAP;EAOA;;EArEmBsV,EAAAA,OAKbC,UAAU;EALGD,EAAAA,OAMbjX,gBAAgBA;EANHiX,EAAAA,OAObpX,iBAAiBA;EAPJoX,EAAAA,OAQbvX,iBAAiBA;EARJuX,EAAAA,OASbtX,kBAAkBA;EATLsX,EAAAA,OAUbrX,eAAeA;EAVFqX,EAAAA,OAWbnX,uBAAuBA;EAXVmX,EAAAA,OAYblX,qBAAqBA;EAZRkX,EAAAA,OAabxX,iBAAiBA;EAbJwX,EAAAA,OAcbpX,iBAAiBA;EAdJoX,EAAAA,OAeb5X,cAAcA;EAfD4X,EAAAA,OAgBb3X,aAAaA;EAhBA2X,EAAAA,OAiBZ1X,YAAYA;EAjBA0X,EAAAA,OAkBbzX,eAAeA;EAlBFyX,EAAAA,OAmBbxI,iBAAiBA;EAnBJwI,EAAAA,OAoBbvI,cAAcA;EApBDuI,EAAAA,OAqBbtI,gBAAgBA;EArBHsI,EAAAA,OAsBbrI,cAAcA;EAtBDqI,EAAAA,OAuBbpI,mBAAmBA;EAvBNoI,EAAAA,OAwBbnI,kBAAkBA;EAxBLmI,EAAAA,OAyBblI,eAAeA;EAzBFkI,EAAAA,OA0Bb/C,UAAUA;EA1BG+C,EAAAA,OA2BbhP,QAAQA;EA3BKgP,EAAAA,OA4Bb/V,cAAcA;EA5BD+V,EAAAA,OA6BbzL,aAAaA;EA7BAyL,EAAAA,OA8BbxK,aAAaA;EA9BAwK,EAAAA,OA+BbvN,oBAAoBA;EA/BPuN,EAAAA,OAgCbtJ,kBAAkBA;EAhCLsJ,EAAAA,OAiCb9B,mBAAmBA;EAjCN8B,EAAAA,OAkCb1H,aAAaA;EAlCA0H,EAAAA,OAmCbtF,iBAAiBA;EAnCJsF,EAAAA,OAoCbE,MAAM3G;EApCOyG,EAAAA,OAqCbG,MAAMnF;EArCOgF,EAAAA,OAsCbI,QAAQ/E;EAtCK2E,EAAAA,OAuCbK,QAAQ/E;EAvCK0E,EAAAA,OAwCbM,SAAS9E;EAxCIwE,EAAAA,OAyCbO,QAAQ9E;EAzCKuE,EAAAA,OA0CbpC,KAAKxN;EA1CQ4P,EAAAA,OA2CblC,MAAMrN;EA3COuP,EAAAA,OA4Cb9W,OAAOA;EA5CM8W,EAAAA,OA6Cbb,QAAQA;EA7CKa,EAAAA,OA8Cbf,SAASA;EA9CIe,EAAAA,OA+CbF,SAASA;EA/CIE,EAAAA,OAgDb1b,SAASA;EAhDI0b,EAAAA,OAiDbX,UAAUA;EAjDGW,EAAAA,OAkDbF,SAASA;EAlDIE,EAAAA,OAmDbna,WAAWA;EAnDEma,EAAAA,OAoDbxM,UAAUA;EApDGwM,EAAAA,OAqDbrO,UAAUA;EArDGqO,EAAAA,OAsDbtM,cAAcA;EAtDDsM,EAAAA,OAuDb9P,WAAWA;EAvDE8P,EAAAA,OAwDb1W,WAAWA;EAxDE0W,EAAAA,OAyDb9T,YAAYA;EAzDC8T,EAAAA,OA0Db5P,oBAAoBA;EA1DP4P,EAAAA,OA2DbvP,uBAAuBA;EA3DVuP,EAAAA,OA4Db9C,WAAW5Y,QAAM,CAAC,EAAD,EAAK4Y,QAAL,EAAe;EAAEf,IAAAA,MAAM,EAANA;EAAF,GAAf;WA5DJ6D;;;;;;;;;"}