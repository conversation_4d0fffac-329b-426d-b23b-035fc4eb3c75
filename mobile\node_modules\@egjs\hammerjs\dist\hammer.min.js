/*! Hammer.JS - v2.0.17-rc - 2019-12-16
 * http://naver.github.io/egjs
 *
 * Forked By Naver egjs
 * Copyright (c) hammerjs
 * Licensed under the MIT license */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Hammer=e()}(this,function(){"use strict";function r(){return(r=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}function o(t,e){t.prototype=Object.create(e.prototype),(t.prototype.constructor=t).__proto__=e}function s(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}var i,a="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var i=arguments[n];if(null!=i)for(var r in i)i.hasOwnProperty(r)&&(e[r]=i[r])}return e}:Object.assign,u=["","webkit","Moz","MS","ms","o"],t="undefined"==typeof document?{style:{}}:document.createElement("div"),n="function",c=Math.round,A=Math.abs,b=Date.now;function h(t,e){for(var n,i,r=e[0].toUpperCase()+e.slice(1),o=0;o<u.length;){if((i=(n=u[o])?n+r:e)in t)return i;o++}}i="undefined"==typeof window?{}:window;var l=h(t.style,"touchAction"),p=void 0!==l;var f="compute",v="auto",d="manipulation",m="none",g="pan-x",y="pan-y",T=function(){if(!p)return!1;var e={},n=i.CSS&&i.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(t){return e[t]=!n||i.CSS.supports("touch-action",t)}),e}(),E="ontouchstart"in i,I=void 0!==h(i,"PointerEvent"),w=E&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),P="touch",C="mouse",S=25,_=1,D=4,O=8,x=1,R=2,M=4,N=8,z=16,X=R|M,Y=N|z,F=X|Y,W=["x","y"],q=["clientX","clientY"];function L(t,e,n){var i;if(t)if(t.forEach)t.forEach(e,n);else if(void 0!==t.length)for(i=0;i<t.length;)e.call(n,t[i],i,t),i++;else for(i in t)t.hasOwnProperty(i)&&e.call(n,t[i],i,t)}function k(t,e){return typeof t===n?t.apply(e&&e[0]||void 0,e):t}function H(t,e){return-1<t.indexOf(e)}var j=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===f&&(t=this.compute()),p&&this.manager.element.style&&T[t]&&(this.manager.element.style[l]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var e=[];return L(this.manager.recognizers,function(t){k(t.options.enable,[t])&&(e=e.concat(t.getTouchAction()))}),function(t){if(H(t,m))return m;var e=H(t,g),n=H(t,y);return e&&n?m:e||n?e?g:y:H(t,d)?d:v}(e.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var i=this.actions,r=H(i,m)&&!T[m],o=H(i,y)&&!T[y],s=H(i,g)&&!T[g];if(r){var a=1===t.pointers.length,u=t.distance<2,c=t.deltaTime<250;if(a&&u&&c)return}if(!s||!o)return r||o&&n&X||s&&n&Y?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function U(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function V(t){var e=t.length;if(1===e)return{x:c(t[0].clientX),y:c(t[0].clientY)};for(var n=0,i=0,r=0;r<e;)n+=t[r].clientX,i+=t[r].clientY,r++;return{x:c(n/e),y:c(i/e)}}function G(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:c(t.pointers[n].clientX),clientY:c(t.pointers[n].clientY)},n++;return{timeStamp:b(),pointers:e,center:V(e),deltaX:t.deltaX,deltaY:t.deltaY}}function Z(t,e,n){n||(n=W);var i=e[n[0]]-t[n[0]],r=e[n[1]]-t[n[1]];return Math.sqrt(i*i+r*r)}function B(t,e,n){n||(n=W);var i=e[n[0]]-t[n[0]],r=e[n[1]]-t[n[1]];return 180*Math.atan2(r,i)/Math.PI}function $(t,e){return t===e?x:A(t)>=A(e)?t<0?R:M:e<0?N:z}function J(t,e,n){return{x:e/t||0,y:n/t||0}}function K(t,e){var n=t.session,i=e.pointers,r=i.length;n.firstInput||(n.firstInput=G(e)),1<r&&!n.firstMultiple?n.firstMultiple=G(e):1===r&&(n.firstMultiple=!1);var o,s,a,u,c,h,l=n.firstInput,p=n.firstMultiple,f=p?p.center:l.center,v=e.center=V(i);e.timeStamp=b(),e.deltaTime=e.timeStamp-l.timeStamp,e.angle=B(f,v),e.distance=Z(f,v),o=n,a=(s=e).center,u=o.offsetDelta||{},c=o.prevDelta||{},h=o.prevInput||{},s.eventType!==_&&h.eventType!==D||(c=o.prevDelta={x:h.deltaX||0,y:h.deltaY||0},u=o.offsetDelta={x:a.x,y:a.y}),s.deltaX=c.x+(a.x-u.x),s.deltaY=c.y+(a.y-u.y),e.offsetDirection=$(e.deltaX,e.deltaY);var d,m,g,y,T=J(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=T.x,e.overallVelocityY=T.y,e.overallVelocity=A(T.x)>A(T.y)?T.x:T.y,e.scale=p?(d=p.pointers,Z((m=i)[0],m[1],q)/Z(d[0],d[1],q)):1,e.rotation=p?(g=p.pointers,B((y=i)[1],y[0],q)+B(g[1],g[0],q)):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,function(t,e){var n,i,r,o,s=t.lastInterval||e,a=e.timeStamp-s.timeStamp;if(e.eventType!==O&&(S<a||void 0===s.velocity)){var u=e.deltaX-s.deltaX,c=e.deltaY-s.deltaY,h=J(a,u,c);i=h.x,r=h.y,n=A(h.x)>A(h.y)?h.x:h.y,o=$(u,c),t.lastInterval=e}else n=s.velocity,i=s.velocityX,r=s.velocityY,o=s.direction;e.velocity=n,e.velocityX=i,e.velocityY=r,e.direction=o}(n,e);var E,I=t.element,w=e.srcEvent;U(E=w.composedPath?w.composedPath()[0]:w.path?w.path[0]:w.target,I)&&(I=E),e.target=I}function Q(t,e,n){var i=n.pointers.length,r=n.changedPointers.length,o=e&_&&i-r==0,s=e&(D|O)&&i-r==0;n.isFirst=!!o,n.isFinal=!!s,o&&(t.session={}),n.eventType=e,K(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function tt(t){return t.trim().split(/\s+/g)}function et(e,t,n){L(tt(t),function(t){e.addEventListener(t,n,!1)})}function nt(e,t,n){L(tt(t),function(t){e.removeEventListener(t,n,!1)})}function it(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var e=function(){function t(e,t){var n=this;this.manager=e,this.callback=t,this.element=e.element,this.target=e.options.inputTarget,this.domHandler=function(t){k(e.options.enable,[e])&&n.handler(t)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&et(this.element,this.evEl,this.domHandler),this.evTarget&&et(this.target,this.evTarget,this.domHandler),this.evWin&&et(it(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&nt(this.element,this.evEl,this.domHandler),this.evTarget&&nt(this.target,this.evTarget,this.domHandler),this.evWin&&nt(it(this.element),this.evWin,this.domHandler)},t}();function rt(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var i=0;i<t.length;){if(n&&t[i][n]==e||!n&&t[i]===e)return i;i++}return-1}var ot={pointerdown:_,pointermove:2,pointerup:D,pointercancel:O,pointerout:O},st={2:P,3:"pen",4:C,5:"kinect"},at="pointerdown",ut="pointermove pointerup pointercancel";i.MSPointerEvent&&!i.PointerEvent&&(at="MSPointerDown",ut="MSPointerMove MSPointerUp MSPointerCancel");var ct=function(n){function i(){var t,e=i.prototype;return e.evEl=at,e.evWin=ut,(t=n.apply(this,arguments)||this).store=t.manager.session.pointerEvents=[],t}return o(i,n),i.prototype.handler=function(t){var e=this.store,n=!1,i=t.type.toLowerCase().replace("ms",""),r=ot[i],o=st[t.pointerType]||t.pointerType,s=o===P,a=rt(e,t.pointerId,"pointerId");r&_&&(0===t.button||s)?a<0&&(e.push(t),a=e.length-1):r&(D|O)&&(n=!0),a<0||(e[a]=t,this.callback(this.manager,r,{pointers:e,changedPointers:[t],pointerType:o,srcEvent:t}),n&&e.splice(a,1))},i}(e);function ht(t){return Array.prototype.slice.call(t,0)}function lt(t,n,e){for(var i=[],r=[],o=0;o<t.length;){var s=n?t[o][n]:t[o];rt(r,s)<0&&i.push(t[o]),r[o]=s,o++}return e&&(i=n?i.sort(function(t,e){return t[n]>e[n]}):i.sort()),i}var pt={touchstart:_,touchmove:2,touchend:D,touchcancel:O},ft=function(e){function n(){var t;return n.prototype.evTarget="touchstart touchmove touchend touchcancel",(t=e.apply(this,arguments)||this).targetIds={},t}return o(n,e),n.prototype.handler=function(t){var e=pt[t.type],n=function(t,e){var n,i,r=ht(t.touches),o=this.targetIds;if(e&(2|_)&&1===r.length)return o[r[0].identifier]=!0,[r,r];var s=ht(t.changedTouches),a=[],u=this.target;if(i=r.filter(function(t){return U(t.target,u)}),e===_)for(n=0;n<i.length;)o[i[n].identifier]=!0,n++;n=0;for(;n<s.length;)o[s[n].identifier]&&a.push(s[n]),e&(D|O)&&delete o[s[n].identifier],n++;return a.length?[lt(i.concat(a),"identifier",!0),a]:void 0}.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:P,srcEvent:t})},n}(e);var vt={mousedown:_,mousemove:2,mouseup:D},dt=function(n){function i(){var t,e=i.prototype;return e.evEl="mousedown",e.evWin="mousemove mouseup",(t=n.apply(this,arguments)||this).pressed=!1,t}return o(i,n),i.prototype.handler=function(t){var e=vt[t.type];e&_&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=D),this.pressed&&(e&D&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:C,srcEvent:t}))},i}(e),mt=2500,gt=25;function yt(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY},i=this.lastTouches;this.lastTouches.push(n);setTimeout(function(){var t=i.indexOf(n);-1<t&&i.splice(t,1)},mt)}}var Tt=function(){return function(n){function t(t,e){var o;return(o=n.call(this,t,e)||this).handler=function(t,e,n){var i=n.pointerType===P,r=n.pointerType===C;if(!(r&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(i)(function(t,e){t&_?(this.primaryTouch=e.changedPointers[0].identifier,yt.call(this,e)):t&(D|O)&&yt.call(this,e)}).call(s(s(o)),e,n);else if(r&&function(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,i=0;i<this.lastTouches.length;i++){var r=this.lastTouches[i],o=Math.abs(e-r.x),s=Math.abs(n-r.y);if(o<=gt&&s<=gt)return!0}return!1}.call(s(s(o)),n))return;o.callback(t,e,n)}},o.touch=new ft(o.manager,o.handler),o.mouse=new dt(o.manager,o.handler),o.primaryTouch=null,o.lastTouches=[],o}return o(t,n),t.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},t}(e)}();function Et(t,e,n){return!!Array.isArray(t)&&(L(t,n[e],n),!0)}var It=1;function wt(t,e){var n=e.manager;return n?n.get(t):t}function At(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var bt=function(){function t(t){void 0===t&&(t={}),this.options=r({enable:!0},t),this.id=It++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return a(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(Et(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=wt(t,this)).id]||(e[t.id]=t).recognizeWith(this),this},e.dropRecognizeWith=function(t){return Et(t,"dropRecognizeWith",this)||(t=wt(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(Et(t,"requireFailure",this))return this;var e=this.requireFail;return-1===rt(e,t=wt(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(Et(t,"dropRequireFailure",this))return this;t=wt(t,this);var e=rt(this.requireFail,t);return-1<e&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return 0<this.requireFail.length},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(e){var n=this,t=this.state;function i(t){n.manager.emit(t,e)}t<8&&i(n.options.event+At(t)),i(n.options.event),e.additionalEvent&&i(e.additionalEvent),8<=t&&i(n.options.event+At(t))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=32},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=a({},t);if(!k(this.options.enable,[this,e]))return this.reset(),void(this.state=32);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),Pt=function(n){function t(t){var e;return void 0===t&&(t={}),(e=n.call(this,r({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},t))||this).pTime=!1,e.pCenter=!1,e._timer=null,e._input=null,e.count=0,e}o(t,n);var e=t.prototype;return e.getTouchAction=function(){return[d]},e.process=function(t){var e=this,n=this.options,i=t.pointers.length===n.pointers,r=t.distance<n.threshold,o=t.deltaTime<n.time;if(this.reset(),t.eventType&_&&0===this.count)return this.failTimeout();if(r&&o&&i){if(t.eventType!==D)return this.failTimeout();var s=!this.pTime||t.timeStamp-this.pTime<n.interval,a=!this.pCenter||Z(this.pCenter,t.center)<n.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,a&&s?this.count+=1:this.count=1,this._input=t,0===this.count%n.taps)return this.hasRequireFailures()?(this._timer=setTimeout(function(){e.state=8,e.tryEmit()},n.interval),2):8}return 32},e.failTimeout=function(){var t=this;return this._timer=setTimeout(function(){t.state=32},this.options.interval),32},e.reset=function(){clearTimeout(this._timer)},e.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},t}(bt),Ct=function(e){function t(t){return void 0===t&&(t={}),e.call(this,r({pointers:1},t))||this}o(t,e);var n=t.prototype;return n.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},n.process=function(t){var e=this.state,n=t.eventType,i=6&e,r=this.attrTest(t);return i&&(n&O||!r)?16|e:i||r?n&D?8|e:2&e?4|e:2:32},t}(bt);function St(t){return t===z?"down":t===N?"up":t===R?"left":t===M?"right":""}var _t=function(n){function t(t){var e;return void 0===t&&(t={}),(e=n.call(this,r({event:"pan",threshold:10,pointers:1,direction:F},t))||this).pX=null,e.pY=null,e}o(t,n);var e=t.prototype;return e.getTouchAction=function(){var t=this.options.direction,e=[];return t&X&&e.push(y),t&Y&&e.push(g),e},e.directionTest=function(t){var e=this.options,n=!0,i=t.distance,r=t.direction,o=t.deltaX,s=t.deltaY;return r&e.direction||(i=e.direction&X?(r=0===o?x:o<0?R:M,n=o!==this.pX,Math.abs(t.deltaX)):(r=0===s?x:s<0?N:z,n=s!==this.pY,Math.abs(t.deltaY))),t.direction=r,n&&i>e.threshold&&r&e.direction},e.attrTest=function(t){return Ct.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},e.emit=function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=St(t.direction);e&&(t.additionalEvent=this.options.event+e),n.prototype.emit.call(this,t)},t}(Ct),Dt=function(i){function t(t){return void 0===t&&(t={}),i.call(this,r({event:"swipe",threshold:10,velocity:.3,direction:X|Y,pointers:1},t))||this}o(t,i);var e=t.prototype;return e.getTouchAction=function(){return _t.prototype.getTouchAction.call(this)},e.attrTest=function(t){var e,n=this.options.direction;return n&(X|Y)?e=t.overallVelocity:n&X?e=t.overallVelocityX:n&Y&&(e=t.overallVelocityY),i.prototype.attrTest.call(this,t)&&n&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers===this.options.pointers&&A(e)>this.options.velocity&&t.eventType&D},e.emit=function(t){var e=St(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},t}(Ct),Ot=function(n){function t(t){return void 0===t&&(t={}),n.call(this,r({event:"pinch",threshold:0,pointers:2},t))||this}o(t,n);var e=t.prototype;return e.getTouchAction=function(){return[m]},e.attrTest=function(t){return n.prototype.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||2&this.state)},e.emit=function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}n.prototype.emit.call(this,t)},t}(Ct),xt=function(e){function t(t){return void 0===t&&(t={}),e.call(this,r({event:"rotate",threshold:0,pointers:2},t))||this}o(t,e);var n=t.prototype;return n.getTouchAction=function(){return[m]},n.attrTest=function(t){return e.prototype.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||2&this.state)},t}(Ct),Rt=function(n){function t(t){var e;return void 0===t&&(t={}),(e=n.call(this,r({event:"press",pointers:1,time:251,threshold:9},t))||this)._timer=null,e._input=null,e}o(t,n);var e=t.prototype;return e.getTouchAction=function(){return[v]},e.process=function(t){var e=this,n=this.options,i=t.pointers.length===n.pointers,r=t.distance<n.threshold,o=t.deltaTime>n.time;if(this._input=t,!r||!i||t.eventType&(D|O)&&!o)this.reset();else if(t.eventType&_)this.reset(),this._timer=setTimeout(function(){e.state=8,e.tryEmit()},n.time);else if(t.eventType&D)return 8;return 32},e.reset=function(){clearTimeout(this._timer)},e.emit=function(t){8===this.state&&(t&&t.eventType&D?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=b(),this.manager.emit(this.options.event,this._input)))},t}(bt),Mt={domEvents:!1,touchAction:f,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},Nt=[[xt,{enable:!1}],[Ot,{enable:!1},["rotate"]],[Dt,{direction:X}],[_t,{direction:X},["swipe"]],[Pt],[Pt,{event:"doubletap",taps:2},["tap"]],[Rt]];function zt(n,i){var r,o=n.element;o.style&&(L(n.options.cssProps,function(t,e){r=h(o.style,e),o.style[r]=i?(n.oldCssProps[r]=o.style[r],t):n.oldCssProps[r]||""}),i||(n.oldCssProps={}))}var Xt=function(){function t(t,e){var n,i=this;this.options=a({},Mt,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((n=this).options.inputClass||(I?ct:w?ft:E?Tt:dt))(n,Q),this.touchAction=new j(this,this.options.touchAction),zt(this,!0),L(this.options.recognizers,function(t){var e=i.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}var e=t.prototype;return e.set=function(t){return a(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var n;this.touchAction.preventDefaults(t);var i=this.recognizers,r=e.curRecognizer;(!r||r&&8&r.state)&&(r=e.curRecognizer=null);for(var o=0;o<i.length;)n=i[o],2===e.stopped||r&&n!==r&&!n.canRecognizeWith(r)?n.reset():n.recognize(t),!r&&14&n.state&&(r=e.curRecognizer=n),o++}},e.get=function(t){if(t instanceof bt)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event===t)return e[n];return null},e.add=function(t){if(Et(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),(t.manager=this).touchAction.update(),t},e.remove=function(t){if(Et(t,"remove",this))return this;var e=this.get(t);if(t){var n=this.recognizers,i=rt(n,e);-1!==i&&(n.splice(i,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var n=this.handlers;return L(tt(t),function(t){n[t]=n[t]||[],n[t].push(e)}),this},e.off=function(t,e){if(void 0===t)return this;var n=this.handlers;return L(tt(t),function(t){e?n[t]&&n[t].splice(rt(n[t],e),1):delete n[t]}),this},e.emit=function(t,e){var n,i,r;this.options.domEvents&&(n=t,i=e,(r=document.createEvent("Event")).initEvent(n,!0,!0),(r.gesture=i).target.dispatchEvent(r));var o=this.handlers[t]&&this.handlers[t].slice();if(o&&o.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var s=0;s<o.length;)o[s](e),s++}},e.destroy=function(){this.element&&zt(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),Yt={touchstart:_,touchmove:2,touchend:D,touchcancel:O},Ft=function(n){function i(){var t,e=i.prototype;return e.evTarget="touchstart",e.evWin="touchstart touchmove touchend touchcancel",(t=n.apply(this,arguments)||this).started=!1,t}return o(i,n),i.prototype.handler=function(t){var e=Yt[t.type];if(e===_&&(this.started=!0),this.started){var n=function(t,e){var n=ht(t.touches),i=ht(t.changedTouches);e&(D|O)&&(n=lt(n.concat(i),"identifier",!0));return[n,i]}.call(this,t,e);e&(D|O)&&n[0].length-n[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:P,srcEvent:t})}},i}(e);function Wt(i,t,e){var r="DEPRECATED METHOD: "+t+"\n"+e+" AT \n";return function(){var t=new Error("get-stack-trace"),e=t&&t.stack?t.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",n=window.console&&(window.console.warn||window.console.log);return n&&n.call(window.console,r,e),i.apply(this,arguments)}}var qt=Wt(function(t,e,n){for(var i=Object.keys(e),r=0;r<i.length;)(!n||n&&void 0===t[i[r]])&&(t[i[r]]=e[i[r]]),r++;return t},"extend","Use `assign`."),Lt=Wt(function(t,e){return qt(t,e,!0)},"merge","Use `assign`.");function kt(t,e,n){var i,r=e.prototype;(i=t.prototype=Object.create(r)).constructor=t,i._super=r,n&&a(i,n)}function Ht(t,e){return function(){return t.apply(e,arguments)}}return function(){var t=function(t,e){return void 0===e&&(e={}),new Xt(t,r({recognizers:Nt.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=F,t.DIRECTION_DOWN=z,t.DIRECTION_LEFT=R,t.DIRECTION_RIGHT=M,t.DIRECTION_UP=N,t.DIRECTION_HORIZONTAL=X,t.DIRECTION_VERTICAL=Y,t.DIRECTION_NONE=x,t.DIRECTION_DOWN=z,t.INPUT_START=_,t.INPUT_MOVE=2,t.INPUT_END=D,t.INPUT_CANCEL=O,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=32,t.Manager=Xt,t.Input=e,t.TouchAction=j,t.TouchInput=ft,t.MouseInput=dt,t.PointerEventInput=ct,t.TouchMouseInput=Tt,t.SingleTouchInput=Ft,t.Recognizer=bt,t.AttrRecognizer=Ct,t.Tap=Pt,t.Pan=_t,t.Swipe=Dt,t.Pinch=Ot,t.Rotate=xt,t.Press=Rt,t.on=et,t.off=nt,t.each=L,t.merge=Lt,t.extend=qt,t.bindFn=Ht,t.assign=a,t.inherit=kt,t.bindFn=Ht,t.prefixed=h,t.toArray=ht,t.inArray=rt,t.uniqueArray=lt,t.splitStr=tt,t.boolOrFn=k,t.hasParent=U,t.addEventListeners=et,t.removeEventListeners=nt,t.defaults=a({},Mt,{preset:Nt}),t}()});
//# sourceMappingURL=hammer.min.js.map
