{"version": 3, "sources": ["../../bin/cli.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport Debug from 'debug';\nimport { boolish } from 'getenv';\n\n// Setup before requiring `debug`.\nif (boolish('EXPO_DEBUG', false)) {\n  Debug.enable('expo:*');\n} else if (Debug.enabled('expo:')) {\n  process.env.EXPO_DEBUG = '1';\n}\n\nconst defaultCmd = 'start';\n\nexport type Command = (argv?: string[]) => void;\n\nconst commands: { [command: string]: () => Promise<Command> } = {\n  // Add a new command here\n  // NOTE(EvanBacon): Ensure every bundler-related command sets `NODE_ENV` as expected for the command.\n  'run:ios': () => import('../src/run/ios').then((i) => i.expoRunIos),\n  'run:android': () => import('../src/run/android').then((i) => i.expoRunAndroid),\n  start: () => import('../src/start').then((i) => i.expoStart),\n  prebuild: () => import('../src/prebuild').then((i) => i.expoPrebuild),\n  config: () => import('../src/config').then((i) => i.expoConfig),\n  export: () => import('../src/export').then((i) => i.expoExport),\n  'export:web': () => import('../src/export/web').then((i) => i.expoExportWeb),\n  'export:embed': () => import('../src/export/embed').then((i) => i.expoExportEmbed),\n\n  // Auxiliary commands\n  install: () => import('../src/install').then((i) => i.expoInstall),\n  add: () => import('../src/install').then((i) => i.expoInstall),\n  customize: () => import('../src/customize').then((i) => i.expoCustomize),\n\n  // Auth\n  login: () => import('../src/login').then((i) => i.expoLogin),\n  logout: () => import('../src/logout').then((i) => i.expoLogout),\n  register: () => import('../src/register').then((i) => i.expoRegister),\n  whoami: () => import('../src/whoami').then((i) => i.expoWhoami),\n};\n\nconst args = arg(\n  {\n    // Types\n    '--version': Boolean,\n    '--help': Boolean,\n    // NOTE(EvanBacon): This is here to silence warnings from processes that\n    // expect the global expo-cli.\n    '--non-interactive': Boolean,\n\n    // Aliases\n    '-v': '--version',\n    '-h': '--help',\n  },\n  {\n    permissive: true,\n  }\n);\n\nif (args['--version']) {\n  // Version is added in the build script.\n  console.log(process.env.__EXPO_VERSION);\n  process.exit(0);\n}\n\nif (args['--non-interactive']) {\n  console.warn(chalk.yellow`  {bold --non-interactive} is not supported, use {bold $CI=1} instead`);\n}\n\n// Check if we are running `npx expo <subcommand>` or `npx expo`\nconst isSubcommand = Boolean(commands[args._[0]]);\n\n// Handle `--help` flag\nif (!isSubcommand && args['--help']) {\n  const {\n    login,\n    logout,\n    whoami,\n    register,\n    start,\n    install,\n    add,\n    export: _export,\n    config,\n    customize,\n    prebuild,\n    'run:ios': runIos,\n    'run:android': runAndroid,\n    // NOTE(EvanBacon): Don't document this command as it's a temporary\n    // workaround until we can use `expo export` for all production bundling.\n    // https://github.com/expo/expo/pull/21396/files#r1121025873\n    'export:embed': exportEmbed_unused,\n    ...others\n  } = commands;\n\n  console.log(chalk`\n  {bold Usage}\n    {dim $} npx expo <command>\n\n  {bold Commands}\n    ${Object.keys({ start, export: _export, ...others }).join(', ')}\n    ${Object.keys({ 'run:ios': runIos, 'run:android': runAndroid, prebuild }).join(', ')}\n    ${Object.keys({ install, customize, config }).join(', ')}\n    {dim ${Object.keys({ login, logout, whoami, register }).join(', ')}}\n\n  {bold Options}\n    --version, -v   Version number\n    --help, -h      Usage info\n\n  For more info run a command with the {bold --help} flag\n    {dim $} npx expo start --help\n`);\n\n  process.exit(0);\n}\n\n// NOTE(EvanBacon): Squat some directory names to help with migration,\n// users can still use folders named \"send\" or \"eject\" by using the fully qualified `npx expo start ./send`.\nif (!isSubcommand) {\n  const migrationMap: Record<string, string> = {\n    init: 'npx create-expo-app',\n    eject: 'npx expo prebuild',\n    web: 'npx expo start --web',\n    'start:web': 'npx expo start --web',\n    'build:ios': 'eas build -p ios',\n    'build:android': 'eas build -p android',\n    'client:install:ios': 'npx expo start --ios',\n    'client:install:android': 'npx expo start --android',\n    doctor: 'npx expo-doctor',\n    upgrade: 'expo-cli upgrade',\n    'customize:web': 'npx expo customize',\n\n    publish: 'eas update',\n    'publish:set': 'eas update',\n    'publish:rollback': 'eas update',\n    'publish:history': 'eas update',\n    'publish:details': 'eas update',\n\n    'build:web': 'npx expo export:web',\n\n    'credentials:manager': `eas credentials`,\n    'fetch:ios:certs': `eas credentials`,\n    'fetch:android:keystore': `eas credentials`,\n    'fetch:android:hashes': `eas credentials`,\n    'fetch:android:upload-cert': `eas credentials`,\n    'push:android:upload': `eas credentials`,\n    'push:android:show': `eas credentials`,\n    'push:android:clear': `eas credentials`,\n    url: `eas build:list`,\n    'url:ipa': `eas build:list`,\n    'url:apk': `eas build:list`,\n    webhooks: `eas webhook`,\n    'webhooks:add': `eas webhook:create`,\n    'webhooks:remove': `eas webhook:delete`,\n    'webhooks:update': `eas webhook:update`,\n\n    'build:status': `eas build:list`,\n    'upload:android': `eas submit -p android`,\n    'upload:ios': `eas submit -p ios`,\n  };\n\n  // TODO: Log telemetry about invalid command used.\n  const subcommand = args._[0];\n  if (subcommand in migrationMap) {\n    const replacement = migrationMap[subcommand];\n    console.log();\n    console.log(\n      chalk.yellow`  {gray $} {bold expo ${subcommand}} is not supported in the local CLI, please use {bold ${replacement}} instead`\n    );\n    console.log();\n    process.exit(1);\n  }\n  const deprecated = ['send', 'client:ios'];\n  if (deprecated.includes(subcommand)) {\n    console.log();\n    console.log(chalk.yellow`  {gray $} {bold expo ${subcommand}} is deprecated`);\n    console.log();\n    process.exit(1);\n  }\n}\n\nconst command = isSubcommand ? args._[0] : defaultCmd;\nconst commandArgs = isSubcommand ? args._.slice(1) : args._;\n\n// Push the help flag to the subcommand args.\nif (args['--help']) {\n  commandArgs.push('--help');\n}\n\n// Install exit hooks\nprocess.on('SIGINT', () => process.exit(0));\nprocess.on('SIGTERM', () => process.exit(0));\n\ncommands[command]().then((exec) => {\n  exec(commandArgs);\n\n  // NOTE(EvanBacon): Track some basic telemetry events indicating the command\n  // that was run. This can be disabled with the $EXPO_NO_TELEMETRY environment variable.\n  // We do this to determine how well deprecations are going before removing a command.\n  const { logEventAsync } =\n    require('../src/utils/analytics/rudderstackClient') as typeof import('../src/utils/analytics/rudderstackClient');\n  logEventAsync('action', {\n    action: `expo ${command}`,\n    source: 'expo/cli',\n    source_version: process.env.__EXPO_VERSION,\n  });\n});\n"], "names": ["boolish", "Debug", "enable", "enabled", "process", "env", "EXPO_DEBUG", "defaultCmd", "commands", "then", "i", "expoRunIos", "expoRunAndroid", "start", "expoStart", "prebuild", "expoPrebuild", "config", "expoConfig", "export", "expoExport", "expoExportWeb", "expoExportEmbed", "install", "expoInstall", "add", "customize", "expoCustomize", "login", "expoLogin", "logout", "expoLogout", "register", "expoRegister", "whoami", "expoWhoami", "args", "arg", "Boolean", "permissive", "console", "log", "__EXPO_VERSION", "exit", "warn", "chalk", "yellow", "isSubcommand", "_", "_export", "runIos", "runAndroid", "exportEmbed_unused", "others", "Object", "keys", "join", "migrationMap", "init", "eject", "web", "doctor", "upgrade", "publish", "url", "webhooks", "subcommand", "replacement", "deprecated", "includes", "command", "commandArgs", "slice", "push", "on", "exec", "logEventAsync", "require", "action", "source", "source_version"], "mappings": "AAAA;;AACgB,IAAA,IAAK,kCAAL,KAAK,EAAA;AACH,IAAA,MAAO,kCAAP,OAAO,EAAA;AACP,IAAA,MAAO,kCAAP,OAAO,EAAA;AACD,IAAA,OAAQ,WAAR,QAAQ,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhC,kCAAkC;AAClC,IAAIA,CAAAA,GAAAA,OAAO,AAAqB,CAAA,QAArB,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE;IAChCC,MAAK,QAAA,CAACC,MAAM,CAAC,QAAQ,CAAC,CAAC;CACxB,MAAM,IAAID,MAAK,QAAA,CAACE,OAAO,CAAC,OAAO,CAAC,EAAE;IACjCC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,GAAG,CAAC;CAC9B;AAED,MAAMC,UAAU,GAAG,OAAO,AAAC;AAI3B,MAAMC,QAAQ,GAAkD;IAC9D,yBAAyB;IACzB,qGAAqG;IACrG,SAAS,EAAE,IAAM;mDAAO,gBAAgB;UAAC,CAACC,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,UAAU;QAAA,CAAC;IAAA;IACnE,aAAa,EAAE,IAAM;mDAAO,oBAAoB;UAAC,CAACF,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACE,cAAc;QAAA,CAAC;IAAA;IAC/EC,KAAK,EAAE,IAAM;mDAAO,cAAc;UAAC,CAACJ,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACI,SAAS;QAAA,CAAC;IAAA;IAC5DC,QAAQ,EAAE,IAAM;mDAAO,iBAAiB;UAAC,CAACN,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACM,YAAY;QAAA,CAAC;IAAA;IACrEC,MAAM,EAAE,IAAM;mDAAO,eAAe;UAAC,CAACR,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACQ,UAAU;QAAA,CAAC;IAAA;IAC/DC,MAAM,EAAE,IAAM;mDAAO,eAAe;UAAC,CAACV,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACU,UAAU;QAAA,CAAC;IAAA;IAC/D,YAAY,EAAE,IAAM;mDAAO,mBAAmB;UAAC,CAACX,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACW,aAAa;QAAA,CAAC;IAAA;IAC5E,cAAc,EAAE,IAAM;mDAAO,qBAAqB;UAAC,CAACZ,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACY,eAAe;QAAA,CAAC;IAAA;IAElF,qBAAqB;IACrBC,OAAO,EAAE,IAAM;mDAAO,gBAAgB;UAAC,CAACd,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACc,WAAW;QAAA,CAAC;IAAA;IAClEC,GAAG,EAAE,IAAM;mDAAO,gBAAgB;UAAC,CAAChB,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACc,WAAW;QAAA,CAAC;IAAA;IAC9DE,SAAS,EAAE,IAAM;mDAAO,kBAAkB;UAAC,CAACjB,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACiB,aAAa;QAAA,CAAC;IAAA;IAExE,OAAO;IACPC,KAAK,EAAE,IAAM;mDAAO,cAAc;UAAC,CAACnB,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACmB,SAAS;QAAA,CAAC;IAAA;IAC5DC,MAAM,EAAE,IAAM;mDAAO,eAAe;UAAC,CAACrB,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACqB,UAAU;QAAA,CAAC;IAAA;IAC/DC,QAAQ,EAAE,IAAM;mDAAO,iBAAiB;UAAC,CAACvB,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACuB,YAAY;QAAA,CAAC;IAAA;IACrEC,MAAM,EAAE,IAAM;mDAAO,eAAe;UAAC,CAACzB,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACyB,UAAU;QAAA,CAAC;CAChE,AAAC;AAEF,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,IAAG,AAgBf,CAAA,QAhBe,CACd;IACE,QAAQ;IACR,WAAW,EAAEC,OAAO;IACpB,QAAQ,EAAEA,OAAO;IACjB,wEAAwE;IACxE,8BAA8B;IAC9B,mBAAmB,EAAEA,OAAO;IAE5B,UAAU;IACV,IAAI,EAAE,WAAW;IACjB,IAAI,EAAE,QAAQ;CACf,EACD;IACEC,UAAU,EAAE,IAAI;CACjB,CACF,AAAC;AAEF,IAAIH,IAAI,CAAC,WAAW,CAAC,EAAE;IACrB,wCAAwC;IACxCI,OAAO,CAACC,GAAG,CAACrC,OAAO,CAACC,GAAG,CAACqC,cAAc,CAAC,CAAC;IACxCtC,OAAO,CAACuC,IAAI,CAAC,CAAC,CAAC,CAAC;CACjB;AAED,IAAIP,IAAI,CAAC,mBAAmB,CAAC,EAAE;IAC7BI,OAAO,CAACI,IAAI,CAACC,MAAK,QAAA,CAACC,MAAM,CAAC,qEAAqE,CAAC,CAAC,CAAC;CACnG;AAED,gEAAgE;AAChE,MAAMC,YAAY,GAAGT,OAAO,CAAC9B,QAAQ,CAAC4B,IAAI,CAACY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC;AAElD,uBAAuB;AACvB,IAAI,CAACD,YAAY,IAAIX,IAAI,CAAC,QAAQ,CAAC,EAAE;IACnC,MAAM,EACJR,KAAK,CAAA,EACLE,MAAM,CAAA,EACNI,MAAM,CAAA,EACNF,QAAQ,CAAA,EACRnB,KAAK,CAAA,EACLU,OAAO,CAAA,EACPE,GAAG,CAAA,EACHN,MAAM,EAAE8B,OAAO,CAAA,EACfhC,MAAM,CAAA,EACNS,SAAS,CAAA,EACTX,QAAQ,CAAA,EACR,SAAS,EAAEmC,MAAM,CAAA,EACjB,aAAa,EAAEC,UAAU,CAAA,EACzB,mEAAmE;IACnE,yEAAyE;IACzE,4DAA4D;IAC5D,cAAc,EAAEC,kBAAkB,CAAA,EAClC,GAAGC,MAAM,EACV,GAAG7C,QAAQ,AAAC;IAEbgC,OAAO,CAACC,GAAG,CAACI,MAAK,QAAA,CAAC;;;;;IAKhB,EAAES,MAAM,CAACC,IAAI,CAAC;QAAE1C,KAAK;QAAEM,MAAM,EAAE8B,OAAO;QAAE,GAAGI,MAAM;KAAE,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;IAChE,EAAEF,MAAM,CAACC,IAAI,CAAC;QAAE,SAAS,EAAEL,MAAM;QAAE,aAAa,EAAEC,UAAU;QAAEpC,QAAQ;KAAE,CAAC,CAACyC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrF,EAAEF,MAAM,CAACC,IAAI,CAAC;QAAEhC,OAAO;QAAEG,SAAS;QAAET,MAAM;KAAE,CAAC,CAACuC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpD,EAAEF,MAAM,CAACC,IAAI,CAAC;QAAE3B,KAAK;QAAEE,MAAM;QAAEI,MAAM;QAAEF,QAAQ;KAAE,CAAC,CAACwB,IAAI,CAAC,IAAI,CAAC,CAAC;;;;;;;;AAQvE,CAAC,CAAC,CAAC;IAEDpD,OAAO,CAACuC,IAAI,CAAC,CAAC,CAAC,CAAC;CACjB;AAED,sEAAsE;AACtE,4GAA4G;AAC5G,IAAI,CAACI,YAAY,EAAE;IACjB,MAAMU,YAAY,GAA2B;QAC3CC,IAAI,EAAE,qBAAqB;QAC3BC,KAAK,EAAE,mBAAmB;QAC1BC,GAAG,EAAE,sBAAsB;QAC3B,WAAW,EAAE,sBAAsB;QACnC,WAAW,EAAE,kBAAkB;QAC/B,eAAe,EAAE,sBAAsB;QACvC,oBAAoB,EAAE,sBAAsB;QAC5C,wBAAwB,EAAE,0BAA0B;QACpDC,MAAM,EAAE,iBAAiB;QACzBC,OAAO,EAAE,kBAAkB;QAC3B,eAAe,EAAE,oBAAoB;QAErCC,OAAO,EAAE,YAAY;QACrB,aAAa,EAAE,YAAY;QAC3B,kBAAkB,EAAE,YAAY;QAChC,iBAAiB,EAAE,YAAY;QAC/B,iBAAiB,EAAE,YAAY;QAE/B,WAAW,EAAE,qBAAqB;QAElC,qBAAqB,EAAE,CAAC,eAAe,CAAC;QACxC,iBAAiB,EAAE,CAAC,eAAe,CAAC;QACpC,wBAAwB,EAAE,CAAC,eAAe,CAAC;QAC3C,sBAAsB,EAAE,CAAC,eAAe,CAAC;QACzC,2BAA2B,EAAE,CAAC,eAAe,CAAC;QAC9C,qBAAqB,EAAE,CAAC,eAAe,CAAC;QACxC,mBAAmB,EAAE,CAAC,eAAe,CAAC;QACtC,oBAAoB,EAAE,CAAC,eAAe,CAAC;QACvCC,GAAG,EAAE,CAAC,cAAc,CAAC;QACrB,SAAS,EAAE,CAAC,cAAc,CAAC;QAC3B,SAAS,EAAE,CAAC,cAAc,CAAC;QAC3BC,QAAQ,EAAE,CAAC,WAAW,CAAC;QACvB,cAAc,EAAE,CAAC,kBAAkB,CAAC;QACpC,iBAAiB,EAAE,CAAC,kBAAkB,CAAC;QACvC,iBAAiB,EAAE,CAAC,kBAAkB,CAAC;QAEvC,cAAc,EAAE,CAAC,cAAc,CAAC;QAChC,gBAAgB,EAAE,CAAC,qBAAqB,CAAC;QACzC,YAAY,EAAE,CAAC,iBAAiB,CAAC;KAClC,AAAC;IAEF,kDAAkD;IAClD,MAAMC,UAAU,GAAG9B,IAAI,CAACY,CAAC,CAAC,CAAC,CAAC,AAAC;IAC7B,IAAIkB,UAAU,IAAIT,YAAY,EAAE;QAC9B,MAAMU,WAAW,GAAGV,YAAY,CAACS,UAAU,CAAC,AAAC;QAC7C1B,OAAO,CAACC,GAAG,EAAE,CAAC;QACdD,OAAO,CAACC,GAAG,CACTI,MAAK,QAAA,CAACC,MAAM,CAAC,sBAAsB,EAAEoB,UAAU,CAAC,sDAAsD,EAAEC,WAAW,CAAC,SAAS,CAAC,CAC/H,CAAC;QACF3B,OAAO,CAACC,GAAG,EAAE,CAAC;QACdrC,OAAO,CAACuC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IACD,MAAMyB,UAAU,GAAG;QAAC,MAAM;QAAE,YAAY;KAAC,AAAC;IAC1C,IAAIA,UAAU,CAACC,QAAQ,CAACH,UAAU,CAAC,EAAE;QACnC1B,OAAO,CAACC,GAAG,EAAE,CAAC;QACdD,OAAO,CAACC,GAAG,CAACI,MAAK,QAAA,CAACC,MAAM,CAAC,sBAAsB,EAAEoB,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;QAC9E1B,OAAO,CAACC,GAAG,EAAE,CAAC;QACdrC,OAAO,CAACuC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;CACF;AAED,MAAM2B,OAAO,GAAGvB,YAAY,GAAGX,IAAI,CAACY,CAAC,CAAC,CAAC,CAAC,GAAGzC,UAAU,AAAC;AACtD,MAAMgE,WAAW,GAAGxB,YAAY,GAAGX,IAAI,CAACY,CAAC,CAACwB,KAAK,CAAC,CAAC,CAAC,GAAGpC,IAAI,CAACY,CAAC,AAAC;AAE5D,6CAA6C;AAC7C,IAAIZ,IAAI,CAAC,QAAQ,CAAC,EAAE;IAClBmC,WAAW,CAACE,IAAI,CAAC,QAAQ,CAAC,CAAC;CAC5B;AAED,qBAAqB;AACrBrE,OAAO,CAACsE,EAAE,CAAC,QAAQ,EAAE,IAAMtE,OAAO,CAACuC,IAAI,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC;AAC5CvC,OAAO,CAACsE,EAAE,CAAC,SAAS,EAAE,IAAMtE,OAAO,CAACuC,IAAI,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC;AAE7CnC,QAAQ,CAAC8D,OAAO,CAAC,EAAE,CAAC7D,IAAI,CAAC,CAACkE,IAAI,GAAK;IACjCA,IAAI,CAACJ,WAAW,CAAC,CAAC;IAElB,4EAA4E;IAC5E,uFAAuF;IACvF,qFAAqF;IACrF,MAAM,EAAEK,aAAa,CAAA,EAAE,GACrBC,OAAO,CAAC,0CAA0C,CAAC,AAA6D,AAAC;IACnHD,aAAa,CAAC,QAAQ,EAAE;QACtBE,MAAM,EAAE,CAAC,KAAK,EAAER,OAAO,CAAC,CAAC;QACzBS,MAAM,EAAE,UAAU;QAClBC,cAAc,EAAE5E,OAAO,CAACC,GAAG,CAACqC,cAAc;KAC3C,CAAC,CAAC;CACJ,CAAC,CAAC"}