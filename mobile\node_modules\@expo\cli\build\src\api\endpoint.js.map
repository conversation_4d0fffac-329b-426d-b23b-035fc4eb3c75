{"version": 3, "sources": ["../../../src/api/endpoint.ts"], "sourcesContent": ["import { env } from '../utils/env';\n\n/** Get the URL for the expo.dev API. */\nexport function getExpoApiBaseUrl(): string {\n  if (env.EXPO_STAGING) {\n    return `https://staging-api.expo.dev`;\n  } else if (env.EXPO_LOCAL) {\n    return `http://127.0.0.1:3000`;\n  } else {\n    return `https://api.expo.dev`;\n  }\n}\n"], "names": ["getExpoApiBaseUrl", "env", "EXPO_STAGING", "EXPO_LOCAL"], "mappings": "AAAA;;;;QAGgBA,iBAAiB,GAAjBA,iBAAiB;AAHb,IAAA,IAAc,WAAd,cAAc,CAAA;AAG3B,SAASA,iBAAiB,GAAW;IAC1C,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE;QACpB,OAAO,CAAC,4BAA4B,CAAC,CAAC;KACvC,MAAM,IAAID,IAAG,IAAA,CAACE,UAAU,EAAE;QACzB,OAAO,CAAC,qBAAqB,CAAC,CAAC;KAChC,MAAM;QACL,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAC/B;CACF"}