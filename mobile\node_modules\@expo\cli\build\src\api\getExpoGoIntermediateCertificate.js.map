{"version": 3, "sources": ["../../../src/api/getExpoGoIntermediateCertificate.ts"], "sourcesContent": ["import { CommandError } from '../utils/errors';\nimport { fetchAsync } from './rest/client';\n\nexport async function getExpoGoIntermediateCertificateAsync(easProjectId: string): Promise<string> {\n  const response = await fetchAsync(\n    `projects/${encodeURIComponent(\n      easProjectId\n    )}/development-certificates/expo-go-intermediate-certificate`,\n    {\n      method: 'GET',\n    }\n  );\n  if (!response.ok) {\n    throw new CommandError('API', `Unexpected error from Expo servers: ${response.statusText}.`);\n  }\n  const buffer = await response.buffer();\n  return buffer.toString('utf8');\n}\n"], "names": ["getExpoGoIntermediateCertificateAsync", "easProjectId", "response", "fetchAsync", "encodeURIComponent", "method", "ok", "CommandError", "statusText", "buffer", "toString"], "mappings": "AAAA;;;;QAGsBA,qCAAqC,GAArCA,qCAAqC;AAH9B,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACnB,IAAA,OAAe,WAAf,eAAe,CAAA;AAEnC,eAAeA,qCAAqC,CAACC,YAAoB,EAAmB;IACjG,MAAMC,QAAQ,GAAG,MAAMC,CAAAA,GAAAA,OAAU,AAOhC,CAAA,WAPgC,CAC/B,CAAC,SAAS,EAAEC,kBAAkB,CAC5BH,YAAY,CACb,CAAC,0DAA0D,CAAC,EAC7D;QACEI,MAAM,EAAE,KAAK;KACd,CACF,AAAC;IACF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,OAAY,aAAA,CAAC,KAAK,EAAE,CAAC,oCAAoC,EAAEL,QAAQ,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9F;IACD,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACO,MAAM,EAAE,AAAC;IACvC,OAAOA,MAAM,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;CAChC"}