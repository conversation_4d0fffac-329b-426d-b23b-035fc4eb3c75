{"version": 3, "sources": ["../../../src/api/getExpoSchema.ts"], "sourcesContent": ["import { JSONObject } from '@expo/json-file';\nimport fs from 'fs';\nimport schemaDerefSync from 'json-schema-deref-sync';\nimport path from 'path';\n\nimport { env } from '../utils/env';\nimport { CommandError } from '../utils/errors';\nimport { createCachedFetch } from './rest/client';\n\nexport type Schema = any;\n\nexport type AssetSchema = {\n  fieldPath: string;\n};\n\nconst schemaJson: { [sdkVersion: string]: Schema } = {};\n\n// TODO: Maybe move json-schema-deref-sync out of api (1.58MB -- lodash)\n// https://packagephobia.com/result?p=json-schema-deref-sync\nasync function getSchemaAsync(sdkVersion: string): Promise<Schema> {\n  const json = await getSchemaJSONAsync(sdkVersion);\n  return schemaDerefSync(json.schema);\n}\n\n/**\n * Array of schema nodes that refer to assets along with their field path (eg. 'notification.icon')\n *\n * @param sdkVersion\n */\nexport async function getAssetSchemasAsync(sdkVersion: string = 'UNVERSIONED'): Promise<string[]> {\n  // If no SDK version is available then fall back to unversioned\n  const schema = await getSchemaAsync(sdkVersion);\n  const assetSchemas: string[] = [];\n  const visit = (node: Schema, fieldPath: string) => {\n    if (node.meta && node.meta.asset) {\n      assetSchemas.push(fieldPath);\n    }\n    const properties = node.properties;\n    if (properties) {\n      Object.keys(properties).forEach((property) =>\n        visit(properties[property], `${fieldPath}${fieldPath.length > 0 ? '.' : ''}${property}`)\n      );\n    }\n  };\n  visit(schema, '');\n\n  return assetSchemas;\n}\n\nasync function getSchemaJSONAsync(sdkVersion: string): Promise<{ schema: Schema }> {\n  if (env.EXPO_UNIVERSE_DIR) {\n    return JSON.parse(\n      fs\n        .readFileSync(\n          path.join(\n            env.EXPO_UNIVERSE_DIR,\n            'server',\n            'www',\n            'xdl-schemas',\n            'UNVERSIONED-schema.json'\n          )\n        )\n        .toString()\n    );\n  }\n\n  if (!schemaJson[sdkVersion]) {\n    try {\n      schemaJson[sdkVersion] = await getConfigurationSchemaAsync(sdkVersion);\n    } catch (e: any) {\n      if (e.code === 'INVALID_JSON') {\n        throw new CommandError('INVALID_JSON', `Couldn't read schema from server`);\n      }\n\n      throw e;\n    }\n  }\n\n  return schemaJson[sdkVersion];\n}\n\nasync function getConfigurationSchemaAsync(sdkVersion: string): Promise<JSONObject> {\n  // Reconstruct the cached fetch since caching could be disabled.\n  const fetchAsync = createCachedFetch({\n    cacheDirectory: 'schema-cache',\n    // We'll use a 1 week cache for versions so older versions get flushed out eventually.\n    ttl: 1000 * 60 * 60 * 24 * 7,\n  });\n  const response = await fetchAsync(`project/configuration/schema/${sdkVersion}`);\n  const { data } = await response.json();\n  return data;\n}\n"], "names": ["getAssetSchemasAsync", "schema<PERSON>son", "getSchemaAsync", "sdkVersion", "json", "getSchemaJSONAsync", "schemaDerefSync", "schema", "assetSchemas", "visit", "node", "fieldPath", "meta", "asset", "push", "properties", "Object", "keys", "for<PERSON>ach", "property", "length", "env", "EXPO_UNIVERSE_DIR", "JSON", "parse", "fs", "readFileSync", "path", "join", "toString", "getConfigurationSchemaAsync", "e", "code", "CommandError", "fetchAsync", "createCachedFetch", "cacheDirectory", "ttl", "response", "data"], "mappings": "AAAA;;;;QA6BsBA,oBAAoB,GAApBA,oBAAoB;AA5B3B,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACS,IAAA,oBAAwB,kCAAxB,wBAAwB,EAAA;AACnC,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEH,IAAA,IAAc,WAAd,cAAc,CAAA;AACL,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACZ,IAAA,OAAe,WAAf,eAAe,CAAA;;;;;;AAQjD,MAAMC,UAAU,GAAqC,EAAE,AAAC;AAExD,wEAAwE;AACxE,4DAA4D;AAC5D,eAAeC,cAAc,CAACC,UAAkB,EAAmB;IACjE,MAAMC,IAAI,GAAG,MAAMC,kBAAkB,CAACF,UAAU,CAAC,AAAC;IAClD,OAAOG,CAAAA,GAAAA,oBAAe,AAAa,CAAA,QAAb,CAACF,IAAI,CAACG,MAAM,CAAC,CAAC;CACrC;AAOM,eAAeP,oBAAoB,CAACG,UAAkB,GAAG,aAAa,EAAqB;IAChG,+DAA+D;IAC/D,MAAMI,MAAM,GAAG,MAAML,cAAc,CAACC,UAAU,CAAC,AAAC;IAChD,MAAMK,YAAY,GAAa,EAAE,AAAC;IAClC,MAAMC,KAAK,GAAG,CAACC,IAAY,EAAEC,SAAiB,GAAK;QACjD,IAAID,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAACC,KAAK,EAAE;YAChCL,YAAY,CAACM,IAAI,CAACH,SAAS,CAAC,CAAC;SAC9B;QACD,MAAMI,UAAU,GAAGL,IAAI,CAACK,UAAU,AAAC;QACnC,IAAIA,UAAU,EAAE;YACdC,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAACG,OAAO,CAAC,CAACC,QAAQ,GACvCV,KAAK,CAACM,UAAU,CAACI,QAAQ,CAAC,EAAE,CAAC,EAAER,SAAS,CAAC,EAAEA,SAAS,CAACS,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAED,QAAQ,CAAC,CAAC,CAAC;YAAA,CACzF,CAAC;SACH;KACF,AAAC;IACFV,KAAK,CAACF,MAAM,EAAE,EAAE,CAAC,CAAC;IAElB,OAAOC,YAAY,CAAC;CACrB;AAED,eAAeH,kBAAkB,CAACF,UAAkB,EAA+B;IACjF,IAAIkB,IAAG,IAAA,CAACC,iBAAiB,EAAE;QACzB,OAAOC,IAAI,CAACC,KAAK,CACfC,GAAE,QAAA,CACCC,YAAY,CACXC,KAAI,QAAA,CAACC,IAAI,CACPP,IAAG,IAAA,CAACC,iBAAiB,EACrB,QAAQ,EACR,KAAK,EACL,aAAa,EACb,yBAAyB,CAC1B,CACF,CACAO,QAAQ,EAAE,CACd,CAAC;KACH;IAED,IAAI,CAAC5B,UAAU,CAACE,UAAU,CAAC,EAAE;QAC3B,IAAI;YACFF,UAAU,CAACE,UAAU,CAAC,GAAG,MAAM2B,2BAA2B,CAAC3B,UAAU,CAAC,CAAC;SACxE,CAAC,OAAO4B,CAAC,EAAO;YACf,IAAIA,CAAC,CAACC,IAAI,KAAK,cAAc,EAAE;gBAC7B,MAAM,IAAIC,OAAY,aAAA,CAAC,cAAc,EAAE,CAAC,gCAAgC,CAAC,CAAC,CAAC;aAC5E;YAED,MAAMF,CAAC,CAAC;SACT;KACF;IAED,OAAO9B,UAAU,CAACE,UAAU,CAAC,CAAC;CAC/B;AAED,eAAe2B,2BAA2B,CAAC3B,UAAkB,EAAuB;IAClF,gEAAgE;IAChE,MAAM+B,UAAU,GAAGC,CAAAA,GAAAA,OAAiB,AAIlC,CAAA,kBAJkC,CAAC;QACnCC,cAAc,EAAE,cAAc;QAC9B,sFAAsF;QACtFC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;KAC7B,CAAC,AAAC;IACH,MAAMC,QAAQ,GAAG,MAAMJ,UAAU,CAAC,CAAC,6BAA6B,EAAE/B,UAAU,CAAC,CAAC,CAAC,AAAC;IAChF,MAAM,EAAEoC,IAAI,CAAA,EAAE,GAAG,MAAMD,QAAQ,CAAClC,IAAI,EAAE,AAAC;IACvC,OAAOmC,IAAI,CAAC;CACb"}