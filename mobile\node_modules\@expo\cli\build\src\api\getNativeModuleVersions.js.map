{"version": 3, "sources": ["../../../src/api/getNativeModuleVersions.ts"], "sourcesContent": ["import { CommandError } from '../utils/errors';\nimport { createCachedFetch } from './rest/client';\n\ninterface NativeModule {\n  npmPackage: string;\n  versionRange: string;\n}\ntype BundledNativeModuleList = NativeModule[];\n\nexport type BundledNativeModules = Record<string, string>;\n\n/**\n * The endpoint returns the list of bundled native modules for a given SDK version.\n * The data is populated by the `et sync-bundled-native-modules` script from expo/expo repo.\n * See the code for more details:\n * https://github.com/expo/expo/blob/main/tools/src/commands/SyncBundledNativeModules.ts\n *\n * Example result:\n * [\n *   {\n *     id: \"79285187-e5c4-47f7-b6a9-664f5d16f0db\",\n *     sdkVersion: \"41.0.0\",\n *     npmPackage: \"expo-camera\",\n *     versionRange: \"~10.1.0\",\n *     createdAt: \"2021-04-29T09:34:32.825Z\",\n *     updatedAt: \"2021-04-29T09:34:32.825Z\"\n *   },\n *   ...\n * ]\n */\nexport async function getNativeModuleVersionsAsync(\n  sdkVersion: string\n): Promise<BundledNativeModules> {\n  const fetchAsync = createCachedFetch({\n    cacheDirectory: 'native-modules-cache',\n    // 1 minute cache\n    ttl: 1000 * 60 * 1,\n  });\n  const results = await fetchAsync(`sdks/${sdkVersion}/native-modules`);\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when fetching version info from Expo servers: ${results.statusText}.`\n    );\n  }\n  const { data } = await results.json();\n  if (!data.length) {\n    throw new CommandError('VERSIONS', 'The bundled native module list from the Expo API is empty');\n  }\n  return fromBundledNativeModuleList(data);\n}\n\nfunction fromBundledNativeModuleList(list: BundledNativeModuleList): BundledNativeModules {\n  return list.reduce((acc, i) => {\n    acc[i.npmPackage] = i.versionRange;\n    return acc;\n  }, {} as BundledNativeModules);\n}\n"], "names": ["getNativeModuleVersionsAsync", "sdkVersion", "fetchAsync", "createCachedFetch", "cacheDirectory", "ttl", "results", "ok", "CommandError", "statusText", "data", "json", "length", "fromBundledNativeModuleList", "list", "reduce", "acc", "i", "npmPackage", "versionRange"], "mappings": "AAAA;;;;QA8BsBA,4BAA4B,GAA5BA,4BAA4B;AA9BrB,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACZ,IAAA,OAAe,WAAf,eAAe,CAAA;AA6B1C,eAAeA,4BAA4B,CAChDC,UAAkB,EACa;IAC/B,MAAMC,UAAU,GAAGC,CAAAA,GAAAA,OAAiB,AAIlC,CAAA,kBAJkC,CAAC;QACnCC,cAAc,EAAE,sBAAsB;QACtC,iBAAiB;QACjBC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC;KACnB,CAAC,AAAC;IACH,MAAMC,OAAO,GAAG,MAAMJ,UAAU,CAAC,CAAC,KAAK,EAAED,UAAU,CAAC,eAAe,CAAC,CAAC,AAAC;IACtE,IAAI,CAACK,OAAO,CAACC,EAAE,EAAE;QACf,MAAM,IAAIC,OAAY,aAAA,CACpB,KAAK,EACL,CAAC,kEAAkE,EAAEF,OAAO,CAACG,UAAU,CAAC,CAAC,CAAC,CAC3F,CAAC;KACH;IACD,MAAM,EAAEC,IAAI,CAAA,EAAE,GAAG,MAAMJ,OAAO,CAACK,IAAI,EAAE,AAAC;IACtC,IAAI,CAACD,IAAI,CAACE,MAAM,EAAE;QAChB,MAAM,IAAIJ,OAAY,aAAA,CAAC,UAAU,EAAE,2DAA2D,CAAC,CAAC;KACjG;IACD,OAAOK,2BAA2B,CAACH,IAAI,CAAC,CAAC;CAC1C;AAED,SAASG,2BAA2B,CAACC,IAA6B,EAAwB;IACxF,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,GAAK;QAC7BD,GAAG,CAACC,CAAC,CAACC,UAAU,CAAC,GAAGD,CAAC,CAACE,YAAY,CAAC;QACnC,OAAOH,GAAG,CAAC;KACZ,EAAE,EAAE,CAAyB,CAAC;CAChC"}