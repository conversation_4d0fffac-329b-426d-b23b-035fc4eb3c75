{"version": 3, "sources": ["../../../src/api/getProjectDevelopmentCertificate.ts"], "sourcesContent": ["import { CommandError } from '../utils/errors';\nimport { fetchAsync } from './rest/client';\n\nexport async function getProjectDevelopmentCertificateAsync(\n  easProjectId: string,\n  csrPEM: string\n): Promise<string> {\n  const response = await fetchAsync(\n    `projects/${encodeURIComponent(easProjectId)}/development-certificates`,\n    {\n      method: 'POST',\n      body: JSON.stringify({\n        csrPEM,\n      }),\n    }\n  );\n  if (!response.ok) {\n    throw new CommandError('API', `Unexpected error from Expo servers: ${response.statusText}.`);\n  }\n  const buffer = await response.buffer();\n  return buffer.toString('utf8');\n}\n"], "names": ["getProjectDevelopmentCertificateAsync", "easProjectId", "csrPEM", "response", "fetchAsync", "encodeURIComponent", "method", "body", "JSON", "stringify", "ok", "CommandError", "statusText", "buffer", "toString"], "mappings": "AAAA;;;;QAGsBA,qCAAqC,GAArCA,qCAAqC;AAH9B,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACnB,IAAA,OAAe,WAAf,eAAe,CAAA;AAEnC,eAAeA,qCAAqC,CACzDC,YAAoB,EACpBC,MAAc,EACG;IACjB,MAAMC,QAAQ,GAAG,MAAMC,CAAAA,GAAAA,OAAU,AAQhC,CAAA,WARgC,CAC/B,CAAC,SAAS,EAAEC,kBAAkB,CAACJ,YAAY,CAAC,CAAC,yBAAyB,CAAC,EACvE;QACEK,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBP,MAAM;SACP,CAAC;KACH,CACF,AAAC;IACF,IAAI,CAACC,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,OAAY,aAAA,CAAC,KAAK,EAAE,CAAC,oCAAoC,EAAER,QAAQ,CAACS,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9F;IACD,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACU,MAAM,EAAE,AAAC;IACvC,OAAOA,MAAM,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;CAChC"}