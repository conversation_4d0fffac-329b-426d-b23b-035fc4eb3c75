{"version": 3, "sources": ["../../../src/api/getVersions.ts"], "sourcesContent": ["import { CommandError } from '../utils/errors';\nimport { createCachedFetch } from './rest/client';\n\n/** Represents version info for a particular SDK. */\nexport type SDKVersion = {\n  /** @example \"2.16.1\" */\n  iosVersion?: string;\n  /** @example \"https://dpq5q02fu5f55.cloudfront.net/Exponent-2.17.4.tar.gz\" */\n  iosClientUrl?: string;\n  /** @example \"https://dev.to/expo/expo-sdk-38-is-now-available-5aa0\" */\n  releaseNoteUrl?: string;\n  /** @example \"2.17.4\" */\n  iosClientVersion?: string;\n  /** @example \"https://d1ahtucjixef4r.cloudfront.net/Exponent-2.16.1.apk\" */\n  androidClientUrl?: string;\n  /** @example \"2.16.1\" */\n  androidClientVersion?: string;\n  /** @example { \"typescript\": \"~3.9.5\" } */\n  relatedPackages?: Record<string, string>;\n\n  facebookReactNativeVersion: string;\n\n  facebookReactVersion?: string;\n\n  beta?: boolean;\n};\n\nexport type SDKVersions = Record<string, SDKVersion>;\n\nexport type Versions = {\n  androidUrl: string;\n  androidVersion: string;\n  iosUrl: string;\n  iosVersion: string;\n  sdkVersions: SDKVersions;\n};\n\n/** Get versions from remote endpoint. */\nexport async function getVersionsAsync({\n  skipCache,\n}: { skipCache?: boolean } = {}): Promise<Versions> {\n  // Reconstruct the cached fetch since caching could be disabled.\n  const fetchAsync = createCachedFetch({\n    skipCache,\n    cacheDirectory: 'versions-cache',\n    // We'll use a 5 minute cache to ensure we stay relatively up to date.\n    ttl: 1000 * 60 * 5,\n  });\n\n  const results = await fetchAsync('versions/latest');\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when fetching version info from Expo servers: ${results.statusText}.`\n    );\n  }\n  const json = await results.json();\n  return json.data;\n}\n"], "names": ["getVersionsAsync", "<PERSON><PERSON><PERSON>", "fetchAsync", "createCachedFetch", "cacheDirectory", "ttl", "results", "ok", "CommandError", "statusText", "json", "data"], "mappings": "AAAA;;;;QAsCsBA,gBAAgB,GAAhBA,gBAAgB;AAtCT,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACZ,IAAA,OAAe,WAAf,eAAe,CAAA;AAqC1C,eAAeA,gBAAgB,CAAC,EACrCC,SAAS,CAAA,EACe,GAAG,EAAE,EAAqB;IAClD,gEAAgE;IAChE,MAAMC,UAAU,GAAGC,CAAAA,GAAAA,OAAiB,AAKlC,CAAA,kBALkC,CAAC;QACnCF,SAAS;QACTG,cAAc,EAAE,gBAAgB;QAChC,sEAAsE;QACtEC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC;KACnB,CAAC,AAAC;IAEH,MAAMC,OAAO,GAAG,MAAMJ,UAAU,CAAC,iBAAiB,CAAC,AAAC;IACpD,IAAI,CAACI,OAAO,CAACC,EAAE,EAAE;QACf,MAAM,IAAIC,OAAY,aAAA,CACpB,KAAK,EACL,CAAC,kEAAkE,EAAEF,OAAO,CAACG,UAAU,CAAC,CAAC,CAAC,CAC3F,CAAC;KACH;IACD,MAAMC,IAAI,GAAG,MAAMJ,OAAO,CAACI,IAAI,EAAE,AAAC;IAClC,OAAOA,IAAI,CAACC,IAAI,CAAC;CAClB"}