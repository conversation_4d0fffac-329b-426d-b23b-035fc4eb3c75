{"version": 3, "sources": ["../../../../src/api/graphql/client.ts"], "sourcesContent": ["import {\n  cacheExchange,\n  Client,\n  CombinedError as GraphqlError,\n  createClient as createUrqlClient,\n  dedupExchange,\n  fetchExchange,\n  OperationContext,\n  OperationResult,\n  PromisifiedSource,\n  TypedDocumentNode,\n} from '@urql/core';\nimport { retryExchange } from '@urql/exchange-retry';\nimport { DocumentNode } from 'graphql';\nimport fetch from 'node-fetch';\n\nimport * as Log from '../../log';\nimport { getExpoApiBaseUrl } from '../endpoint';\nimport { wrapFetchWithOffline } from '../rest/wrapFetchWithOffline';\nimport { wrapFetchWithProxy } from '../rest/wrapFetchWithProxy';\nimport UserSettings from '../user/UserSettings';\n\ntype AccessTokenHeaders = {\n  authorization: string;\n};\n\ntype SessionHeaders = {\n  'expo-session': string;\n};\n\nexport const graphqlClient = createUrqlClient({\n  url: getExpoApiBaseUrl() + '/graphql',\n  exchanges: [\n    dedupExchange,\n    cacheExchange,\n    retryExchange({\n      maxDelayMs: 4000,\n      retryIf: (err) =>\n        !!(err && (err.networkError || err.graphQLErrors.some((e) => e?.extensions?.isTransient))),\n    }),\n    fetchExchange,\n  ],\n  // @ts-ignore Type 'typeof fetch' is not assignable to type '(input: RequestInfo, init?: RequestInit | undefined) => Promise<Response>'.\n  fetch: wrapFetchWithOffline(wrapFetchWithProxy(fetch)),\n  fetchOptions: (): { headers?: AccessTokenHeaders | SessionHeaders } => {\n    const token = UserSettings.getAccessToken();\n    if (token) {\n      return {\n        headers: {\n          authorization: `Bearer ${token}`,\n        },\n      };\n    }\n    const sessionSecret = UserSettings.getSession()?.sessionSecret;\n    if (sessionSecret) {\n      return {\n        headers: {\n          'expo-session': sessionSecret,\n        },\n      };\n    }\n    return {};\n  },\n}) as StricterClient;\n\n/* Please specify additionalTypenames in your Graphql queries */\nexport interface StricterClient extends Client {\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  query<Data = any, Variables extends object = {}>(\n    query: DocumentNode | TypedDocumentNode<Data, Variables> | string,\n    variables: Variables | undefined,\n    context: Partial<OperationContext> & { additionalTypenames: string[] }\n  ): PromisifiedSource<OperationResult<Data, Variables>>;\n}\n\nexport async function withErrorHandlingAsync<T>(promise: Promise<OperationResult<T>>): Promise<T> {\n  const { data, error } = await promise;\n\n  if (error) {\n    if (error.graphQLErrors.some((e) => e?.extensions?.isTransient)) {\n      Log.error(`We've encountered a transient error, please try again shortly.`);\n    }\n    throw error;\n  }\n\n  // Check for a malformed response. This only checks the root query's existence. It doesn't affect\n  // returning responses with an empty result set.\n  if (!data) {\n    throw new Error('Returned query result data is null!');\n  }\n\n  return data;\n}\n\nexport { GraphqlError };\n"], "names": ["GraphqlError", "withErrorHandlingAsync", "Log", "graphqlClient", "createUrqlClient", "url", "getExpoApiBaseUrl", "exchanges", "dedupExchange", "cacheExchange", "retryExchange", "max<PERSON>elay<PERSON>", "retryIf", "err", "networkError", "graphQLErrors", "some", "e", "extensions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchExchange", "fetch", "wrapFetchWithOffline", "wrapFetchWithProxy", "fetchOptions", "UserSettings", "token", "getAccessToken", "headers", "authorization", "sessionSecret", "getSession", "promise", "data", "error", "Error"], "mappings": "AAAA;;;;+BA8FS<PERSON>,cAAY;;;eAAZA,KAAY,cAAA;;;QAnBCC,sBAAsB,GAAtBA,sBAAsB;;AAhErC,IAAA,KAAY,WAAZ,YAAY,CAAA;AACW,IAAA,cAAsB,WAAtB,sBAAsB,CAAA;AAElC,IAAA,UAAY,kCAAZ,YAAY,EAAA;AAElBC,IAAAA,GAAG,mCAAM,WAAW,EAAjB;AACmB,IAAA,SAAa,WAAb,aAAa,CAAA;AACV,IAAA,qBAA8B,WAA9B,8BAA8B,CAAA;AAChC,IAAA,mBAA4B,WAA5B,4BAA4B,CAAA;AACtC,IAAA,aAAsB,kCAAtB,sBAAsB,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxC,MAAMC,aAAa,GAAGC,CAAAA,GAAAA,KAAgB,AAiC3C,CAAA,aAjC2C,CAAC;IAC5CC,GAAG,EAAEC,CAAAA,GAAAA,SAAiB,AAAE,CAAA,kBAAF,EAAE,GAAG,UAAU;IACrCC,SAAS,EAAE;QACTC,KAAa,cAAA;QACbC,KAAa,cAAA;QACbC,CAAAA,GAAAA,cAAa,AAIX,CAAA,cAJW,CAAC;YACZC,UAAU,EAAE,IAAI;YAChBC,OAAO,EAAE,CAACC,GAAG;gBACX,OAAA,CAAC,CAAC,CAACA,GAAG,IAAI,CAACA,GAAG,CAACC,YAAY,IAAID,GAAG,CAACE,aAAa,CAACC,IAAI,CAAC,CAACC,CAAC;wBAAKA,GAAa;oBAAbA,OAAAA,CAAC,QAAY,GAAbA,KAAAA,CAAa,GAAbA,CAAAA,GAAa,GAAbA,CAAC,CAAEC,UAAU,SAAA,GAAbD,KAAAA,CAAa,GAAbA,GAAa,CAAEE,WAAW,AAAb,CAAa;iBAAA,CAAC,CAAC,CAAC,CAAA;aAAA;SAC7F,CAAC;QACFC,KAAa,cAAA;KACd;IACD,wIAAwI;IACxIC,KAAK,EAAEC,CAAAA,GAAAA,qBAAoB,AAA2B,CAAA,qBAA3B,CAACC,CAAAA,GAAAA,mBAAkB,AAAO,CAAA,mBAAP,CAACF,UAAK,QAAA,CAAC,CAAC;IACtDG,YAAY,EAAE,IAAyD;YAS/CC,GAAyB;QAR/C,MAAMC,KAAK,GAAGD,aAAY,QAAA,CAACE,cAAc,EAAE,AAAC;QAC5C,IAAID,KAAK,EAAE;YACT,OAAO;gBACLE,OAAO,EAAE;oBACPC,aAAa,EAAE,CAAC,OAAO,EAAEH,KAAK,CAAC,CAAC;iBACjC;aACF,CAAC;SACH;QACD,MAAMI,aAAa,GAAGL,CAAAA,GAAyB,GAAzBA,aAAY,QAAA,CAACM,UAAU,EAAE,SAAe,GAAxCN,KAAAA,CAAwC,GAAxCA,GAAyB,CAAEK,aAAa,AAAC;QAC/D,IAAIA,aAAa,EAAE;YACjB,OAAO;gBACLF,OAAO,EAAE;oBACP,cAAc,EAAEE,aAAa;iBAC9B;aACF,CAAC;SACH;QACD,OAAO,EAAE,CAAC;KACX;CACF,CAAC,AAAkB,AAAC;QAjCR3B,aAAa,GAAbA,aAAa;AA6CnB,eAAeF,sBAAsB,CAAI+B,OAAoC,EAAc;IAChG,MAAM,EAAEC,IAAI,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAG,MAAMF,OAAO,AAAC;IAEtC,IAAIE,KAAK,EAAE;QACT,IAAIA,KAAK,CAACnB,aAAa,CAACC,IAAI,CAAC,CAACC,CAAC;gBAAKA,GAAa;YAAbA,OAAAA,CAAC,QAAY,GAAbA,KAAAA,CAAa,GAAbA,CAAAA,GAAa,GAAbA,CAAC,CAAEC,UAAU,SAAA,GAAbD,KAAAA,CAAa,GAAbA,GAAa,CAAEE,WAAW,AAAb,CAAa;SAAA,CAAC,EAAE;YAC/DjB,GAAG,CAACgC,KAAK,CAAC,CAAC,8DAA8D,CAAC,CAAC,CAAC;SAC7E;QACD,MAAMA,KAAK,CAAC;KACb;IAED,iGAAiG;IACjG,gDAAgD;IAChD,IAAI,CAACD,IAAI,EAAE;QACT,MAAM,IAAIE,KAAK,CAAC,qCAAqC,CAAC,CAAC;KACxD;IAED,OAAOF,IAAI,CAAC;CACb"}