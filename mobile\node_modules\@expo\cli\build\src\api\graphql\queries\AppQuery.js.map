{"version": 3, "sources": ["../../../../../src/api/graphql/queries/AppQuery.ts"], "sourcesContent": ["import { print } from 'graphql';\nimport gql from 'graphql-tag';\n\nimport { AppByIdQuery } from '../../../graphql/generated';\nimport { graphqlClient, withErrorHandlingAsync } from '../client';\nimport { AppFragmentNode } from '../types/App';\n\nexport const AppQuery = {\n  async byIdAsync(projectId: string): Promise<AppByIdQuery['app']['byId']> {\n    const data = await withErrorHandlingAsync(\n      graphqlClient\n        .query<AppByIdQuery>(\n          gql`\n            query AppByIdQuery($appId: String!) {\n              app {\n                byId(appId: $appId) {\n                  id\n                  ...AppFragment\n                }\n              }\n            }\n            ${print(AppFragmentNode)}\n          `,\n          { appId: projectId },\n          {\n            additionalTypenames: ['App'],\n          }\n        )\n        .toPromise()\n    );\n    return data.app.byId;\n  },\n};\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "byIdAsync", "projectId", "data", "withErrorHandlingAsync", "graphqlClient", "query", "gql", "print", "AppFragmentNode", "appId", "additionalTypenames", "to<PERSON>romise", "app", "byId"], "mappings": "AAAA;;;;;AAAsB,IAAA,QAAS,WAAT,SAAS,CAAA;AACf,IAAA,WAAa,kCAAb,aAAa,EAAA;AAGyB,IAAA,OAAW,WAAX,WAAW,CAAA;AACjC,IAAA,IAAc,WAAd,cAAc,CAAA;;;;;;AAEvC,MAAMA,QAAQ,GAAG;IACtB,MAAMC,SAAS,EAACC,SAAiB,EAAwC;QACvE,MAAMC,IAAI,GAAG,MAAMC,CAAAA,GAAAA,OAAsB,AAoBxC,CAAA,uBApBwC,CACvCC,OAAa,cAAA,CACVC,KAAK,CACJC,WAAG,QAAA,CAAC;;;;;;;;;YASF,EAAEC,CAAAA,GAAAA,QAAK,AAAiB,CAAA,MAAjB,CAACC,IAAe,gBAAA,CAAC,CAAC;UAC3B,CAAC,EACD;YAAEC,KAAK,EAAER,SAAS;SAAE,EACpB;YACES,mBAAmB,EAAE;gBAAC,KAAK;aAAC;SAC7B,CACF,CACAC,SAAS,EAAE,CACf,AAAC;QACF,OAAOT,IAAI,CAACU,GAAG,CAACC,IAAI,CAAC;KACtB;CACF,AAAC;QAzBWd,QAAQ,GAARA,QAAQ"}