{"version": 3, "sources": ["../../../../../src/api/graphql/queries/UserQuery.ts"], "sourcesContent": ["import gql from 'graphql-tag';\n\nimport { CurrentUserQuery } from '../../../graphql/generated';\nimport { graphqlClient, withErrorHandlingAsync } from '../client';\n\nexport const UserQuery = {\n  async currentUserAsync(): Promise<CurrentUserQuery['meActor']> {\n    const data = await withErrorHandlingAsync(\n      graphqlClient\n        .query<CurrentUserQuery>(\n          gql`\n            query CurrentUser {\n              meActor {\n                __typename\n                id\n                ... on UserActor {\n                  primaryAccount {\n                    id\n                  }\n                  username\n                }\n                ... on Robot {\n                  firstName\n                }\n                accounts {\n                  id\n                  users {\n                    actor {\n                      id\n                    }\n                    permissions\n                  }\n                }\n              }\n            }\n          `,\n          /* variables */ undefined,\n          {\n            additionalTypenames: ['User'],\n          }\n        )\n        .toPromise()\n    );\n\n    return data.meActor;\n  },\n};\n"], "names": ["UserQuery", "currentUserAsync", "data", "withErrorHandlingAsync", "graphqlClient", "query", "gql", "undefined", "additionalTypenames", "to<PERSON>romise", "meActor"], "mappings": "AAAA;;;;;AAAgB,IAAA,WAAa,kCAAb,aAAa,EAAA;AAGyB,IAAA,OAAW,WAAX,WAAW,CAAA;;;;;;AAE1D,MAAMA,SAAS,GAAG;IACvB,MAAMC,gBAAgB,IAAyC;QAC7D,MAAMC,IAAI,GAAG,MAAMC,CAAAA,GAAAA,OAAsB,AAmCxC,CAAA,uBAnCwC,CACvCC,OAAa,cAAA,CACVC,KAAK,CACJC,WAAG,QAAA,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;UAyBJ,CAAC,EACD,eAAe,CAACC,SAAS,EACzB;YACEC,mBAAmB,EAAE;gBAAC,MAAM;aAAC;SAC9B,CACF,CACAC,SAAS,EAAE,CACf,AAAC;QAEF,OAAOP,IAAI,CAACQ,OAAO,CAAC;KACrB;CACF,AAAC;QAzCWV,SAAS,GAATA,SAAS"}