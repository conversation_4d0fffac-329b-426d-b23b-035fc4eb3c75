{"version": 3, "sources": ["../../../../../src/api/graphql/types/App.ts"], "sourcesContent": ["import gql from 'graphql-tag';\n\nexport const AppFragmentNode = gql`\n  fragment AppFragment on App {\n    id\n    scopeKey\n    ownerAccount {\n      id\n    }\n  }\n`;\n"], "names": ["AppFragmentNode", "gql"], "mappings": "AAAA;;;;;AAAgB,IAAA,WAAa,kCAAb,aAAa,EAAA;;;;;;AAEtB,MAAMA,eAAe,GAAGC,WAAG,QAAA,CAAC;;;;;;;;AAQnC,CAAC,AAAC;QARWD,eAAe,GAAfA,eAAe"}