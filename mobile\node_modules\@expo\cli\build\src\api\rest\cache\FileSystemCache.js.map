{"version": 3, "sources": ["../../../../../src/api/rest/cache/FileSystemCache.ts"], "sourcesContent": ["import cacache from 'cacache';\nimport { Readable } from 'stream';\n\nfunction getBodyAndMetaKeys(key: string): [string, string] {\n  return [`${key}body`, `${key}meta`];\n}\n\nexport class FileSystemCache {\n  constructor(public options: { ttl?: number; cacheDirectory: string }) {}\n\n  async get(key: string) {\n    const [, metaKey] = getBodyAndMetaKeys(key);\n\n    const metaInfo = await cacache.get.info(this.options.cacheDirectory, metaKey);\n\n    if (!metaInfo) {\n      return undefined;\n    }\n\n    const metaBuffer = await cacache.get.byDigest(this.options.cacheDirectory, metaInfo.integrity);\n    const metaData = JSON.parse(metaBuffer);\n    const { bodyStreamIntegrity, empty, expiration } = metaData;\n\n    delete metaData.bodyStreamIntegrity;\n    delete metaData.empty;\n    delete metaData.expiration;\n\n    if (expiration && expiration < Date.now()) {\n      return undefined;\n    }\n\n    const bodyStream = empty\n      ? Readable.from(Buffer.alloc(0))\n      : cacache.get.stream.byDigest(this.options.cacheDirectory, bodyStreamIntegrity);\n\n    return {\n      bodyStream,\n      metaData,\n    };\n  }\n\n  remove(key: string) {\n    const [bodyKey, metaKey] = getBodyAndMetaKeys(key);\n\n    return Promise.all([\n      cacache.rm.entry(this.options.cacheDirectory, bodyKey),\n      cacache.rm.entry(this.options.cacheDirectory, metaKey),\n    ]);\n  }\n\n  async set(key: string, bodyStream: NodeJS.ReadStream, metaData: any) {\n    const [bodyKey, metaKey] = getBodyAndMetaKeys(key);\n    const metaCopy = { ...metaData };\n\n    if (typeof this.options.ttl === 'number') {\n      metaCopy.expiration = Date.now() + this.options.ttl;\n    }\n\n    try {\n      metaCopy.bodyStreamIntegrity = await new Promise((fulfill, reject) => {\n        bodyStream\n          .pipe(cacache.put.stream(this.options.cacheDirectory, bodyKey))\n          .on('integrity', (i) => fulfill(i))\n          .on('error', (e) => {\n            reject(e);\n          });\n      });\n    } catch (err: any) {\n      if (err.code !== 'ENODATA') {\n        throw err;\n      }\n\n      metaCopy.empty = true;\n    }\n\n    const metaBuffer = Buffer.from(JSON.stringify(metaCopy));\n    await cacache.put(this.options.cacheDirectory, metaKey, metaBuffer);\n    const cachedData = await this.get(key);\n\n    return cachedData;\n  }\n}\n"], "names": ["getBodyAndMetaKeys", "key", "FileSystemCache", "constructor", "options", "get", "metaKey", "metaInfo", "cacache", "info", "cacheDirectory", "undefined", "metaBuffer", "byDigest", "integrity", "metaData", "JSON", "parse", "bodyStreamIntegrity", "empty", "expiration", "Date", "now", "bodyStream", "Readable", "from", "<PERSON><PERSON><PERSON>", "alloc", "stream", "remove", "<PERSON><PERSON><PERSON>", "Promise", "all", "rm", "entry", "set", "metaCopy", "ttl", "fulfill", "reject", "pipe", "put", "on", "i", "e", "err", "code", "stringify", "cachedData"], "mappings": "AAAA;;;;AAAoB,IAAA,QAAS,kCAAT,SAAS,EAAA;AACJ,IAAA,OAAQ,WAAR,QAAQ,CAAA;;;;;;AAEjC,SAASA,kBAAkB,CAACC,GAAW,EAAoB;IACzD,OAAO;QAAC,CAAC,EAAEA,GAAG,CAAC,IAAI,CAAC;QAAE,CAAC,EAAEA,GAAG,CAAC,IAAI,CAAC;KAAC,CAAC;CACrC;AAEM,MAAMC,eAAe;IAC1BC,YAAmBC,OAAiD,CAAE;aAAnDA,OAAiD,GAAjDA,OAAiD;KAAI;IAExE,MAAMC,GAAG,CAACJ,GAAW,EAAE;QACrB,MAAM,GAAGK,OAAO,CAAC,GAAGN,kBAAkB,CAACC,GAAG,CAAC,AAAC;QAE5C,MAAMM,QAAQ,GAAG,MAAMC,QAAO,QAAA,CAACH,GAAG,CAACI,IAAI,CAAC,IAAI,CAACL,OAAO,CAACM,cAAc,EAAEJ,OAAO,CAAC,AAAC;QAE9E,IAAI,CAACC,QAAQ,EAAE;YACb,OAAOI,SAAS,CAAC;SAClB;QAED,MAAMC,UAAU,GAAG,MAAMJ,QAAO,QAAA,CAACH,GAAG,CAACQ,QAAQ,CAAC,IAAI,CAACT,OAAO,CAACM,cAAc,EAAEH,QAAQ,CAACO,SAAS,CAAC,AAAC;QAC/F,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,AAAC;QACxC,MAAM,EAAEM,mBAAmB,CAAA,EAAEC,KAAK,CAAA,EAAEC,UAAU,CAAA,EAAE,GAAGL,QAAQ,AAAC;QAE5D,OAAOA,QAAQ,CAACG,mBAAmB,CAAC;QACpC,OAAOH,QAAQ,CAACI,KAAK,CAAC;QACtB,OAAOJ,QAAQ,CAACK,UAAU,CAAC;QAE3B,IAAIA,UAAU,IAAIA,UAAU,GAAGC,IAAI,CAACC,GAAG,EAAE,EAAE;YACzC,OAAOX,SAAS,CAAC;SAClB;QAED,MAAMY,UAAU,GAAGJ,KAAK,GACpBK,OAAQ,SAAA,CAACC,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAC9BnB,QAAO,QAAA,CAACH,GAAG,CAACuB,MAAM,CAACf,QAAQ,CAAC,IAAI,CAACT,OAAO,CAACM,cAAc,EAAEQ,mBAAmB,CAAC,AAAC;QAElF,OAAO;YACLK,UAAU;YACVR,QAAQ;SACT,CAAC;KACH;IAEDc,MAAM,CAAC5B,GAAW,EAAE;QAClB,MAAM,CAAC6B,OAAO,EAAExB,OAAO,CAAC,GAAGN,kBAAkB,CAACC,GAAG,CAAC,AAAC;QAEnD,OAAO8B,OAAO,CAACC,GAAG,CAAC;YACjBxB,QAAO,QAAA,CAACyB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC9B,OAAO,CAACM,cAAc,EAAEoB,OAAO,CAAC;YACtDtB,QAAO,QAAA,CAACyB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC9B,OAAO,CAACM,cAAc,EAAEJ,OAAO,CAAC;SACvD,CAAC,CAAC;KACJ;IAED,MAAM6B,GAAG,CAAClC,GAAW,EAAEsB,UAA6B,EAAER,QAAa,EAAE;QACnE,MAAM,CAACe,OAAO,EAAExB,OAAO,CAAC,GAAGN,kBAAkB,CAACC,GAAG,CAAC,AAAC;QACnD,MAAMmC,QAAQ,GAAG;YAAE,GAAGrB,QAAQ;SAAE,AAAC;QAEjC,IAAI,OAAO,IAAI,CAACX,OAAO,CAACiC,GAAG,KAAK,QAAQ,EAAE;YACxCD,QAAQ,CAAChB,UAAU,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAClB,OAAO,CAACiC,GAAG,CAAC;SACrD;QAED,IAAI;YACFD,QAAQ,CAAClB,mBAAmB,GAAG,MAAM,IAAIa,OAAO,CAAC,CAACO,OAAO,EAAEC,MAAM,GAAK;gBACpEhB,UAAU,CACPiB,IAAI,CAAChC,QAAO,QAAA,CAACiC,GAAG,CAACb,MAAM,CAAC,IAAI,CAACxB,OAAO,CAACM,cAAc,EAAEoB,OAAO,CAAC,CAAC,CAC9DY,EAAE,CAAC,WAAW,EAAE,CAACC,CAAC,GAAKL,OAAO,CAACK,CAAC,CAAC;gBAAA,CAAC,CAClCD,EAAE,CAAC,OAAO,EAAE,CAACE,CAAC,GAAK;oBAClBL,MAAM,CAACK,CAAC,CAAC,CAAC;iBACX,CAAC,CAAC;aACN,CAAC,CAAC;SACJ,CAAC,OAAOC,GAAG,EAAO;YACjB,IAAIA,GAAG,CAACC,IAAI,KAAK,SAAS,EAAE;gBAC1B,MAAMD,GAAG,CAAC;aACX;YAEDT,QAAQ,CAACjB,KAAK,GAAG,IAAI,CAAC;SACvB;QAED,MAAMP,UAAU,GAAGc,MAAM,CAACD,IAAI,CAACT,IAAI,CAAC+B,SAAS,CAACX,QAAQ,CAAC,CAAC,AAAC;QACzD,MAAM5B,QAAO,QAAA,CAACiC,GAAG,CAAC,IAAI,CAACrC,OAAO,CAACM,cAAc,EAAEJ,OAAO,EAAEM,UAAU,CAAC,CAAC;QACpE,MAAMoC,UAAU,GAAG,MAAM,IAAI,CAAC3C,GAAG,CAACJ,GAAG,CAAC,AAAC;QAEvC,OAAO+C,UAAU,CAAC;KACnB;CACF;QA1EY9C,eAAe,GAAfA,eAAe"}