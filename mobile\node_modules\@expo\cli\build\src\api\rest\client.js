"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.wrapFetchWithCredentials = wrapFetchWithCredentials;
exports.createCachedFetch = createCachedFetch;
exports.fetchAsync = void 0;
var _getUserState = require("@expo/config/build/getUserState");
var _nodeFetch = _interopRequireDefault(require("node-fetch"));
var _path = _interopRequireDefault(require("path"));
var _env = require("../../utils/env");
var _errors = require("../../utils/errors");
var _endpoint = require("../endpoint");
var _settings = require("../settings");
var _userSettings = _interopRequireDefault(require("../user/UserSettings"));
var _fileSystemCache = require("./cache/FileSystemCache");
var _wrapFetchWithCache = require("./cache/wrapFetchWithCache");
var _wrapFetchWithBaseUrl = require("./wrapFetchWithBaseUrl");
var _wrapFetchWithOffline = require("./wrapFetchWithOffline");
var _wrapFetchWithProgress = require("./wrapFetchWithProgress");
var _wrapFetchWithProxy = require("./wrapFetchWithProxy");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
class ApiV2Error extends Error {
    name = "ApiV2Error";
    constructor(response){
        super(response.message);
        this.code = response.code;
        this.expoApiV2ErrorCode = response.code;
        this.expoApiV2ErrorDetails = response.details;
        this.expoApiV2ErrorServerStack = response.stack;
        this.expoApiV2ErrorMetadata = response.metadata;
    }
}
exports.ApiV2Error = ApiV2Error;
class UnexpectedServerError extends Error {
    name = "UnexpectedServerError";
}
exports.UnexpectedServerError = UnexpectedServerError;
function wrapFetchWithCredentials(fetchFunction) {
    return async function fetchWithCredentials(url, options = {}) {
        if (Array.isArray(options.headers)) {
            throw new Error("request headers must be in object form");
        }
        var _headers;
        const resolvedHeaders = (_headers = options.headers) != null ? _headers : {};
        const token = _userSettings.default.getAccessToken();
        if (token) {
            resolvedHeaders.authorization = `Bearer ${token}`;
        } else {
            var ref;
            const sessionSecret = (ref = _userSettings.default.getSession()) == null ? void 0 : ref.sessionSecret;
            if (sessionSecret) {
                resolvedHeaders["expo-session"] = sessionSecret;
            }
        }
        try {
            const results = await fetchFunction(url, {
                ...options,
                headers: resolvedHeaders
            });
            if (results.status >= 400 && results.status < 500) {
                const body = await results.text();
                try {
                    var ref1;
                    const data = JSON.parse(body);
                    if (data == null ? void 0 : (ref1 = data.errors) == null ? void 0 : ref1.length) {
                        throw new ApiV2Error(data.errors[0]);
                    }
                } catch (error) {
                    // Server returned non-json response.
                    if (error.message.includes("in JSON at position")) {
                        throw new UnexpectedServerError(body);
                    }
                    throw error;
                }
            }
            return results;
        } catch (error) {
            // Specifically, when running `npx expo start` and the wifi is connected but not really (public wifi, airplanes, etc).
            if ("code" in error && error.code === "ENOTFOUND") {
                (0, _settings).disableNetwork();
                throw new _errors.CommandError("OFFLINE", "Network connection is unreliable. Try again with the environment variable `EXPO_OFFLINE=1` to skip network requests.");
            }
            throw error;
        }
    };
}
const fetchWithOffline = (0, _wrapFetchWithOffline).wrapFetchWithOffline(_nodeFetch.default);
const fetchWithBaseUrl = (0, _wrapFetchWithBaseUrl).wrapFetchWithBaseUrl(fetchWithOffline, (0, _endpoint).getExpoApiBaseUrl() + "/v2/");
const fetchWithProxy = (0, _wrapFetchWithProxy).wrapFetchWithProxy(fetchWithBaseUrl);
const fetchWithCredentials = (0, _wrapFetchWithProgress).wrapFetchWithProgress(wrapFetchWithCredentials(fetchWithProxy));
function createCachedFetch({ fetch =fetchWithCredentials , cacheDirectory , ttl , skipCache  }) {
    // Disable all caching in EXPO_BETA.
    if (skipCache || _env.env.EXPO_BETA || _env.env.EXPO_NO_CACHE) {
        return fetch;
    }
    return (0, _wrapFetchWithCache).wrapFetchWithCache(fetch, new _fileSystemCache.FileSystemCache({
        cacheDirectory: _path.default.join((0, _getUserState).getExpoHomeDirectory(), cacheDirectory),
        ttl
    }));
}
const fetchAsync = (0, _wrapFetchWithProgress).wrapFetchWithProgress(wrapFetchWithCredentials(fetchWithProxy));
exports.fetchAsync = fetchAsync;

//# sourceMappingURL=client.js.map