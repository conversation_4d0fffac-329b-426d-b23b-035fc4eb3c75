{"version": 3, "sources": ["../../../../src/api/rest/client.ts"], "sourcesContent": ["import { getExpoHomeDirectory } from '@expo/config/build/getUserState';\nimport { JSONValue } from '@expo/json-file';\nimport fetchInstance from 'node-fetch';\nimport path from 'path';\n\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { getExpoApiBaseUrl } from '../endpoint';\nimport { disableNetwork } from '../settings';\nimport UserSettings from '../user/UserSettings';\nimport { FileSystemCache } from './cache/FileSystemCache';\nimport { wrapFetchWithCache } from './cache/wrapFetchWithCache';\nimport { FetchLike } from './client.types';\nimport { wrapFetchWithBaseUrl } from './wrapFetchWithBaseUrl';\nimport { wrapFetchWithOffline } from './wrapFetchWithOffline';\nimport { wrapFetchWithProgress } from './wrapFetchWithProgress';\nimport { wrapFetchWithProxy } from './wrapFetchWithProxy';\n\nexport class ApiV2Error extends Error {\n  readonly name = 'ApiV2Error';\n  readonly code: string;\n  readonly expoApiV2ErrorCode: string;\n  readonly expoApiV2ErrorDetails?: JSONValue;\n  readonly expoApiV2ErrorServerStack?: string;\n  readonly expoApiV2ErrorMetadata?: object;\n\n  constructor(response: {\n    message: string;\n    code: string;\n    stack?: string;\n    details?: JSONValue;\n    metadata?: object;\n  }) {\n    super(response.message);\n    this.code = response.code;\n    this.expoApiV2ErrorCode = response.code;\n    this.expoApiV2ErrorDetails = response.details;\n    this.expoApiV2ErrorServerStack = response.stack;\n    this.expoApiV2ErrorMetadata = response.metadata;\n  }\n}\n\n/**\n * An Expo server error that didn't return the expected error JSON information.\n * The only 'expected' place for this is in testing, all other cases are bugs with the server.\n */\nexport class UnexpectedServerError extends Error {\n  readonly name = 'UnexpectedServerError';\n}\n\n/**\n * @returns a `fetch` function that will inject user authentication information and handle errors from the Expo API.\n */\nexport function wrapFetchWithCredentials(fetchFunction: FetchLike): FetchLike {\n  return async function fetchWithCredentials(url, options = {}) {\n    if (Array.isArray(options.headers)) {\n      throw new Error('request headers must be in object form');\n    }\n\n    const resolvedHeaders = options.headers ?? ({} as any);\n\n    const token = UserSettings.getAccessToken();\n    if (token) {\n      resolvedHeaders.authorization = `Bearer ${token}`;\n    } else {\n      const sessionSecret = UserSettings.getSession()?.sessionSecret;\n      if (sessionSecret) {\n        resolvedHeaders['expo-session'] = sessionSecret;\n      }\n    }\n\n    try {\n      const results = await fetchFunction(url, {\n        ...options,\n        headers: resolvedHeaders,\n      });\n\n      if (results.status >= 400 && results.status < 500) {\n        const body = await results.text();\n        try {\n          const data = JSON.parse(body);\n          if (data?.errors?.length) {\n            throw new ApiV2Error(data.errors[0]);\n          }\n        } catch (error: any) {\n          // Server returned non-json response.\n          if (error.message.includes('in JSON at position')) {\n            throw new UnexpectedServerError(body);\n          }\n          throw error;\n        }\n      }\n      return results;\n    } catch (error: any) {\n      // Specifically, when running `npx expo start` and the wifi is connected but not really (public wifi, airplanes, etc).\n      if ('code' in error && error.code === 'ENOTFOUND') {\n        disableNetwork();\n\n        throw new CommandError(\n          'OFFLINE',\n          'Network connection is unreliable. Try again with the environment variable `EXPO_OFFLINE=1` to skip network requests.'\n        );\n      }\n\n      throw error;\n    }\n  };\n}\n\nconst fetchWithOffline = wrapFetchWithOffline(fetchInstance);\n\nconst fetchWithBaseUrl = wrapFetchWithBaseUrl(fetchWithOffline, getExpoApiBaseUrl() + '/v2/');\n\nconst fetchWithProxy = wrapFetchWithProxy(fetchWithBaseUrl);\n\nconst fetchWithCredentials = wrapFetchWithProgress(wrapFetchWithCredentials(fetchWithProxy));\n\n/**\n * Create an instance of the fully qualified fetch command (auto authentication and api) but with caching in the '~/.expo' directory.\n * Caching is disabled automatically if the EXPO_NO_CACHE or EXPO_BETA environment variables are enabled.\n */\nexport function createCachedFetch({\n  fetch = fetchWithCredentials,\n  cacheDirectory,\n  ttl,\n  skipCache,\n}: {\n  fetch?: FetchLike;\n  cacheDirectory: string;\n  ttl?: number;\n  skipCache?: boolean;\n}): FetchLike {\n  // Disable all caching in EXPO_BETA.\n  if (skipCache || env.EXPO_BETA || env.EXPO_NO_CACHE) {\n    return fetch;\n  }\n\n  return wrapFetchWithCache(\n    fetch,\n    new FileSystemCache({\n      cacheDirectory: path.join(getExpoHomeDirectory(), cacheDirectory),\n      ttl,\n    })\n  );\n}\n\n/** Instance of fetch with automatic base URL pointing to the Expo API, user credential injection, and API error handling. Caching not included.  */\nexport const fetchAsync = wrapFetchWithProgress(wrapFetchWithCredentials(fetchWithProxy));\n"], "names": ["wrapFetchWithCredentials", "createCachedFetch", "ApiV2Error", "Error", "name", "constructor", "response", "message", "code", "expoApiV2ErrorCode", "expoApiV2ErrorDetails", "details", "expoApiV2ErrorServerStack", "stack", "expoApiV2ErrorMetadata", "metadata", "UnexpectedServerError", "fetchFunction", "fetchWithCredentials", "url", "options", "Array", "isArray", "headers", "resolvedHeaders", "token", "UserSettings", "getAccessToken", "authorization", "sessionSecret", "getSession", "results", "status", "body", "text", "data", "JSON", "parse", "errors", "length", "error", "includes", "disableNetwork", "CommandError", "fetchWithOffline", "wrapFetchWithOffline", "fetchInstance", "fetchWithBaseUrl", "wrapFetchWithBaseUrl", "getExpoApiBaseUrl", "fetchWithProxy", "wrapFetchWithProxy", "wrapFetchWithProgress", "fetch", "cacheDirectory", "ttl", "<PERSON><PERSON><PERSON>", "env", "EXPO_BETA", "EXPO_NO_CACHE", "wrapFetchWithCache", "FileSystemCache", "path", "join", "getExpoHomeDirectory", "fetchAsync"], "mappings": "AAAA;;;;QAqDgBA,wBAAwB,GAAxBA,wBAAwB;QAoExBC,iBAAiB,GAAjBA,iBAAiB;;AAzHI,IAAA,aAAiC,WAAjC,iCAAiC,CAAA;AAE5C,IAAA,UAAY,kCAAZ,YAAY,EAAA;AACrB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEH,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AACR,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AACf,IAAA,SAAa,WAAb,aAAa,CAAA;AAChB,IAAA,SAAa,WAAb,aAAa,CAAA;AACnB,IAAA,aAAsB,kCAAtB,sBAAsB,EAAA;AACf,IAAA,gBAAyB,WAAzB,yBAAyB,CAAA;AACtB,IAAA,mBAA4B,WAA5B,4BAA4B,CAAA;AAE1B,IAAA,qBAAwB,WAAxB,wBAAwB,CAAA;AACxB,IAAA,qBAAwB,WAAxB,wBAAwB,CAAA;AACvB,IAAA,sBAAyB,WAAzB,yBAAyB,CAAA;AAC5B,IAAA,mBAAsB,WAAtB,sBAAsB,CAAA;;;;;;AAElD,MAAMC,UAAU,SAASC,KAAK;IACnC,AAASC,IAAI,GAAG,YAAY,CAAC;IAO7BC,YAAYC,QAMX,CAAE;QACD,KAAK,CAACA,QAAQ,CAACC,OAAO,CAAC,CAAC;QACxB,IAAI,CAACC,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAAC;QAC1B,IAAI,CAACC,kBAAkB,GAAGH,QAAQ,CAACE,IAAI,CAAC;QACxC,IAAI,CAACE,qBAAqB,GAAGJ,QAAQ,CAACK,OAAO,CAAC;QAC9C,IAAI,CAACC,yBAAyB,GAAGN,QAAQ,CAACO,KAAK,CAAC;QAChD,IAAI,CAACC,sBAAsB,GAAGR,QAAQ,CAACS,QAAQ,CAAC;KACjD;CACF;QAtBYb,UAAU,GAAVA,UAAU;AA4BhB,MAAMc,qBAAqB,SAASb,KAAK;IAC9C,AAASC,IAAI,GAAG,uBAAuB,CAAC;CACzC;QAFYY,qBAAqB,GAArBA,qBAAqB;AAO3B,SAAShB,wBAAwB,CAACiB,aAAwB,EAAa;IAC5E,OAAO,eAAeC,oBAAoB,CAACC,GAAG,EAAEC,OAAO,GAAG,EAAE,EAAE;QAC5D,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAACG,OAAO,CAAC,EAAE;YAClC,MAAM,IAAIpB,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;YAEuBiB,QAAe;QAAvC,MAAMI,eAAe,GAAGJ,CAAAA,QAAe,GAAfA,OAAO,CAACG,OAAO,YAAfH,QAAe,GAAK,EAAE,AAAQ,AAAC;QAEvD,MAAMK,KAAK,GAAGC,aAAY,QAAA,CAACC,cAAc,EAAE,AAAC;QAC5C,IAAIF,KAAK,EAAE;YACTD,eAAe,CAACI,aAAa,GAAG,CAAC,OAAO,EAAEH,KAAK,CAAC,CAAC,CAAC;SACnD,MAAM;gBACiBC,GAAyB;YAA/C,MAAMG,aAAa,GAAGH,CAAAA,GAAyB,GAAzBA,aAAY,QAAA,CAACI,UAAU,EAAE,SAAe,GAAxCJ,KAAAA,CAAwC,GAAxCA,GAAyB,CAAEG,aAAa,AAAC;YAC/D,IAAIA,aAAa,EAAE;gBACjBL,eAAe,CAAC,cAAc,CAAC,GAAGK,aAAa,CAAC;aACjD;SACF;QAED,IAAI;YACF,MAAME,OAAO,GAAG,MAAMd,aAAa,CAACE,GAAG,EAAE;gBACvC,GAAGC,OAAO;gBACVG,OAAO,EAAEC,eAAe;aACzB,CAAC,AAAC;YAEH,IAAIO,OAAO,CAACC,MAAM,IAAI,GAAG,IAAID,OAAO,CAACC,MAAM,GAAG,GAAG,EAAE;gBACjD,MAAMC,IAAI,GAAG,MAAMF,OAAO,CAACG,IAAI,EAAE,AAAC;gBAClC,IAAI;wBAEEC,IAAY;oBADhB,MAAMA,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,AAAC;oBAC9B,IAAIE,IAAI,QAAQ,GAAZA,KAAAA,CAAY,GAAZA,CAAAA,IAAY,GAAZA,IAAI,CAAEG,MAAM,SAAA,GAAZH,KAAAA,CAAY,GAAZA,IAAY,CAAEI,MAAM,AAAR,EAAU;wBACxB,MAAM,IAAIrC,UAAU,CAACiC,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;qBACtC;iBACF,CAAC,OAAOE,KAAK,EAAO;oBACnB,qCAAqC;oBACrC,IAAIA,KAAK,CAACjC,OAAO,CAACkC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;wBACjD,MAAM,IAAIzB,qBAAqB,CAACiB,IAAI,CAAC,CAAC;qBACvC;oBACD,MAAMO,KAAK,CAAC;iBACb;aACF;YACD,OAAOT,OAAO,CAAC;SAChB,CAAC,OAAOS,KAAK,EAAO;YACnB,sHAAsH;YACtH,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAAChC,IAAI,KAAK,WAAW,EAAE;gBACjDkC,CAAAA,GAAAA,SAAc,AAAE,CAAA,eAAF,EAAE,CAAC;gBAEjB,MAAM,IAAIC,OAAY,aAAA,CACpB,SAAS,EACT,sHAAsH,CACvH,CAAC;aACH;YAED,MAAMH,KAAK,CAAC;SACb;KACF,CAAC;CACH;AAED,MAAMI,gBAAgB,GAAGC,CAAAA,GAAAA,qBAAoB,AAAe,CAAA,qBAAf,CAACC,UAAa,QAAA,CAAC,AAAC;AAE7D,MAAMC,gBAAgB,GAAGC,CAAAA,GAAAA,qBAAoB,AAAgD,CAAA,qBAAhD,CAACJ,gBAAgB,EAAEK,CAAAA,GAAAA,SAAiB,AAAE,CAAA,kBAAF,EAAE,GAAG,MAAM,CAAC,AAAC;AAE9F,MAAMC,cAAc,GAAGC,CAAAA,GAAAA,mBAAkB,AAAkB,CAAA,mBAAlB,CAACJ,gBAAgB,CAAC,AAAC;AAE5D,MAAM7B,oBAAoB,GAAGkC,CAAAA,GAAAA,sBAAqB,AAA0C,CAAA,sBAA1C,CAACpD,wBAAwB,CAACkD,cAAc,CAAC,CAAC,AAAC;AAMtF,SAASjD,iBAAiB,CAAC,EAChCoD,KAAK,EAAGnC,oBAAoB,CAAA,EAC5BoC,cAAc,CAAA,EACdC,GAAG,CAAA,EACHC,SAAS,CAAA,EAMV,EAAa;IACZ,oCAAoC;IACpC,IAAIA,SAAS,IAAIC,IAAG,IAAA,CAACC,SAAS,IAAID,IAAG,IAAA,CAACE,aAAa,EAAE;QACnD,OAAON,KAAK,CAAC;KACd;IAED,OAAOO,CAAAA,GAAAA,mBAAkB,AAMxB,CAAA,mBANwB,CACvBP,KAAK,EACL,IAAIQ,gBAAe,gBAAA,CAAC;QAClBP,cAAc,EAAEQ,KAAI,QAAA,CAACC,IAAI,CAACC,CAAAA,GAAAA,aAAoB,AAAE,CAAA,qBAAF,EAAE,EAAEV,cAAc,CAAC;QACjEC,GAAG;KACJ,CAAC,CACH,CAAC;CACH;AAGM,MAAMU,UAAU,GAAGb,CAAAA,GAAAA,sBAAqB,AAA0C,CAAA,sBAA1C,CAACpD,wBAAwB,CAACkD,cAAc,CAAC,CAAC,AAAC;QAA7Ee,UAAU,GAAVA,UAAU"}