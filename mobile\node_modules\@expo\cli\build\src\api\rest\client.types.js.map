{"version": 3, "sources": ["../../../../src/api/rest/client.types.ts"], "sourcesContent": ["import { RequestInfo, RequestInit, Response } from 'node-fetch';\nimport { URLSearchParams } from 'url';\n\nexport type ProgressCallback = (props: {\n  /** Number ranging from 0 to 1 representing the download percentage. */\n  progress: number;\n  /** Total size of the download, in bytes. */\n  total: number;\n  /** Current amount of data downloaded, in bytes. */\n  loaded: number;\n}) => void;\n\n/**\n * Represents a `fetch`-like function. Used since `typeof fetch` has statics we don't\n * use and aren't interested in hoisting every time we wrap fetch with extra features.\n */\nexport type FetchLike = (\n  url: RequestInfo,\n  init?: RequestInit & {\n    searchParams?: URLSearchParams;\n    /** Progress callback, only implemented when `wrapFetchWithProgress` is used. */\n    onProgress?: ProgressCallback;\n  }\n) => Promise<Response>;\n"], "names": [], "mappings": "AAAA"}