{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithBaseUrl.ts"], "sourcesContent": ["import { URL } from 'url';\n\nimport { FetchLike } from './client.types';\n\nconst debug = require('debug')('expo:api:fetch:base') as typeof console.log;\n\n/**\n * Wrap a fetch function with support for a predefined base URL.\n * This implementation works like the browser fetch, applying the input to a prefix base URL.\n */\nexport function wrapFetchWithBaseUrl(fetch: FetchLike, baseUrl: string): FetchLike {\n  // NOTE(EvanBacon): DO NOT RETURN AN ASYNC WRAPPER. THIS BREAKS LOADING INDICATORS.\n  return (url, init) => {\n    if (typeof url !== 'string') {\n      throw new TypeError('Custom fetch function only accepts a string URL as the first parameter');\n    }\n    const parsed = new URL(url, baseUrl);\n    if (init?.searchParams) {\n      parsed.search = init.searchParams.toString();\n    }\n    debug('fetch:', parsed.toString().trim());\n    return fetch(parsed.toString(), init);\n  };\n}\n"], "names": ["wrapFetchWithBaseUrl", "debug", "require", "fetch", "baseUrl", "url", "init", "TypeError", "parsed", "URL", "searchParams", "search", "toString", "trim"], "mappings": "AAAA;;;;QAUgBA,oBAAoB,GAApBA,oBAAoB;AAVhB,IAAA,IAAK,WAAL,KAAK,CAAA;AAIzB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,AAAsB,AAAC;AAMrE,SAASF,oBAAoB,CAACG,KAAgB,EAAEC,OAAe,EAAa;IACjF,mFAAmF;IACnF,OAAO,CAACC,GAAG,EAAEC,IAAI,GAAK;QACpB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAIE,SAAS,CAAC,wEAAwE,CAAC,CAAC;SAC/F;QACD,MAAMC,MAAM,GAAG,IAAIC,IAAG,IAAA,CAACJ,GAAG,EAAED,OAAO,CAAC,AAAC;QACrC,IAAIE,IAAI,QAAc,GAAlBA,KAAAA,CAAkB,GAAlBA,IAAI,CAAEI,YAAY,EAAE;YACtBF,MAAM,CAACG,MAAM,GAAGL,IAAI,CAACI,YAAY,CAACE,QAAQ,EAAE,CAAC;SAC9C;QACDX,KAAK,CAAC,QAAQ,EAAEO,MAAM,CAACI,QAAQ,EAAE,CAACC,IAAI,EAAE,CAAC,CAAC;QAC1C,OAAOV,KAAK,CAACK,MAAM,CAACI,QAAQ,EAAE,EAAEN,IAAI,CAAC,CAAC;KACvC,CAAC;CACH"}