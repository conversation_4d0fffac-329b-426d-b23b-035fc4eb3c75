"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.wrapFetchWithOffline = wrapFetchWithOffline;
var _env = require("../../utils/env");
const debug = require("debug")("expo:api:fetch:offline");
function wrapFetchWithOffline(fetchFunction) {
    // NOTE(EvanBacon): DO NOT RETURN AN ASYNC WRAPPER. THIS BREAKS LOADING INDICATORS.
    return function fetchWithOffline(url, options = {}) {
        if (_env.env.EXPO_OFFLINE) {
            debug("Skipping network request: " + url);
            options.timeout = 1;
        }
        return fetchFunction(url, options);
    };
}

//# sourceMappingURL=wrapFetchWithOffline.js.map