{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithProgress.ts"], "sourcesContent": ["import * as Log from '../../log';\nimport { FetchLike } from './client.types';\nconst debug = require('debug')('expo:api:fetch:progress') as typeof console.log;\n\nexport function wrapFetchWithProgress(fetch: FetchLike): FetchLike {\n  return (url, init) => {\n    return fetch(url, init).then((res) => {\n      if (res.ok && init?.onProgress) {\n        const totalDownloadSize = res.headers.get('Content-Length');\n        const total = Number(totalDownloadSize);\n\n        debug(`Download size: ${totalDownloadSize}`);\n        if (!totalDownloadSize || isNaN(total) || total < 0) {\n          Log.warn(\n            'Progress callback not supported for network request because \"Content-Length\" header missing or invalid in response from URL:',\n            url.toString()\n          );\n          return res;\n        }\n\n        let length = 0;\n\n        debug(`Starting progress animation for ${url}`);\n        res.body.on('data', (chunk) => {\n          length += Buffer.byteLength(chunk);\n          onProgress();\n        });\n\n        res.body.on('end', () => {\n          debug(`Finished progress animation for ${url}`);\n          onProgress();\n        });\n\n        const onProgress = () => {\n          const progress = length / total || 0;\n          init.onProgress?.({\n            progress,\n            total,\n            loaded: length,\n          });\n        };\n      }\n      return res;\n    });\n  };\n}\n"], "names": ["wrapFetchWithProgress", "Log", "debug", "require", "fetch", "url", "init", "then", "res", "ok", "onProgress", "totalDownloadSize", "headers", "get", "total", "Number", "isNaN", "warn", "toString", "length", "body", "on", "chunk", "<PERSON><PERSON><PERSON>", "byteLength", "progress", "loaded"], "mappings": "AAAA;;;;QAIgBA,qBAAqB,GAArBA,qBAAqB;AAJzBC,IAAAA,GAAG,mCAAM,WAAW,EAAjB;;;;;;;;;;;;;;;;;;;;;;AAEf,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEzE,SAASH,qBAAqB,CAACI,KAAgB,EAAa;IACjE,OAAO,CAACC,GAAG,EAAEC,IAAI,GAAK;QACpB,OAAOF,KAAK,CAACC,GAAG,EAAEC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,GAAG,GAAK;YACpC,IAAIA,GAAG,CAACC,EAAE,IAAIH,CAAAA,IAAI,QAAY,GAAhBA,KAAAA,CAAgB,GAAhBA,IAAI,CAAEI,UAAU,CAAA,EAAE;gBAC9B,MAAMC,iBAAiB,GAAGH,GAAG,CAACI,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,AAAC;gBAC5D,MAAMC,KAAK,GAAGC,MAAM,CAACJ,iBAAiB,CAAC,AAAC;gBAExCT,KAAK,CAAC,CAAC,eAAe,EAAES,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBAC7C,IAAI,CAACA,iBAAiB,IAAIK,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;oBACnDb,GAAG,CAACgB,IAAI,CACN,8HAA8H,EAC9HZ,GAAG,CAACa,QAAQ,EAAE,CACf,CAAC;oBACF,OAAOV,GAAG,CAAC;iBACZ;gBAED,IAAIW,MAAM,GAAG,CAAC,AAAC;gBAEfjB,KAAK,CAAC,CAAC,gCAAgC,EAAEG,GAAG,CAAC,CAAC,CAAC,CAAC;gBAChDG,GAAG,CAACY,IAAI,CAACC,EAAE,CAAC,MAAM,EAAE,CAACC,KAAK,GAAK;oBAC7BH,MAAM,IAAII,MAAM,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC;oBACnCZ,UAAU,EAAE,CAAC;iBACd,CAAC,CAAC;gBAEHF,GAAG,CAACY,IAAI,CAACC,EAAE,CAAC,KAAK,EAAE,IAAM;oBACvBnB,KAAK,CAAC,CAAC,gCAAgC,EAAEG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAChDK,UAAU,EAAE,CAAC;iBACd,CAAC,CAAC;gBAEH,MAAMA,UAAU,GAAG,IAAM;oBACvB,MAAMe,QAAQ,GAAGN,MAAM,GAAGL,KAAK,IAAI,CAAC,AAAC;oBACrCR,IAAI,CAACI,UAAU,QAIb,GAJFJ,KAAAA,CAIE,GAJFA,IAAI,CAACI,UAAU,CAAG;wBAChBe,QAAQ;wBACRX,KAAK;wBACLY,MAAM,EAAEP,MAAM;qBACf,CAAC,AAvCZ,CAuCa;iBACJ,AAAC;aACH;YACD,OAAOX,GAAG,CAAC;SACZ,CAAC,CAAC;KACJ,CAAC;CACH"}