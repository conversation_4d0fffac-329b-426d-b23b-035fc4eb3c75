{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithProxy.ts"], "sourcesContent": ["import createHttpsProxyAgent from 'https-proxy-agent';\n\nimport { env } from '../../utils/env';\nimport { FetchLike } from './client.types';\n\nconst debug = require('debug')('expo:api:fetch:proxy') as typeof console.log;\n\n/** Wrap fetch with support for proxies. */\nexport function wrapFetchWithProxy(fetchFunction: FetchLike): FetchLike {\n  // NOTE(EvanBacon): DO NOT RETURN AN ASYNC WRAPPER. THIS BREAKS LOADING INDICATORS.\n  return function fetchWithProxy(url, options = {}) {\n    const proxy = env.HTTP_PROXY;\n    if (!options.agent && proxy) {\n      debug('Using proxy:', proxy);\n      options.agent = createHttpsProxyAgent(proxy);\n    }\n    return fetchFunction(url, options);\n  };\n}\n"], "names": ["wrapFetchWithProxy", "debug", "require", "fetchFunction", "fetchWithProxy", "url", "options", "proxy", "env", "HTTP_PROXY", "agent", "createHttpsProxyAgent"], "mappings": "AAAA;;;;QAQgBA,kBAAkB,GAAlBA,kBAAkB;AARA,IAAA,gBAAmB,kCAAnB,mBAAmB,EAAA;AAEjC,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;;;;;;AAGrC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,AAAsB,AAAC;AAGtE,SAASF,kBAAkB,CAACG,aAAwB,EAAa;IACtE,mFAAmF;IACnF,OAAO,SAASC,cAAc,CAACC,GAAG,EAAEC,OAAO,GAAG,EAAE,EAAE;QAChD,MAAMC,KAAK,GAAGC,IAAG,IAAA,CAACC,UAAU,AAAC;QAC7B,IAAI,CAACH,OAAO,CAACI,KAAK,IAAIH,KAAK,EAAE;YAC3BN,KAAK,CAAC,cAAc,EAAEM,KAAK,CAAC,CAAC;YAC7BD,OAAO,CAACI,KAAK,GAAGC,CAAAA,GAAAA,gBAAqB,AAAO,CAAA,QAAP,CAACJ,KAAK,CAAC,CAAC;SAC9C;QACD,OAAOJ,aAAa,CAACE,GAAG,EAAEC,OAAO,CAAC,CAAC;KACpC,CAAC;CACH"}