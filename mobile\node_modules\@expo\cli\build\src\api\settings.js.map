{"version": 3, "sources": ["../../../src/api/settings.ts"], "sourcesContent": ["// This file represents temporary globals for the CLI when using the API.\n// Settings should be as minimal as possible since they are globals.\nimport chalk from 'chalk';\n\nimport { Log } from '../log';\nimport { env } from '../utils/env';\n\nexport function disableNetwork() {\n  if (env.EXPO_OFFLINE) return;\n  process.env.EXPO_OFFLINE = '1';\n  Log.log(chalk.gray('Networking has been disabled'));\n}\n"], "names": ["disableNetwork", "env", "EXPO_OFFLINE", "process", "Log", "log", "chalk", "gray"], "mappings": "AAEA;;;;QAKgBA,cAAc,GAAdA,cAAc;AALZ,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEL,IAAA,IAAQ,WAAR,QAAQ,CAAA;AACR,IAAA,IAAc,WAAd,cAAc,CAAA;;;;;;AAE3B,SAASA,cAAc,GAAG;IAC/B,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE,OAAO;IAC7BC,OAAO,CAACF,GAAG,CAACC,YAAY,GAAG,GAAG,CAAC;IAC/BE,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,QAAA,CAACC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC;CACrD"}