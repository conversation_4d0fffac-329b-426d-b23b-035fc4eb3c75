{"version": 3, "sources": ["../../../src/api/signManifest.ts"], "sourcesContent": ["import { ExpoAppManifest } from '@expo/config';\nimport { JSONObject } from '@expo/json-file';\n\nimport { fetchAsync } from './rest/client';\nimport { ensureLoggedInAsync } from './user/actions';\nimport { getActorDisplayName, getUserAsync } from './user/user';\n\n/** Sign a classic manifest to access secure features in sandboxed environments like Expo Go. */\nexport async function signClassicExpoGoManifestAsync(\n  manifest: Partial<ExpoAppManifest>\n): Promise<string> {\n  await ensureLoggedInAsync();\n  const res = await fetchAsync('manifest/sign', {\n    method: 'POST',\n    body: JSON.stringify({\n      args: {\n        remoteUsername: manifest.owner ?? getActorDisplayName(await getUserAsync()),\n        remotePackageName: manifest.slug,\n      },\n      manifest: manifest as JSONObject,\n    }),\n  });\n  const { data } = await res.json();\n  return data.response;\n}\n"], "names": ["signClassicExpoGoManifestAsync", "manifest", "ensureLoggedInAsync", "res", "fetchAsync", "method", "body", "JSON", "stringify", "args", "remoteUsername", "owner", "getActorDisplayName", "getUserAsync", "remotePackageName", "slug", "data", "json", "response"], "mappings": "AAAA;;;;QAQsBA,8BAA8B,GAA9BA,8BAA8B;AALzB,IAAA,OAAe,WAAf,eAAe,CAAA;AACN,IAAA,QAAgB,WAAhB,gBAAgB,CAAA;AACF,IAAA,KAAa,WAAb,aAAa,CAAA;AAGxD,eAAeA,8BAA8B,CAClDC,QAAkC,EACjB;IACjB,MAAMC,CAAAA,GAAAA,QAAmB,AAAE,CAAA,oBAAF,EAAE,CAAC;QAKND,MAAc;IAJpC,MAAME,GAAG,GAAG,MAAMC,CAAAA,GAAAA,OAAU,AAS1B,CAAA,WAT0B,CAAC,eAAe,EAAE;QAC5CC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,IAAI,EAAE;gBACJC,cAAc,EAAET,CAAAA,MAAc,GAAdA,QAAQ,CAACU,KAAK,YAAdV,MAAc,GAAIW,CAAAA,GAAAA,KAAmB,AAAsB,CAAA,oBAAtB,CAAC,MAAMC,CAAAA,GAAAA,KAAY,AAAE,CAAA,aAAF,EAAE,CAAC;gBAC3EC,iBAAiB,EAAEb,QAAQ,CAACc,IAAI;aACjC;YACDd,QAAQ,EAAEA,QAAQ;SACnB,CAAC;KACH,CAAC,AAAC;IACH,MAAM,EAAEe,IAAI,CAAA,EAAE,GAAG,MAAMb,GAAG,CAACc,IAAI,EAAE,AAAC;IAClC,OAAOD,IAAI,CAACE,QAAQ,CAAC;CACtB"}