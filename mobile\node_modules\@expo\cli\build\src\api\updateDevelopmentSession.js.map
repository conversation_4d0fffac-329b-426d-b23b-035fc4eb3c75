{"version": 3, "sources": ["../../../src/api/updateDevelopmentSession.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport os from 'os';\nimport { URLSearchParams } from 'url';\n\nimport { CommandError } from '../utils/errors';\nimport { fetchAsync } from './rest/client';\n\n/** Create the expected session info. */\nexport function createSessionInfo({\n  exp,\n  runtime,\n  url,\n}: {\n  exp: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n  runtime: 'native' | 'web';\n  url: string;\n}) {\n  return {\n    session: {\n      description: `${exp.name} on ${os.hostname()}`,\n      hostname: os.hostname(),\n      platform: runtime,\n      config: {\n        // TODO: if icons are specified, upload a url for them too so people can distinguish\n        description: exp.description,\n        name: exp.name,\n        slug: exp.slug,\n        primaryColor: exp.primaryColor,\n      },\n      url,\n      source: 'desktop',\n    },\n  };\n}\n\n/** Send a request to Expo API to keep the 'development session' alive for the provided devices. */\nexport async function updateDevelopmentSessionAsync({\n  deviceIds,\n  exp,\n  runtime,\n  url,\n}: {\n  deviceIds: string[];\n  exp: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n  runtime: 'native' | 'web';\n  url: string;\n}) {\n  const searchParams = new URLSearchParams();\n  deviceIds.forEach((id) => {\n    searchParams.append('deviceId', id);\n  });\n\n  const results = await fetchAsync('development-sessions/notify-alive', {\n    searchParams,\n    method: 'POST',\n    body: JSON.stringify({\n      data: createSessionInfo({ exp, runtime, url }),\n    }),\n  });\n\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when updating the development session on Expo servers: ${results.statusText}.`\n    );\n  }\n}\n\n/** Send a request to Expo API to close the 'development session' for the provided devices. */\nexport async function closeDevelopmentSessionAsync({\n  deviceIds,\n  url,\n}: {\n  deviceIds: string[];\n  url: string;\n}) {\n  const searchParams = new URLSearchParams();\n  deviceIds.forEach((id) => {\n    searchParams.append('deviceId', id);\n  });\n\n  const results = await fetchAsync('development-sessions/notify-close', {\n    searchParams,\n    method: 'POST',\n    body: JSON.stringify({\n      session: { url },\n    }),\n  });\n\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when closing the development session on Expo servers: ${results.statusText}.`\n    );\n  }\n}\n"], "names": ["createSessionInfo", "updateDevelopmentSessionAsync", "closeDevelopmentSessionAsync", "exp", "runtime", "url", "session", "description", "name", "os", "hostname", "platform", "config", "slug", "primaryColor", "source", "deviceIds", "searchParams", "URLSearchParams", "for<PERSON>ach", "id", "append", "results", "fetchAsync", "method", "body", "JSON", "stringify", "data", "ok", "CommandError", "statusText"], "mappings": "AAAA;;;;QAQgBA,iBAAiB,GAAjBA,iBAAiB;QA4BXC,6BAA6B,GAA7BA,6BAA6B;QAiC7BC,4BAA4B,GAA5BA,4BAA4B;AApEnC,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACa,IAAA,IAAK,WAAL,KAAK,CAAA;AAER,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACnB,IAAA,OAAe,WAAf,eAAe,CAAA;;;;;;AAGnC,SAASF,iBAAiB,CAAC,EAChCG,GAAG,CAAA,EACHC,OAAO,CAAA,EACPC,GAAG,CAAA,EAKJ,EAAE;IACD,OAAO;QACLC,OAAO,EAAE;YACPC,WAAW,EAAE,CAAC,EAAEJ,GAAG,CAACK,IAAI,CAAC,IAAI,EAAEC,GAAE,QAAA,CAACC,QAAQ,EAAE,CAAC,CAAC;YAC9CA,QAAQ,EAAED,GAAE,QAAA,CAACC,QAAQ,EAAE;YACvBC,QAAQ,EAAEP,OAAO;YACjBQ,MAAM,EAAE;gBACN,oFAAoF;gBACpFL,WAAW,EAAEJ,GAAG,CAACI,WAAW;gBAC5BC,IAAI,EAAEL,GAAG,CAACK,IAAI;gBACdK,IAAI,EAAEV,GAAG,CAACU,IAAI;gBACdC,YAAY,EAAEX,GAAG,CAACW,YAAY;aAC/B;YACDT,GAAG;YACHU,MAAM,EAAE,SAAS;SAClB;KACF,CAAC;CACH;AAGM,eAAed,6BAA6B,CAAC,EAClDe,SAAS,CAAA,EACTb,GAAG,CAAA,EACHC,OAAO,CAAA,EACPC,GAAG,CAAA,EAMJ,EAAE;IACD,MAAMY,YAAY,GAAG,IAAIC,IAAe,gBAAA,EAAE,AAAC;IAC3CF,SAAS,CAACG,OAAO,CAAC,CAACC,EAAE,GAAK;QACxBH,YAAY,CAACI,MAAM,CAAC,UAAU,EAAED,EAAE,CAAC,CAAC;KACrC,CAAC,CAAC;IAEH,MAAME,OAAO,GAAG,MAAMC,CAAAA,GAAAA,OAAU,AAM9B,CAAA,WAN8B,CAAC,mCAAmC,EAAE;QACpEN,YAAY;QACZO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,IAAI,EAAE5B,iBAAiB,CAAC;gBAAEG,GAAG;gBAAEC,OAAO;gBAAEC,GAAG;aAAE,CAAC;SAC/C,CAAC;KACH,CAAC,AAAC;IAEH,IAAI,CAACiB,OAAO,CAACO,EAAE,EAAE;QACf,MAAM,IAAIC,OAAY,aAAA,CACpB,KAAK,EACL,CAAC,2EAA2E,EAAER,OAAO,CAACS,UAAU,CAAC,CAAC,CAAC,CACpG,CAAC;KACH;CACF;AAGM,eAAe7B,4BAA4B,CAAC,EACjDc,SAAS,CAAA,EACTX,GAAG,CAAA,EAIJ,EAAE;IACD,MAAMY,YAAY,GAAG,IAAIC,IAAe,gBAAA,EAAE,AAAC;IAC3CF,SAAS,CAACG,OAAO,CAAC,CAACC,EAAE,GAAK;QACxBH,YAAY,CAACI,MAAM,CAAC,UAAU,EAAED,EAAE,CAAC,CAAC;KACrC,CAAC,CAAC;IAEH,MAAME,OAAO,GAAG,MAAMC,CAAAA,GAAAA,OAAU,AAM9B,CAAA,WAN8B,CAAC,mCAAmC,EAAE;QACpEN,YAAY;QACZO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBrB,OAAO,EAAE;gBAAED,GAAG;aAAE;SACjB,CAAC;KACH,CAAC,AAAC;IAEH,IAAI,CAACiB,OAAO,CAACO,EAAE,EAAE;QACf,MAAM,IAAIC,OAAY,aAAA,CACpB,KAAK,EACL,CAAC,0EAA0E,EAAER,OAAO,CAACS,UAAU,CAAC,CAAC,CAAC,CACnG,CAAC;KACH;CACF"}