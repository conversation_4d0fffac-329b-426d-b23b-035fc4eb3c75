{"version": 3, "sources": ["../../../../src/api/user/actions.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport * as Log from '../../log';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { learnMore } from '../../utils/link';\nimport promptAsync, { Question } from '../../utils/prompts';\nimport { ApiV2Error } from '../rest/client';\nimport { retryUsernamePasswordAuthWithOTPAsync } from './otp';\nimport { Actor, getUserAsync, loginAsync } from './user';\n\n/** Show login prompt while prompting for missing credentials. */\nexport async function showLoginPromptAsync({\n  printNewLine = false,\n  otp,\n  ...options\n}: {\n  printNewLine?: boolean;\n  username?: string;\n  password?: string;\n  otp?: string;\n} = {}): Promise<void> {\n  if (env.EXPO_OFFLINE) {\n    throw new CommandError('OFFLINE', 'Cannot authenticate in offline-mode');\n  }\n  const hasCredentials = options.username && options.password;\n\n  if (printNewLine) {\n    Log.log();\n  }\n\n  Log.log(hasCredentials ? 'Logging in to EAS' : 'Log in to EAS');\n\n  let username = options.username;\n  let password = options.password;\n\n  if (!hasCredentials) {\n    const resolved = await promptAsync(\n      [\n        !options.username && {\n          type: 'text',\n          name: 'username',\n          message: 'Email or username',\n        },\n        !options.password && {\n          type: 'password',\n          name: 'password',\n          message: 'Password',\n        },\n      ].filter(Boolean) as Question<string>[],\n      {\n        nonInteractiveHelp: `Use the EXPO_TOKEN environment variable to authenticate in CI (${learnMore(\n          'https://docs.expo.dev/accounts/programmatic-access/'\n        )})`,\n      }\n    );\n    username ??= resolved.username;\n    password ??= resolved.password;\n  }\n  // This is just for the types.\n  assert(username && password);\n\n  try {\n    await loginAsync({\n      username,\n      password,\n      otp,\n    });\n  } catch (e) {\n    if (e instanceof ApiV2Error && e.expoApiV2ErrorCode === 'ONE_TIME_PASSWORD_REQUIRED') {\n      await retryUsernamePasswordAuthWithOTPAsync(\n        username,\n        password,\n        e.expoApiV2ErrorMetadata as any\n      );\n    } else {\n      throw e;\n    }\n  }\n}\n\n/** Ensure the user is logged in, if not, prompt to login. */\nexport async function ensureLoggedInAsync(): Promise<Actor> {\n  let user = await getUserAsync().catch(() => null);\n\n  if (!user) {\n    Log.warn(chalk.yellow`An Expo user account is required to proceed.`);\n    await showLoginPromptAsync({ printNewLine: true });\n    user = await getUserAsync();\n  }\n\n  assert(user, 'User should be logged in');\n  return user;\n}\n"], "names": ["showLoginPromptAsync", "ensureLoggedInAsync", "Log", "printNewLine", "otp", "options", "env", "EXPO_OFFLINE", "CommandError", "hasCredentials", "username", "password", "log", "resolved", "promptAsync", "type", "name", "message", "filter", "Boolean", "nonInteractiveHelp", "learnMore", "assert", "loginAsync", "e", "ApiV2Error", "expoApiV2ErrorCode", "retryUsernamePasswordAuthWithOTPAsync", "expoApiV2ErrorMetadata", "user", "getUserAsync", "catch", "warn", "chalk", "yellow"], "mappings": "AAAA;;;;QAasBA,oBAAoB,GAApBA,oBAAoB;QAsEpBC,mBAAmB,GAAnBA,mBAAmB;AAnFtB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACT,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEbC,IAAAA,GAAG,mCAAM,WAAW,EAAjB;AACK,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AACR,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AACvB,IAAA,KAAkB,WAAlB,kBAAkB,CAAA;AACN,IAAA,QAAqB,kCAArB,qBAAqB,EAAA;AAChC,IAAA,OAAgB,WAAhB,gBAAgB,CAAA;AACW,IAAA,IAAO,WAAP,OAAO,CAAA;AACb,IAAA,KAAQ,WAAR,QAAQ,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGjD,eAAeF,oBAAoB,CAAC,EACzCG,YAAY,EAAG,KAAK,CAAA,EACpBC,GAAG,CAAA,EACH,GAAGC,OAAO,EAMX,GAAG,EAAE,EAAiB;IACrB,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE;QACpB,MAAM,IAAIC,OAAY,aAAA,CAAC,SAAS,EAAE,qCAAqC,CAAC,CAAC;KAC1E;IACD,MAAMC,cAAc,GAAGJ,OAAO,CAACK,QAAQ,IAAIL,OAAO,CAACM,QAAQ,AAAC;IAE5D,IAAIR,YAAY,EAAE;QAChBD,GAAG,CAACU,GAAG,EAAE,CAAC;KACX;IAEDV,GAAG,CAACU,GAAG,CAACH,cAAc,GAAG,mBAAmB,GAAG,eAAe,CAAC,CAAC;IAEhE,IAAIC,QAAQ,GAAGL,OAAO,CAACK,QAAQ,AAAC;IAChC,IAAIC,QAAQ,GAAGN,OAAO,CAACM,QAAQ,AAAC;IAEhC,IAAI,CAACF,cAAc,EAAE;QACnB,MAAMI,QAAQ,GAAG,MAAMC,CAAAA,GAAAA,QAAW,AAkBjC,CAAA,QAlBiC,CAChC;YACE,CAACT,OAAO,CAACK,QAAQ,IAAI;gBACnBK,IAAI,EAAE,MAAM;gBACZC,IAAI,EAAE,UAAU;gBAChBC,OAAO,EAAE,mBAAmB;aAC7B;YACD,CAACZ,OAAO,CAACM,QAAQ,IAAI;gBACnBI,IAAI,EAAE,UAAU;gBAChBC,IAAI,EAAE,UAAU;gBAChBC,OAAO,EAAE,UAAU;aACpB;SACF,CAACC,MAAM,CAACC,OAAO,CAAC,EACjB;YACEC,kBAAkB,EAAE,CAAC,+DAA+D,EAAEC,CAAAA,GAAAA,KAAS,AAE9F,CAAA,UAF8F,CAC7F,qDAAqD,CACtD,CAAC,CAAC,CAAC;SACL,CACF,AAAC;QACFX,QAAQ,WAARA,QAAQ,GAARA,QAAQ,GAAKG,QAAQ,CAACH,QAAQ,CAAC;QAC/BC,QAAQ,WAARA,QAAQ,GAARA,QAAQ,GAAKE,QAAQ,CAACF,QAAQ,CAAC;KAChC;IACD,8BAA8B;IAC9BW,CAAAA,GAAAA,OAAM,AAAsB,CAAA,QAAtB,CAACZ,QAAQ,IAAIC,QAAQ,CAAC,CAAC;IAE7B,IAAI;QACF,MAAMY,CAAAA,GAAAA,KAAU,AAId,CAAA,WAJc,CAAC;YACfb,QAAQ;YACRC,QAAQ;YACRP,GAAG;SACJ,CAAC,CAAC;KACJ,CAAC,OAAOoB,CAAC,EAAE;QACV,IAAIA,CAAC,YAAYC,OAAU,WAAA,IAAID,CAAC,CAACE,kBAAkB,KAAK,4BAA4B,EAAE;YACpF,MAAMC,CAAAA,GAAAA,IAAqC,AAI1C,CAAA,sCAJ0C,CACzCjB,QAAQ,EACRC,QAAQ,EACRa,CAAC,CAACI,sBAAsB,CACzB,CAAC;SACH,MAAM;YACL,MAAMJ,CAAC,CAAC;SACT;KACF;CACF;AAGM,eAAevB,mBAAmB,GAAmB;IAC1D,IAAI4B,IAAI,GAAG,MAAMC,CAAAA,GAAAA,KAAY,AAAE,CAAA,aAAF,EAAE,CAACC,KAAK,CAAC,IAAM,IAAI;IAAA,CAAC,AAAC;IAElD,IAAI,CAACF,IAAI,EAAE;QACT3B,GAAG,CAAC8B,IAAI,CAACC,MAAK,QAAA,CAACC,MAAM,CAAC,4CAA4C,CAAC,CAAC,CAAC;QACrE,MAAMlC,oBAAoB,CAAC;YAAEG,YAAY,EAAE,IAAI;SAAE,CAAC,CAAC;QACnD0B,IAAI,GAAG,MAAMC,CAAAA,GAAAA,KAAY,AAAE,CAAA,aAAF,EAAE,CAAC;KAC7B;IAEDR,CAAAA,GAAAA,OAAM,AAAkC,CAAA,QAAlC,CAACO,IAAI,EAAE,0BAA0B,CAAC,CAAC;IACzC,OAAOA,IAAI,CAAC;CACb"}