{"version": 3, "sources": ["../../../src/config/configAsync.ts"], "sourcesContent": ["import { ExpoConfig, getConfig, ProjectConfig } from '@expo/config';\nimport assert from 'assert';\nimport util from 'util';\n\nimport * as Log from '../log';\nimport { CommandError } from '../utils/errors';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { profile } from '../utils/profile';\n\ntype Options = {\n  type?: string;\n  full?: boolean;\n  json?: boolean;\n};\n\nexport function logConfig(config: ExpoConfig | ProjectConfig) {\n  const isObjStr = (str: string): boolean => /^\\w+: {/g.test(str);\n  Log.log(\n    util.inspect(config, {\n      colors: true,\n      compact: false,\n      // Sort objects to the end so that smaller values aren't hidden between large objects.\n      sorted(a: string, b: string) {\n        if (isObjStr(a)) return 1;\n        if (isObjStr(b)) return -1;\n        return 0;\n      },\n      showHidden: false,\n      depth: null,\n    })\n  );\n}\n\nexport async function configAsync(projectRoot: string, options: Options) {\n  const loggingFunctions = {\n    log: console.log,\n    warn: console.warn,\n    error: console.error,\n  };\n  // Disable logging for this command if the user wants to get JSON output.\n  // This will ensure that only the JSON is printed to stdout.\n  if (options.json) {\n    console.log = function () {};\n    console.warn = function () {};\n    console.error = function () {};\n  }\n  setNodeEnv('development');\n  require('@expo/env').load(projectRoot);\n\n  if (options.type) {\n    assert.match(options.type, /^(public|prebuild|introspect)$/);\n  }\n\n  let config: ProjectConfig;\n\n  if (options.type === 'prebuild') {\n    const { getPrebuildConfigAsync } = await import('@expo/prebuild-config');\n\n    config = await profile(getPrebuildConfigAsync)(projectRoot, {\n      platforms: ['ios', 'android'],\n    });\n  } else if (options.type === 'introspect') {\n    const { getPrebuildConfigAsync } = await import('@expo/prebuild-config');\n    const { compileModsAsync } = await import('@expo/config-plugins/build/plugins/mod-compiler');\n\n    config = await profile(getPrebuildConfigAsync)(projectRoot, {\n      platforms: ['ios', 'android'],\n    });\n\n    await compileModsAsync(config.exp, {\n      projectRoot,\n      introspect: true,\n      platforms: ['ios', 'android'],\n      assertMissingModProviders: false,\n    });\n    // @ts-ignore\n    delete config.modRequest;\n    // @ts-ignore\n    delete config.modResults;\n  } else if (options.type === 'public') {\n    config = profile(getConfig)(projectRoot, {\n      skipSDKVersionRequirement: true,\n      isPublicConfig: true,\n    });\n  } else if (options.type) {\n    throw new CommandError(\n      `Invalid option: --type ${options.type}. Valid options are: public, prebuild`\n    );\n  } else {\n    config = profile(getConfig)(projectRoot, {\n      skipSDKVersionRequirement: true,\n    });\n  }\n\n  const configOutput = options.full ? config : config.exp;\n\n  if (!options.json) {\n    Log.log();\n    logConfig(configOutput);\n    Log.log();\n  } else {\n    process.stdout.write(JSON.stringify(configOutput));\n\n    // Re-enable logging functions for testing.\n    console.log = loggingFunctions.log;\n    console.warn = loggingFunctions.warn;\n    console.error = loggingFunctions.error;\n  }\n}\n"], "names": ["logConfig", "config<PERSON><PERSON>", "Log", "config", "isObjStr", "str", "test", "log", "util", "inspect", "colors", "compact", "sorted", "a", "b", "showHidden", "depth", "projectRoot", "options", "loggingFunctions", "console", "warn", "error", "json", "setNodeEnv", "require", "load", "type", "assert", "match", "getPrebuildConfigAsync", "profile", "platforms", "compileModsAsync", "exp", "introspect", "assertMissingModProviders", "modRequest", "modResults", "getConfig", "skipSDKVersionRequirement", "isPublicConfig", "CommandError", "configOutput", "full", "process", "stdout", "write", "JSON", "stringify"], "mappings": "AAAA;;;;QAegBA,SAAS,GAATA,SAAS;QAkBHC,WAAW,GAAXA,WAAW;AAjCoB,IAAA,OAAc,WAAd,cAAc,CAAA;AAChD,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACV,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEXC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACc,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACnB,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACrB,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnC,SAASF,SAAS,CAACG,MAAkC,EAAE;IAC5D,MAAMC,QAAQ,GAAG,CAACC,GAAW,GAAc,WAAWC,IAAI,CAACD,GAAG,CAAC;IAAC;IAChEH,GAAG,CAACK,GAAG,CACLC,KAAI,QAAA,CAACC,OAAO,CAACN,MAAM,EAAE;QACnBO,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,KAAK;QACd,sFAAsF;QACtFC,MAAM,EAACC,CAAS,EAAEC,CAAS,EAAE;YAC3B,IAAIV,QAAQ,CAACS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YAC1B,IAAIT,QAAQ,CAACU,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;YAC3B,OAAO,CAAC,CAAC;SACV;QACDC,UAAU,EAAE,KAAK;QACjBC,KAAK,EAAE,IAAI;KACZ,CAAC,CACH,CAAC;CACH;AAEM,eAAef,WAAW,CAACgB,WAAmB,EAAEC,OAAgB,EAAE;IACvE,MAAMC,gBAAgB,GAAG;QACvBZ,GAAG,EAAEa,OAAO,CAACb,GAAG;QAChBc,IAAI,EAAED,OAAO,CAACC,IAAI;QAClBC,KAAK,EAAEF,OAAO,CAACE,KAAK;KACrB,AAAC;IACF,yEAAyE;IACzE,4DAA4D;IAC5D,IAAIJ,OAAO,CAACK,IAAI,EAAE;QAChBH,OAAO,CAACb,GAAG,GAAG,WAAY,EAAE,CAAC;QAC7Ba,OAAO,CAACC,IAAI,GAAG,WAAY,EAAE,CAAC;QAC9BD,OAAO,CAACE,KAAK,GAAG,WAAY,EAAE,CAAC;KAChC;IACDE,CAAAA,GAAAA,QAAU,AAAe,CAAA,WAAf,CAAC,aAAa,CAAC,CAAC;IAC1BC,OAAO,CAAC,WAAW,CAAC,CAACC,IAAI,CAACT,WAAW,CAAC,CAAC;IAEvC,IAAIC,OAAO,CAACS,IAAI,EAAE;QAChBC,OAAM,QAAA,CAACC,KAAK,CAACX,OAAO,CAACS,IAAI,mCAAmC,CAAC;KAC9D;IAED,IAAIxB,MAAM,AAAe,AAAC;IAE1B,IAAIe,OAAO,CAACS,IAAI,KAAK,UAAU,EAAE;QAC/B,MAAM,EAAEG,sBAAsB,CAAA,EAAE,GAAG,MAAM;mDAAO,uBAAuB;UAAC,AAAC;QAEzE3B,MAAM,GAAG,MAAM4B,CAAAA,GAAAA,QAAO,AAAwB,CAAA,QAAxB,CAACD,sBAAsB,CAAC,CAACb,WAAW,EAAE;YAC1De,SAAS,EAAE;gBAAC,KAAK;gBAAE,SAAS;aAAC;SAC9B,CAAC,CAAC;KACJ,MAAM,IAAId,OAAO,CAACS,IAAI,KAAK,YAAY,EAAE;QACxC,MAAM,EAAEG,sBAAsB,CAAA,EAAE,GAAG,MAAM;mDAAO,uBAAuB;UAAC,AAAC;QACzE,MAAM,EAAEG,gBAAgB,CAAA,EAAE,GAAG,MAAM;mDAAO,iDAAiD;UAAC,AAAC;QAE7F9B,MAAM,GAAG,MAAM4B,CAAAA,GAAAA,QAAO,AAAwB,CAAA,QAAxB,CAACD,sBAAsB,CAAC,CAACb,WAAW,EAAE;YAC1De,SAAS,EAAE;gBAAC,KAAK;gBAAE,SAAS;aAAC;SAC9B,CAAC,CAAC;QAEH,MAAMC,gBAAgB,CAAC9B,MAAM,CAAC+B,GAAG,EAAE;YACjCjB,WAAW;YACXkB,UAAU,EAAE,IAAI;YAChBH,SAAS,EAAE;gBAAC,KAAK;gBAAE,SAAS;aAAC;YAC7BI,yBAAyB,EAAE,KAAK;SACjC,CAAC,CAAC;QACH,aAAa;QACb,OAAOjC,MAAM,CAACkC,UAAU,CAAC;QACzB,aAAa;QACb,OAAOlC,MAAM,CAACmC,UAAU,CAAC;KAC1B,MAAM,IAAIpB,OAAO,CAACS,IAAI,KAAK,QAAQ,EAAE;QACpCxB,MAAM,GAAG4B,CAAAA,GAAAA,QAAO,AAAW,CAAA,QAAX,CAACQ,OAAS,UAAA,CAAC,CAACtB,WAAW,EAAE;YACvCuB,yBAAyB,EAAE,IAAI;YAC/BC,cAAc,EAAE,IAAI;SACrB,CAAC,CAAC;KACJ,MAAM,IAAIvB,OAAO,CAACS,IAAI,EAAE;QACvB,MAAM,IAAIe,OAAY,aAAA,CACpB,CAAC,uBAAuB,EAAExB,OAAO,CAACS,IAAI,CAAC,qCAAqC,CAAC,CAC9E,CAAC;KACH,MAAM;QACLxB,MAAM,GAAG4B,CAAAA,GAAAA,QAAO,AAAW,CAAA,QAAX,CAACQ,OAAS,UAAA,CAAC,CAACtB,WAAW,EAAE;YACvCuB,yBAAyB,EAAE,IAAI;SAChC,CAAC,CAAC;KACJ;IAED,MAAMG,YAAY,GAAGzB,OAAO,CAAC0B,IAAI,GAAGzC,MAAM,GAAGA,MAAM,CAAC+B,GAAG,AAAC;IAExD,IAAI,CAAChB,OAAO,CAACK,IAAI,EAAE;QACjBrB,GAAG,CAACK,GAAG,EAAE,CAAC;QACVP,SAAS,CAAC2C,YAAY,CAAC,CAAC;QACxBzC,GAAG,CAACK,GAAG,EAAE,CAAC;KACX,MAAM;QACLsC,OAAO,CAACC,MAAM,CAACC,KAAK,CAACC,IAAI,CAACC,SAAS,CAACN,YAAY,CAAC,CAAC,CAAC;QAEnD,2CAA2C;QAC3CvB,OAAO,CAACb,GAAG,GAAGY,gBAAgB,CAACZ,GAAG,CAAC;QACnCa,OAAO,CAACC,IAAI,GAAGF,gBAAgB,CAACE,IAAI,CAAC;QACrCD,OAAO,CAACE,KAAK,GAAGH,gBAAgB,CAACG,KAAK,CAAC;KACxC;CACF"}