{"version": 3, "sources": ["../../../src/config/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../utils/args';\n\nexport const expoConfig: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--full': <PERSON>olean,\n      '--json': Boolean,\n      '--type': String,\n      // Aliases\n      '-h': '--help',\n      '-t': '--type',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Show the project config`,\n      chalk`npx expo config {dim <dir>}`,\n      [\n        chalk`<dir>                                    Directory of the Expo project. {dim Default: Current working directory}`,\n        `--full                                   Include all project config data`,\n        `--json                                   Output in JSON format`,\n        `-t, --type <public|prebuild|introspect>  Type of config to show`,\n        `-h, --help                               Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo config -h` shows as fast as possible.\n  const [\n    // ./configAsync\n    { configAsync },\n    // ../utils/errors\n    { logCmdError },\n  ] = await Promise.all([import('./configAsync'), import('../utils/errors')]);\n\n  return configAsync(getProjectRoot(args), {\n    // Parsed options\n    full: args['--full'],\n    json: args['--json'],\n    type: args['--type'],\n  }).catch(logCmdError);\n};\n"], "names": ["expoConfig", "argv", "args", "assertArgs", "Boolean", "String", "printHelp", "chalk", "join", "config<PERSON><PERSON>", "logCmdError", "Promise", "all", "getProjectRoot", "full", "json", "type", "catch"], "mappings": "AAAA;;;;;;AACkB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAG6B,IAAA,KAAe,WAAf,eAAe,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,MAAMA,UAAU,GAAY,OAAOC,IAAI,GAAK;IACjD,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,KAAU,AAYtB,CAAA,WAZsB,CACrB;QACE,QAAQ;QACR,QAAQ,EAAEC,OAAO;QACjB,QAAQ,EAAEA,OAAO;QACjB,QAAQ,EAAEA,OAAO;QACjB,QAAQ,EAAEC,MAAM;QAChB,UAAU;QACV,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,QAAQ;KACf,EACDJ,IAAI,CACL,AAAC;IAEF,IAAIC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBI,CAAAA,GAAAA,KAAS,AAUR,CAAA,UAVQ,CACP,CAAC,uBAAuB,CAAC,EACzBC,MAAK,QAAA,CAAC,2BAA2B,CAAC,EAClC;YACEA,MAAK,QAAA,CAAC,gHAAgH,CAAC;YACvH,CAAC,wEAAwE,CAAC;YAC1E,CAAC,8DAA8D,CAAC;YAChE,CAAC,+DAA+D,CAAC;YACjE,CAAC,mDAAmD,CAAC;SACtD,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;KACH;IAED,wFAAwF;IACxF,MAAM,CACJ,gBAAgB;IAChB,EAAEC,WAAW,CAAA,EAAE,EACf,kBAAkB;IAClB,EAAEC,WAAW,CAAA,EAAE,GAChB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC;QAAC;mDAAO,eAAe;UAAC;QAAE;mDAAO,iBAAiB;UAAC;KAAC,CAAC,AAAC;IAE5E,OAAOH,WAAW,CAACI,CAAAA,GAAAA,KAAc,AAAM,CAAA,eAAN,CAACX,IAAI,CAAC,EAAE;QACvC,iBAAiB;QACjBY,IAAI,EAAEZ,IAAI,CAAC,QAAQ,CAAC;QACpBa,IAAI,EAAEb,IAAI,CAAC,QAAQ,CAAC;QACpBc,IAAI,EAAEd,IAAI,CAAC,QAAQ,CAAC;KACrB,CAAC,CAACe,KAAK,CAACP,WAAW,CAAC,CAAC;CACvB,AAAC;QA3CWV,UAAU,GAAVA,UAAU"}