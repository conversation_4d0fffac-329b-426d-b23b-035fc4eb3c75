{"version": 3, "sources": ["../../../src/customize/customizeAsync.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\n\nimport { findUpProjectRootOrAssert } from '../utils/findUp';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { queryAndGenerateAsync, selectAndGenerateAsync } from './generate';\nimport { Options } from './resolveOptions';\nimport { DestinationResolutionProps } from './templates';\n\nexport async function customizeAsync(files: string[], options: Options, extras: any[]) {\n  setNodeEnv('development');\n  // Locate the project root based on the process current working directory.\n  // This enables users to run `npx expo customize` from a subdirectory of the project.\n  const projectRoot = findUpProjectRootOrAssert(process.cwd());\n\n  require('@expo/env').load(projectRoot);\n\n  // Get the static path (defaults to 'web/')\n  // Doesn't matter if expo is installed or which mode is used.\n  const { exp } = getConfig(projectRoot, {\n    skipSDKVersionRequirement: true,\n  });\n\n  // Create the destination resolution props which are used in both\n  // the query and select functions.\n  const props: DestinationResolutionProps = {\n    webStaticPath: exp.web?.staticPath ?? 'web',\n  };\n\n  // If the user provided files, we'll generate them without prompting.\n  if (files.length) {\n    return queryAndGenerateAsync(projectRoot, {\n      files,\n      props,\n      extras,\n    });\n  }\n\n  // Otherwise, we'll prompt the user to select which files to generate.\n  await selectAndGenerateAsync(projectRoot, {\n    props,\n    extras,\n  });\n}\n"], "names": ["customizeAsync", "files", "options", "extras", "exp", "setNodeEnv", "projectRoot", "findUpProjectRootOrAssert", "process", "cwd", "require", "load", "getConfig", "skipSDKVersionRequirement", "props", "webStaticPath", "web", "staticPath", "length", "queryAndGenerateAsync", "selectAndGenerateAsync"], "mappings": "AAAA;;;;QAQsBA,cAAc,GAAdA,cAAc;AARV,IAAA,OAAc,WAAd,cAAc,CAAA;AAEE,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AAChC,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACiB,IAAA,SAAY,WAAZ,YAAY,CAAA;AAInE,eAAeA,cAAc,CAACC,KAAe,EAAEC,OAAgB,EAAEC,MAAa,EAAE;QAiBpEC,GAAO;IAhBxBC,CAAAA,GAAAA,QAAU,AAAe,CAAA,WAAf,CAAC,aAAa,CAAC,CAAC;IAC1B,0EAA0E;IAC1E,qFAAqF;IACrF,MAAMC,WAAW,GAAGC,CAAAA,GAAAA,OAAyB,AAAe,CAAA,0BAAf,CAACC,OAAO,CAACC,GAAG,EAAE,CAAC,AAAC;IAE7DC,OAAO,CAAC,WAAW,CAAC,CAACC,IAAI,CAACL,WAAW,CAAC,CAAC;IAEvC,2CAA2C;IAC3C,6DAA6D;IAC7D,MAAM,EAAEF,GAAG,CAAA,EAAE,GAAGQ,CAAAA,GAAAA,OAAS,AAEvB,CAAA,UAFuB,CAACN,WAAW,EAAE;QACrCO,yBAAyB,EAAE,IAAI;KAChC,CAAC,AAAC;QAKcT,IAAmB;IAHpC,iEAAiE;IACjE,kCAAkC;IAClC,MAAMU,KAAK,GAA+B;QACxCC,aAAa,EAAEX,CAAAA,IAAmB,GAAnBA,CAAAA,GAAO,GAAPA,GAAG,CAACY,GAAG,SAAY,GAAnBZ,KAAAA,CAAmB,GAAnBA,GAAO,CAAEa,UAAU,YAAnBb,IAAmB,GAAI,KAAK;KAC5C,AAAC;IAEF,qEAAqE;IACrE,IAAIH,KAAK,CAACiB,MAAM,EAAE;QAChB,OAAOC,CAAAA,GAAAA,SAAqB,AAI1B,CAAA,sBAJ0B,CAACb,WAAW,EAAE;YACxCL,KAAK;YACLa,KAAK;YACLX,MAAM;SACP,CAAC,CAAC;KACJ;IAED,sEAAsE;IACtE,MAAMiB,CAAAA,GAAAA,SAAsB,AAG1B,CAAA,uBAH0B,CAACd,WAAW,EAAE;QACxCQ,KAAK;QACLX,MAAM;KACP,CAAC,CAAC;CACJ"}