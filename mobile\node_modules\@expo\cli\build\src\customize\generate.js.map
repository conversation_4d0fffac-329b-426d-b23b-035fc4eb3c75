{"version": 3, "sources": ["../../../src/customize/generate.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { installAsync } from '../install/installAsync';\nimport { Log } from '../log';\nimport { copyAsync } from '../utils/dir';\nimport { CommandError } from '../utils/errors';\nimport { DestinationResolutionProps, selectTemplatesAsync, TEMPLATES } from './templates';\n\nexport async function queryAndGenerateAsync(\n  projectRoot: string,\n  {\n    files,\n    props,\n    extras,\n  }: {\n    files: string[];\n    props: DestinationResolutionProps;\n    /** Any extra props to pass to the install command. */\n    extras: any[];\n  }\n) {\n  const valid = files.filter(\n    (file) => !!TEMPLATES.find((template) => template.destination(props) === file)\n  );\n\n  if (valid.length !== files.length) {\n    const diff = files.filter(\n      (file) => !TEMPLATES.find((template) => template.destination(props) === file)\n    );\n    throw new CommandError(\n      `Invalid files: ${diff.join(', ')}. Allowed: ${TEMPLATES.map((template) =>\n        template.destination(props)\n      ).join(', ')}`\n    );\n  }\n\n  if (!valid.length) {\n    return;\n  }\n  Log.log(`Generating: ${valid.join(', ')}`);\n  return generateAsync(projectRoot, {\n    answer: files.map((file) =>\n      TEMPLATES.findIndex((template) => template.destination(props) === file)\n    ),\n    props,\n    extras,\n  });\n}\n\n/** Select templates to generate then generate and install. */\nexport async function selectAndGenerateAsync(\n  projectRoot: string,\n  {\n    props,\n    extras,\n  }: {\n    props: DestinationResolutionProps;\n    /** Any extra props to pass to the install command. */\n    extras: any[];\n  }\n) {\n  const answer = await selectTemplatesAsync(projectRoot, props);\n\n  if (!answer?.length) {\n    Log.exit('\\n\\u203A Exiting with no change...', 0);\n  }\n\n  await generateAsync(projectRoot, {\n    answer,\n    props,\n    extras,\n  });\n}\n\nasync function generateAsync(\n  projectRoot: string,\n  {\n    answer,\n    props,\n    extras,\n  }: {\n    answer: number[];\n    props: DestinationResolutionProps;\n    /** Any extra props to pass to the install command. */\n    extras: any[];\n  }\n) {\n  // Copy files\n  await Promise.all(\n    answer.map(async (file) => {\n      const template = TEMPLATES[file];\n\n      if (template.id === 'tsconfig.json') {\n        const { typescript } = await import('./typescript');\n        return typescript(projectRoot);\n      }\n\n      const projectFilePath = path.resolve(projectRoot, template.destination(props));\n      // copy the file from template\n      return copyAsync(template.file(projectRoot), projectFilePath, {\n        overwrite: true,\n        recursive: true,\n      });\n    })\n  );\n\n  // Install dependencies\n  const packages = answer\n    .map((file) => TEMPLATES[file].dependencies)\n    .flat()\n    .filter((pkg) => !resolveFrom.silent(projectRoot, pkg));\n  if (packages.length) {\n    Log.debug('Installing ' + packages.join(', '));\n    await installAsync(packages, {}, ['--dev', ...extras]);\n  }\n}\n"], "names": ["queryAndGenerateAsync", "selectAndGenerateAsync", "projectRoot", "files", "props", "extras", "valid", "filter", "file", "TEMPLATES", "find", "template", "destination", "length", "diff", "CommandError", "join", "map", "Log", "log", "generateAsync", "answer", "findIndex", "selectTemplatesAsync", "exit", "Promise", "all", "id", "typescript", "projectFilePath", "path", "resolve", "copyAsync", "overwrite", "recursive", "packages", "dependencies", "flat", "pkg", "resolveFrom", "silent", "debug", "installAsync"], "mappings": "AAAA;;;;QASsBA,qBAAqB,GAArBA,qBAAqB;QA0CrBC,sBAAsB,GAAtBA,sBAAsB;AAnD3B,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,YAAc,kCAAd,cAAc,EAAA;AAET,IAAA,aAAyB,WAAzB,yBAAyB,CAAA;AAClC,IAAA,IAAQ,WAAR,QAAQ,CAAA;AACF,IAAA,IAAc,WAAd,cAAc,CAAA;AACX,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AAC8B,IAAA,UAAa,WAAb,aAAa,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElF,eAAeD,qBAAqB,CACzCE,WAAmB,EACnB,EACEC,KAAK,CAAA,EACLC,KAAK,CAAA,EACLC,MAAM,CAAA,EAMP,EACD;IACA,MAAMC,KAAK,GAAGH,KAAK,CAACI,MAAM,CACxB,CAACC,IAAI,GAAK,CAAC,CAACC,UAAS,UAAA,CAACC,IAAI,CAAC,CAACC,QAAQ,GAAKA,QAAQ,CAACC,WAAW,CAACR,KAAK,CAAC,KAAKI,IAAI;QAAA,CAAC;IAAA,CAC/E,AAAC;IAEF,IAAIF,KAAK,CAACO,MAAM,KAAKV,KAAK,CAACU,MAAM,EAAE;QACjC,MAAMC,IAAI,GAAGX,KAAK,CAACI,MAAM,CACvB,CAACC,IAAI,GAAK,CAACC,UAAS,UAAA,CAACC,IAAI,CAAC,CAACC,QAAQ,GAAKA,QAAQ,CAACC,WAAW,CAACR,KAAK,CAAC,KAAKI,IAAI;YAAA,CAAC;QAAA,CAC9E,AAAC;QACF,MAAM,IAAIO,OAAY,aAAA,CACpB,CAAC,eAAe,EAAED,IAAI,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAEP,UAAS,UAAA,CAACQ,GAAG,CAAC,CAACN,QAAQ,GACpEA,QAAQ,CAACC,WAAW,CAACR,KAAK,CAAC;QAAA,CAC5B,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CACf,CAAC;KACH;IAED,IAAI,CAACV,KAAK,CAACO,MAAM,EAAE;QACjB,OAAO;KACR;IACDK,IAAG,IAAA,CAACC,GAAG,CAAC,CAAC,YAAY,EAAEb,KAAK,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,OAAOI,aAAa,CAAClB,WAAW,EAAE;QAChCmB,MAAM,EAAElB,KAAK,CAACc,GAAG,CAAC,CAACT,IAAI,GACrBC,UAAS,UAAA,CAACa,SAAS,CAAC,CAACX,QAAQ,GAAKA,QAAQ,CAACC,WAAW,CAACR,KAAK,CAAC,KAAKI,IAAI;YAAA,CAAC;QAAA,CACxE;QACDJ,KAAK;QACLC,MAAM;KACP,CAAC,CAAC;CACJ;AAGM,eAAeJ,sBAAsB,CAC1CC,WAAmB,EACnB,EACEE,KAAK,CAAA,EACLC,MAAM,CAAA,EAKP,EACD;IACA,MAAMgB,MAAM,GAAG,MAAME,CAAAA,GAAAA,UAAoB,AAAoB,CAAA,qBAApB,CAACrB,WAAW,EAAEE,KAAK,CAAC,AAAC;IAE9D,IAAI,CAACiB,CAAAA,MAAM,QAAQ,GAAdA,KAAAA,CAAc,GAAdA,MAAM,CAAER,MAAM,CAAA,EAAE;QACnBK,IAAG,IAAA,CAACM,IAAI,CAAC,oCAAoC,EAAE,CAAC,CAAC,CAAC;KACnD;IAED,MAAMJ,aAAa,CAAClB,WAAW,EAAE;QAC/BmB,MAAM;QACNjB,KAAK;QACLC,MAAM;KACP,CAAC,CAAC;CACJ;AAED,eAAee,aAAa,CAC1BlB,WAAmB,EACnB,EACEmB,MAAM,CAAA,EACNjB,KAAK,CAAA,EACLC,MAAM,CAAA,EAMP,EACD;IACA,aAAa;IACb,MAAMoB,OAAO,CAACC,GAAG,CACfL,MAAM,CAACJ,GAAG,CAAC,OAAOT,IAAI,GAAK;QACzB,MAAMG,QAAQ,GAAGF,UAAS,UAAA,CAACD,IAAI,CAAC,AAAC;QAEjC,IAAIG,QAAQ,CAACgB,EAAE,KAAK,eAAe,EAAE;YACnC,MAAM,EAAEC,UAAU,CAAA,EAAE,GAAG,MAAM;uDAAO,cAAc;cAAC,AAAC;YACpD,OAAOA,UAAU,CAAC1B,WAAW,CAAC,CAAC;SAChC;QAED,MAAM2B,eAAe,GAAGC,KAAI,QAAA,CAACC,OAAO,CAAC7B,WAAW,EAAES,QAAQ,CAACC,WAAW,CAACR,KAAK,CAAC,CAAC,AAAC;QAC/E,8BAA8B;QAC9B,OAAO4B,CAAAA,GAAAA,IAAS,AAGd,CAAA,UAHc,CAACrB,QAAQ,CAACH,IAAI,CAACN,WAAW,CAAC,EAAE2B,eAAe,EAAE;YAC5DI,SAAS,EAAE,IAAI;YACfC,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;KACJ,CAAC,CACH,CAAC;IAEF,uBAAuB;IACvB,MAAMC,QAAQ,GAAGd,MAAM,CACpBJ,GAAG,CAAC,CAACT,IAAI,GAAKC,UAAS,UAAA,CAACD,IAAI,CAAC,CAAC4B,YAAY;IAAA,CAAC,CAC3CC,IAAI,EAAE,CACN9B,MAAM,CAAC,CAAC+B,GAAG,GAAK,CAACC,YAAW,QAAA,CAACC,MAAM,CAACtC,WAAW,EAAEoC,GAAG,CAAC;IAAA,CAAC,AAAC;IAC1D,IAAIH,QAAQ,CAACtB,MAAM,EAAE;QACnBK,IAAG,IAAA,CAACuB,KAAK,CAAC,aAAa,GAAGN,QAAQ,CAACnB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/C,MAAM0B,CAAAA,GAAAA,aAAY,AAAoC,CAAA,aAApC,CAACP,QAAQ,EAAE,EAAE,EAAE;YAAC,OAAO;eAAK9B,MAAM;SAAC,CAAC,CAAC;KACxD;CACF"}