{"version": 3, "sources": ["../../../src/customize/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../utils/args';\n\nexport const expoCustomize: Command = async (argv) => {\n  const args = assertWithOptionsArgs(\n    {\n      // Other options are parsed manually.\n      '--help': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n    },\n    {\n      argv,\n      // Allow other options, we'll throw an error if unexpected values are passed.\n      permissive: true,\n    }\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Generate static project files`,\n      chalk`npx expo customize {dim [files...] -- [options]}`,\n      [\n        chalk`[files...]  List of files to generate`,\n        chalk`[options]   Options to pass to the install command`,\n        `-h, --help  Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo install -h` shows as fast as possible.\n  const { customizeAsync } = require('./customizeAsync') as typeof import('./customizeAsync');\n  const { logCmdError } = require('../utils/errors') as typeof import('../utils/errors');\n  const { resolveArgsAsync } = require('./resolveOptions') as typeof import('./resolveOptions');\n\n  const { variadic, options, extras } = await resolveArgsAsync(process.argv.slice(3)).catch(\n    logCmdError\n  );\n  return customizeAsync(variadic, options, extras).catch(logCmdError);\n};\n"], "names": ["expoCustomize", "argv", "args", "assertWithOptionsArgs", "Boolean", "permissive", "printHelp", "chalk", "join", "customizeAsync", "require", "logCmdError", "resolveArgsAsync", "variadic", "options", "extras", "process", "slice", "catch"], "mappings": "AAAA;;;;;;AACkB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAGwB,IAAA,KAAe,WAAf,eAAe,CAAA;;;;;;AAEzD,MAAMA,aAAa,GAAY,OAAOC,IAAI,GAAK;IACpD,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,KAAqB,AAYjC,CAAA,sBAZiC,CAChC;QACE,qCAAqC;QACrC,QAAQ,EAAEC,OAAO;QACjB,UAAU;QACV,IAAI,EAAE,QAAQ;KACf,EACD;QACEH,IAAI;QACJ,6EAA6E;QAC7EI,UAAU,EAAE,IAAI;KACjB,CACF,AAAC;IAEF,IAAIH,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBI,CAAAA,GAAAA,KAAS,AAQR,CAAA,UARQ,CACP,CAAC,6BAA6B,CAAC,EAC/BC,MAAK,QAAA,CAAC,gDAAgD,CAAC,EACvD;YACEA,MAAK,QAAA,CAAC,qCAAqC,CAAC;YAC5CA,MAAK,QAAA,CAAC,kDAAkD,CAAC;YACzD,CAAC,sBAAsB,CAAC;SACzB,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;KACH;IAED,yFAAyF;IACzF,MAAM,EAAEC,cAAc,CAAA,EAAE,GAAGC,OAAO,CAAC,kBAAkB,CAAC,AAAqC,AAAC;IAC5F,MAAM,EAAEC,WAAW,CAAA,EAAE,GAAGD,OAAO,CAAC,iBAAiB,CAAC,AAAoC,AAAC;IACvF,MAAM,EAAEE,gBAAgB,CAAA,EAAE,GAAGF,OAAO,CAAC,kBAAkB,CAAC,AAAqC,AAAC;IAE9F,MAAM,EAAEG,QAAQ,CAAA,EAAEC,OAAO,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAG,MAAMH,gBAAgB,CAACI,OAAO,CAACf,IAAI,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CACvFP,WAAW,CACZ,AAAC;IACF,OAAOF,cAAc,CAACI,QAAQ,EAAEC,OAAO,EAAEC,MAAM,CAAC,CAACG,KAAK,CAACP,WAAW,CAAC,CAAC;CACrE,AAAC;QApCWX,aAAa,GAAbA,aAAa"}