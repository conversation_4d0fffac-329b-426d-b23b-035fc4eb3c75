{"version": 3, "sources": ["../../../src/customize/resolveOptions.ts"], "sourcesContent": ["import { assertUnexpectedObjectKeys, parseVariadicArguments } from '../utils/variadic';\n\nexport type Options = object;\n\nexport async function resolveArgsAsync(\n  argv: string[]\n): Promise<{ variadic: string[]; options: Options; extras: string[] }> {\n  const { variadic, extras, flags } = parseVariadicArguments(argv);\n\n  assertUnexpectedObjectKeys([], flags);\n\n  return {\n    // Variadic arguments like `npx expo install react react-dom` -> ['react', 'react-dom']\n    variadic,\n    options: {},\n    extras,\n  };\n}\n"], "names": ["resolveArgsAsync", "argv", "variadic", "extras", "flags", "parseVariadicArguments", "assertUnexpectedObjectKeys", "options"], "mappings": "AAAA;;;;QAIsBA,gBAAgB,GAAhBA,gBAAgB;AAJ6B,IAAA,SAAmB,WAAnB,mBAAmB,CAAA;AAI/E,eAAeA,gBAAgB,CACpCC,IAAc,EACuD;IACrE,MAAM,EAAEC,QAAQ,CAAA,EAAEC,MAAM,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAGC,CAAAA,GAAAA,SAAsB,AAAM,CAAA,uBAAN,CAACJ,IAAI,CAAC,AAAC;IAEjEK,CAAAA,GAAAA,SAA0B,AAAW,CAAA,2BAAX,CAAC,EAAE,EAAEF,KAAK,CAAC,CAAC;IAEtC,OAAO;QACL,uFAAuF;QACvFF,QAAQ;QACRK,OAAO,EAAE,EAAE;QACXJ,MAAM;KACP,CAAC;CACH"}