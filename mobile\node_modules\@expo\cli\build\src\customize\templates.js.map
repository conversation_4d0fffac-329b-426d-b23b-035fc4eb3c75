{"version": 3, "sources": ["../../../src/customize/templates.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport prompt, { ExpoChoice } from '../utils/prompts';\n\nconst debug = require('debug')('expo:customize:templates');\n\nexport type DestinationResolutionProps = {\n  /** Web 'public' folder path (defaults to `/web`). This technically can be changed but shouldn't be. */\n  webStaticPath: string;\n};\n\nfunction importFromExpoWebpackConfig(projectRoot: string, folder: string, moduleId: string) {\n  try {\n    const filePath = resolveFrom(projectRoot, `@expo/webpack-config/${folder}/${moduleId}`);\n    debug(`Using @expo/webpack-config template for \"${moduleId}\": ${filePath}`);\n    return filePath;\n  } catch {\n    debug(`@expo/webpack-config template for \"${moduleId}\" not found, falling back on @expo/cli`);\n  }\n  return importFromVendor(projectRoot, moduleId);\n}\n\nfunction importFromVendor(projectRoot: string, moduleId: string) {\n  try {\n    const filePath = resolveFrom(projectRoot, '@expo/cli/static/template/' + moduleId);\n    debug(`Using @expo/cli template for \"${moduleId}\": ${filePath}`);\n    return filePath;\n  } catch {\n    // For dev mode, testing and other cases where @expo/cli is not installed\n    const filePath = require.resolve(`@expo/cli/static/template/${moduleId}`);\n    debug(\n      `Local @expo/cli template for \"${moduleId}\" not found, falling back on template relative to @expo/cli: ${filePath}`\n    );\n\n    return filePath;\n  }\n}\n\nexport const TEMPLATES: {\n  /** Unique ID for easily indexing. */\n  id: string;\n  /** Template file path to copy into the project. */\n  file: (projectRoot: string) => string;\n  /** Output location for the file in the user project. */\n  destination: (props: DestinationResolutionProps) => string;\n  /** List of dependencies to install in the project. These are used inside of the template file. */\n  dependencies: string[];\n}[] = [\n  {\n    id: 'babel.config.js',\n    file: (projectRoot) => importFromVendor(projectRoot, 'babel.config.js'),\n    destination: () => 'babel.config.js',\n    dependencies: [\n      // Even though this is installed in `expo`, we should add it for now.\n      'babel-preset-expo',\n    ],\n  },\n  {\n    id: 'webpack.config.js',\n    file: (projectRoot) =>\n      importFromExpoWebpackConfig(projectRoot, 'template', 'webpack.config.js'),\n    destination: () => 'webpack.config.js',\n    dependencies: ['@expo/webpack-config'],\n  },\n  {\n    id: 'metro.config.js',\n    dependencies: ['@expo/metro-config'],\n    destination: () => 'metro.config.js',\n    file: (projectRoot) => importFromVendor(projectRoot, 'metro.config.js'),\n  },\n  {\n    id: 'serve.json',\n    file: (projectRoot) => importFromExpoWebpackConfig(projectRoot, 'web-default', 'serve.json'),\n    // web/serve.json\n    destination: ({ webStaticPath }) => webStaticPath + '/serve.json',\n    dependencies: [],\n  },\n  {\n    id: 'index.html',\n    file: (projectRoot) => importFromExpoWebpackConfig(projectRoot, 'web-default', 'index.html'),\n    // web/index.html\n    destination: ({ webStaticPath }) => webStaticPath + '/index.html',\n    dependencies: [],\n  },\n  {\n    // `tsconfig.json` is special cased and don't not follow the template\n    id: 'tsconfig.json',\n    dependencies: [],\n    destination: () => 'tsconfig.json',\n    file: () => '',\n  },\n];\n\n/** Generate the prompt choices. */\nfunction createChoices(\n  projectRoot: string,\n  props: DestinationResolutionProps\n): ExpoChoice<number>[] {\n  return TEMPLATES.map((template, index) => {\n    const destination = template.destination(props);\n    const localProjectFile = path.resolve(projectRoot, destination);\n    const exists = fs.existsSync(localProjectFile);\n\n    return {\n      title: destination,\n      value: index,\n      description: exists ? chalk.red('This will overwrite the existing file') : undefined,\n    };\n  });\n}\n\n/** Prompt to select templates to add. */\nexport async function selectTemplatesAsync(projectRoot: string, props: DestinationResolutionProps) {\n  const options = createChoices(projectRoot, props);\n\n  const { answer } = await prompt({\n    type: 'multiselect',\n    name: 'answer',\n    message: 'Which files would you like to generate?',\n    hint: '- Space to select. Return to submit',\n    warn: 'File already exists.',\n    limit: options.length,\n    instructions: '',\n    choices: options,\n  });\n  return answer;\n}\n"], "names": ["selectTemplatesAsync", "debug", "require", "importFromExpoWebpackConfig", "projectRoot", "folder", "moduleId", "filePath", "resolveFrom", "importFromVendor", "resolve", "TEMPLATES", "id", "file", "destination", "dependencies", "webStaticPath", "createChoices", "props", "map", "template", "index", "localProjectFile", "path", "exists", "fs", "existsSync", "title", "value", "description", "chalk", "red", "undefined", "options", "answer", "prompt", "type", "name", "message", "hint", "warn", "limit", "length", "instructions", "choices"], "mappings": "AAAA;;;;QAmHsBA,oBAAoB,GAApBA,oBAAoB;;AAnHxB,IAAA,MAAO,kCAAP,OAAO,EAAA;AACV,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,YAAc,kCAAd,cAAc,EAAA;AAEH,IAAA,QAAkB,kCAAlB,kBAAkB,EAAA;;;;;;AAErD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC,AAAC;AAO3D,SAASC,2BAA2B,CAACC,WAAmB,EAAEC,MAAc,EAAEC,QAAgB,EAAE;IAC1F,IAAI;QACF,MAAMC,QAAQ,GAAGC,CAAAA,GAAAA,YAAW,AAA2D,CAAA,QAA3D,CAACJ,WAAW,EAAE,CAAC,qBAAqB,EAAEC,MAAM,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC,AAAC;QACxFL,KAAK,CAAC,CAAC,yCAAyC,EAAEK,QAAQ,CAAC,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5E,OAAOA,QAAQ,CAAC;KACjB,CAAC,OAAM;QACNN,KAAK,CAAC,CAAC,mCAAmC,EAAEK,QAAQ,CAAC,sCAAsC,CAAC,CAAC,CAAC;KAC/F;IACD,OAAOG,gBAAgB,CAACL,WAAW,EAAEE,QAAQ,CAAC,CAAC;CAChD;AAED,SAASG,gBAAgB,CAACL,WAAmB,EAAEE,QAAgB,EAAE;IAC/D,IAAI;QACF,MAAMC,QAAQ,GAAGC,CAAAA,GAAAA,YAAW,AAAsD,CAAA,QAAtD,CAACJ,WAAW,EAAE,4BAA4B,GAAGE,QAAQ,CAAC,AAAC;QACnFL,KAAK,CAAC,CAAC,8BAA8B,EAAEK,QAAQ,CAAC,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjE,OAAOA,QAAQ,CAAC;KACjB,CAAC,OAAM;QACN,yEAAyE;QACzE,MAAMA,QAAQ,GAAGL,OAAO,CAACQ,OAAO,CAAC,CAAC,0BAA0B,EAAEJ,QAAQ,CAAC,CAAC,CAAC,AAAC;QAC1EL,KAAK,CACH,CAAC,8BAA8B,EAAEK,QAAQ,CAAC,6DAA6D,EAAEC,QAAQ,CAAC,CAAC,CACpH,CAAC;QAEF,OAAOA,QAAQ,CAAC;KACjB;CACF;AAEM,MAAMI,SAAS,GAShB;IACJ;QACEC,EAAE,EAAE,iBAAiB;QACrBC,IAAI,EAAE,CAACT,WAAW,GAAKK,gBAAgB,CAACL,WAAW,EAAE,iBAAiB,CAAC;QAAA;QACvEU,WAAW,EAAE,IAAM,iBAAiB;QAAA;QACpCC,YAAY,EAAE;YACZ,qEAAqE;YACrE,mBAAmB;SACpB;KACF;IACD;QACEH,EAAE,EAAE,mBAAmB;QACvBC,IAAI,EAAE,CAACT,WAAW,GAChBD,2BAA2B,CAACC,WAAW,EAAE,UAAU,EAAE,mBAAmB,CAAC;QAAA;QAC3EU,WAAW,EAAE,IAAM,mBAAmB;QAAA;QACtCC,YAAY,EAAE;YAAC,sBAAsB;SAAC;KACvC;IACD;QACEH,EAAE,EAAE,iBAAiB;QACrBG,YAAY,EAAE;YAAC,oBAAoB;SAAC;QACpCD,WAAW,EAAE,IAAM,iBAAiB;QAAA;QACpCD,IAAI,EAAE,CAACT,WAAW,GAAKK,gBAAgB,CAACL,WAAW,EAAE,iBAAiB,CAAC;KACxE;IACD;QACEQ,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,CAACT,WAAW,GAAKD,2BAA2B,CAACC,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;QAAA;QAC5F,iBAAiB;QACjBU,WAAW,EAAE,CAAC,EAAEE,aAAa,CAAA,EAAE,GAAKA,aAAa,GAAG,aAAa;QAAA;QACjED,YAAY,EAAE,EAAE;KACjB;IACD;QACEH,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,CAACT,WAAW,GAAKD,2BAA2B,CAACC,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;QAAA;QAC5F,iBAAiB;QACjBU,WAAW,EAAE,CAAC,EAAEE,aAAa,CAAA,EAAE,GAAKA,aAAa,GAAG,aAAa;QAAA;QACjED,YAAY,EAAE,EAAE;KACjB;IACD;QACE,qEAAqE;QACrEH,EAAE,EAAE,eAAe;QACnBG,YAAY,EAAE,EAAE;QAChBD,WAAW,EAAE,IAAM,eAAe;QAAA;QAClCD,IAAI,EAAE,IAAM,EAAE;KACf;CACF,AAAC;QArDWF,SAAS,GAATA,SAAS;AAuDtB,mCAAmC,CACnC,SAASM,aAAa,CACpBb,WAAmB,EACnBc,KAAiC,EACX;IACtB,OAAOP,SAAS,CAACQ,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,GAAK;QACxC,MAAMP,WAAW,GAAGM,QAAQ,CAACN,WAAW,CAACI,KAAK,CAAC,AAAC;QAChD,MAAMI,gBAAgB,GAAGC,KAAI,QAAA,CAACb,OAAO,CAACN,WAAW,EAAEU,WAAW,CAAC,AAAC;QAChE,MAAMU,MAAM,GAAGC,GAAE,QAAA,CAACC,UAAU,CAACJ,gBAAgB,CAAC,AAAC;QAE/C,OAAO;YACLK,KAAK,EAAEb,WAAW;YAClBc,KAAK,EAAEP,KAAK;YACZQ,WAAW,EAAEL,MAAM,GAAGM,MAAK,QAAA,CAACC,GAAG,CAAC,uCAAuC,CAAC,GAAGC,SAAS;SACrF,CAAC;KACH,CAAC,CAAC;CACJ;AAGM,eAAehC,oBAAoB,CAACI,WAAmB,EAAEc,KAAiC,EAAE;IACjG,MAAMe,OAAO,GAAGhB,aAAa,CAACb,WAAW,EAAEc,KAAK,CAAC,AAAC;IAElD,MAAM,EAAEgB,MAAM,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,QAAM,AAS7B,CAAA,QAT6B,CAAC;QAC9BC,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,yCAAyC;QAClDC,IAAI,EAAE,qCAAqC;QAC3CC,IAAI,EAAE,sBAAsB;QAC5BC,KAAK,EAAER,OAAO,CAACS,MAAM;QACrBC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAEX,OAAO;KACjB,CAAC,AAAC;IACH,OAAOC,MAAM,CAAC;CACf"}