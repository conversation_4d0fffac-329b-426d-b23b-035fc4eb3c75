{"version": 3, "sources": ["../../../src/customize/typescript.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\n\nimport { Log } from '../log';\n\nexport async function typescript(projectRoot: string) {\n  const { TypeScriptProjectPrerequisite } = await import(\n    '../start/doctor/typescript/TypeScriptProjectPrerequisite'\n  );\n  const { MetroBundlerDevServer } = await import('../start/server/metro/MetroBundlerDevServer');\n  const { getPlatformBundlers } = await import('../start/server/platformBundlers');\n\n  try {\n    await new TypeScriptProjectPrerequisite(projectRoot).bootstrapAsync();\n  } catch (error: any) {\n    // Ensure the process doesn't fail if the TypeScript check fails.\n    // This could happen during the install.\n    Log.log();\n    Log.exception(error);\n    return;\n  }\n\n  const { exp } = getConfig(projectRoot, { skipSDKVersionRequirement: true });\n  await new MetroBundlerDevServer(\n    projectRoot,\n    getPlatformBundlers(exp),\n    true\n  ).startTypeScriptServices();\n}\n"], "names": ["typescript", "projectRoot", "TypeScriptProjectPrerequisite", "MetroBundlerDevServer", "getPlatformBundlers", "bootstrapAsync", "error", "Log", "log", "exception", "exp", "getConfig", "skipSDKVersionRequirement", "startTypeScriptServices"], "mappings": "AAAA;;;;QAIsBA,UAAU,GAAVA,UAAU;AAJN,IAAA,OAAc,WAAd,cAAc,CAAA;AAEpB,IAAA,IAAQ,WAAR,QAAQ,CAAA;;;;;;;;;;;;;;;;;;;;;;AAErB,eAAeA,UAAU,CAACC,WAAmB,EAAE;IACpD,MAAM,EAAEC,6BAA6B,CAAA,EAAE,GAAG,MAAM;+CAC9C,0DAA0D;MAC3D,AAAC;IACF,MAAM,EAAEC,qBAAqB,CAAA,EAAE,GAAG,MAAM;+CAAO,6CAA6C;MAAC,AAAC;IAC9F,MAAM,EAAEC,mBAAmB,CAAA,EAAE,GAAG,MAAM;+CAAO,kCAAkC;MAAC,AAAC;IAEjF,IAAI;QACF,MAAM,IAAIF,6BAA6B,CAACD,WAAW,CAAC,CAACI,cAAc,EAAE,CAAC;KACvE,CAAC,OAAOC,KAAK,EAAO;QACnB,iEAAiE;QACjE,wCAAwC;QACxCC,IAAG,IAAA,CAACC,GAAG,EAAE,CAAC;QACVD,IAAG,IAAA,CAACE,SAAS,CAACH,KAAK,CAAC,CAAC;QACrB,OAAO;KACR;IAED,MAAM,EAAEI,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAAkD,CAAA,UAAlD,CAACV,WAAW,EAAE;QAAEW,yBAAyB,EAAE,IAAI;KAAE,CAAC,AAAC;IAC5E,MAAM,IAAIT,qBAAqB,CAC7BF,WAAW,EACXG,mBAAmB,CAACM,GAAG,CAAC,EACxB,IAAI,CACL,CAACG,uBAAuB,EAAE,CAAC;CAC7B"}