{"version": 3, "sources": ["../../../src/export/createBundles.ts"], "sourcesContent": ["import { getConfig, Platform, ProjectTarget } from '@expo/config';\n\nimport * as Log from '../log';\nimport { getEntryWithServerRoot } from '../start/server/middleware/ManifestMiddleware';\nimport { bundleAsync, BundleOutput } from './fork-bundleAsync';\n\nexport type PublishOptions = {\n  releaseChannel?: string;\n  target?: ProjectTarget;\n  resetCache?: boolean;\n  maxWorkers?: number;\n};\n\n// TODO: Reduce layers of indirection\nexport async function createBundlesAsync(\n  projectRoot: string,\n  publishOptions: PublishOptions = {},\n  bundleOptions: { platforms: Platform[]; dev?: boolean; minify?: boolean }\n): Promise<Partial<Record<Platform, BundleOutput>>> {\n  if (!bundleOptions.platforms.length) {\n    return {};\n  }\n  const projectConfig = getConfig(projectRoot, { skipSDKVersionRequirement: true });\n  const { exp } = projectConfig;\n\n  const bundles = await bundleAsync(\n    projectRoot,\n    exp,\n    {\n      // If not legacy, ignore the target option to prevent warnings from being thrown.\n      resetCache: publishOptions.resetCache,\n      maxWorkers: publishOptions.maxWorkers,\n      logger: {\n        info(tag: unknown, message: string) {\n          Log.log(message);\n        },\n        error(tag: unknown, message: string) {\n          Log.error(message);\n        },\n      } as any,\n      quiet: false,\n    },\n    bundleOptions.platforms.map((platform: Platform) => ({\n      platform,\n      entryPoint: getEntryWithServerRoot(projectRoot, projectConfig, platform),\n      minify: bundleOptions.minify,\n      dev: bundleOptions.dev,\n    }))\n  );\n\n  // { ios: bundle, android: bundle }\n  return bundleOptions.platforms.reduce<Partial<Record<Platform, BundleOutput>>>(\n    (prev, platform, index) => ({\n      ...prev,\n      [platform]: bundles[index],\n    }),\n    {}\n  );\n}\n"], "names": ["createBundlesAsync", "Log", "projectRoot", "publishOptions", "bundleOptions", "platforms", "length", "projectConfig", "getConfig", "skipSDKVersionRequirement", "exp", "bundles", "bundleAsync", "resetCache", "maxWorkers", "logger", "info", "tag", "message", "log", "error", "quiet", "map", "platform", "entryPoint", "getEntryWithServerRoot", "minify", "dev", "reduce", "prev", "index"], "mappings": "AAAA;;;;QAcsBA,kBAAkB,GAAlBA,kBAAkB;AAdW,IAAA,OAAc,WAAd,cAAc,CAAA;AAErDC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACwB,IAAA,mBAA+C,WAA/C,+CAA+C,CAAA;AAC5C,IAAA,gBAAoB,WAApB,oBAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;AAUvD,eAAeD,kBAAkB,CACtCE,WAAmB,EACnBC,cAA8B,GAAG,EAAE,EACnCC,aAAyE,EACvB;IAClD,IAAI,CAACA,aAAa,CAACC,SAAS,CAACC,MAAM,EAAE;QACnC,OAAO,EAAE,CAAC;KACX;IACD,MAAMC,aAAa,GAAGC,CAAAA,GAAAA,OAAS,AAAkD,CAAA,UAAlD,CAACN,WAAW,EAAE;QAAEO,yBAAyB,EAAE,IAAI;KAAE,CAAC,AAAC;IAClF,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGH,aAAa,AAAC;IAE9B,MAAMI,OAAO,GAAG,MAAMC,CAAAA,GAAAA,gBAAW,AAuBhC,CAAA,YAvBgC,CAC/BV,WAAW,EACXQ,GAAG,EACH;QACE,iFAAiF;QACjFG,UAAU,EAAEV,cAAc,CAACU,UAAU;QACrCC,UAAU,EAAEX,cAAc,CAACW,UAAU;QACrCC,MAAM,EAAE;YACNC,IAAI,EAACC,GAAY,EAAEC,OAAe,EAAE;gBAClCjB,GAAG,CAACkB,GAAG,CAACD,OAAO,CAAC,CAAC;aAClB;YACDE,KAAK,EAACH,GAAY,EAAEC,OAAe,EAAE;gBACnCjB,GAAG,CAACmB,KAAK,CAACF,OAAO,CAAC,CAAC;aACpB;SACF;QACDG,KAAK,EAAE,KAAK;KACb,EACDjB,aAAa,CAACC,SAAS,CAACiB,GAAG,CAAC,CAACC,QAAkB,GAAK,CAAC;YACnDA,QAAQ;YACRC,UAAU,EAAEC,CAAAA,GAAAA,mBAAsB,AAAsC,CAAA,uBAAtC,CAACvB,WAAW,EAAEK,aAAa,EAAEgB,QAAQ,CAAC;YACxEG,MAAM,EAAEtB,aAAa,CAACsB,MAAM;YAC5BC,GAAG,EAAEvB,aAAa,CAACuB,GAAG;SACvB,CAAC;IAAA,CAAC,CACJ,AAAC;IAEF,mCAAmC;IACnC,OAAOvB,aAAa,CAACC,SAAS,CAACuB,MAAM,CACnC,CAACC,IAAI,EAAEN,QAAQ,EAAEO,KAAK,GAAK,CAAC;YAC1B,GAAGD,IAAI;YACP,CAACN,QAAQ,CAAC,EAAEZ,OAAO,CAACmB,KAAK,CAAC;SAC3B,CAAC;IAAA,EACF,EAAE,CACH,CAAC;CACH"}