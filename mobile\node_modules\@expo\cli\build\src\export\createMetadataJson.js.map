{"version": 3, "sources": ["../../../src/export/createMetadataJson.ts"], "sourcesContent": ["import path from 'path';\n\nimport { BundleOutput } from './fork-bundleAsync';\n\nexport type BundlePlatform = 'android' | 'ios';\n\ntype PlatformMetadataAsset = { path: string; ext: string };\n\ntype PlatformMetadata = { bundle: string; assets: PlatformMetadataAsset[] };\n\ntype FileMetadata = {\n  [key in BundlePlatform]: PlatformMetadata;\n};\n\nexport function createMetadataJson({\n  bundles,\n  fileNames,\n}: {\n  bundles: Partial<Record<BundlePlatform, Pick<BundleOutput, 'assets'>>>;\n  fileNames: Record<string, string>;\n}): {\n  version: 0;\n  bundler: 'metro';\n  fileMetadata: FileMetadata;\n} {\n  // Build metadata.json\n  return {\n    version: 0,\n    bundler: 'metro',\n    fileMetadata: Object.entries(bundles).reduce<Record<string, Partial<PlatformMetadata>>>(\n      (metadata, [platform, bundle]) => ({\n        ...metadata,\n        [platform]: {\n          // Get the filename for each platform's bundle.\n          bundle: path.join('bundles', fileNames[platform]!),\n          // Collect all of the assets and convert them to the serial format.\n          assets: bundle.assets\n            .map((asset) =>\n              // Each asset has multiple hashes which we convert and then flatten.\n              asset.fileHashes?.map((hash) => ({\n                path: path.join('assets', hash),\n                ext: asset.type,\n              }))\n            )\n            .filter(Boolean)\n            .flat(),\n        },\n      }),\n      {}\n    ) as FileMetadata,\n  };\n}\n"], "names": ["createMetadataJson", "bundles", "fileNames", "version", "bundler", "fileMetadata", "Object", "entries", "reduce", "metadata", "platform", "bundle", "path", "join", "assets", "map", "asset", "fileHashes", "hash", "ext", "type", "filter", "Boolean", "flat"], "mappings": "AAAA;;;;QAcgBA,kBAAkB,GAAlBA,kBAAkB;AAdjB,IAAA,KAAM,kCAAN,MAAM,EAAA;;;;;;AAchB,SAASA,kBAAkB,CAAC,EACjCC,OAAO,CAAA,EACPC,SAAS,CAAA,EAIV,EAIC;IACA,sBAAsB;IACtB,OAAO;QACLC,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE,OAAO;QAChBC,YAAY,EAAEC,MAAM,CAACC,OAAO,CAACN,OAAO,CAAC,CAACO,MAAM,CAC1C,CAACC,QAAQ,EAAE,CAACC,QAAQ,EAAEC,MAAM,CAAC;YAAK,OAAC;gBACjC,GAAGF,QAAQ;gBACX,CAACC,QAAQ,CAAC,EAAE;oBACV,+CAA+C;oBAC/CC,MAAM,EAAEC,KAAI,QAAA,CAACC,IAAI,CAAC,SAAS,EAAEX,SAAS,CAACQ,QAAQ,CAAC,CAAE;oBAClD,mEAAmE;oBACnEI,MAAM,EAAEH,MAAM,CAACG,MAAM,CAClBC,GAAG,CAAC,CAACC,KAAK;4BACT,oEAAoE;wBACpEA,GAAgB;wBAAhBA,OAAAA,CAAAA,GAAgB,GAAhBA,KAAK,CAACC,UAAU,SAAK,GAArBD,KAAAA,CAAqB,GAArBA,GAAgB,CAAED,GAAG,CAAC,CAACG,IAAI,GAAK,CAAC;gCAC/BN,IAAI,EAAEA,KAAI,QAAA,CAACC,IAAI,CAAC,QAAQ,EAAEK,IAAI,CAAC;gCAC/BC,GAAG,EAAEH,KAAK,CAACI,IAAI;6BAChB,CAAC;wBAAA,CAAC,CAAA;qBAAA,CACJ,CACAC,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,EAAE;iBACV;aACF,CAAC;SAAA,EACF,EAAE,CACH;KACF,CAAC;CACH"}