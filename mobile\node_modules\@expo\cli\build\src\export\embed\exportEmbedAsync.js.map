{"version": 3, "sources": ["../../../../src/export/embed/exportEmbedAsync.ts"], "sourcesContent": ["import { loadMetroConfigAsync } from '../../start/server/metro/instantiateMetro';\nimport { importCliBuildBundleWithConfigFromProject } from '../../start/server/metro/resolveFromProject';\nimport { setNodeEnv } from '../../utils/nodeEnv';\nimport { Options } from './resolveOptions';\n\nexport async function exportEmbedAsync(projectRoot: string, options: Options) {\n  setNodeEnv(options.dev ? 'development' : 'production');\n  require('@expo/env').load(projectRoot);\n\n  const { config } = await loadMetroConfigAsync(projectRoot, {\n    maxWorkers: options.maxWorkers,\n    resetCache: options.resetCache,\n    config: options.config,\n  });\n\n  const buildBundleWithConfig = importCliBuildBundleWithConfigFromProject(projectRoot);\n\n  // Import the internal `buildBundleWithConfig()` function from `react-native` for the purpose\n  // of exporting with `@expo/metro-config` and other defaults like a resolved project entry.\n  await buildBundleWithConfig(options, config);\n}\n"], "names": ["exportEmbedAsync", "projectRoot", "options", "setNodeEnv", "dev", "require", "load", "config", "loadMetroConfigAsync", "maxWorkers", "resetCache", "buildBundleWithConfig", "importCliBuildBundleWithConfigFromProject"], "mappings": "AAAA;;;;QAKsBA,gBAAgB,GAAhBA,gBAAgB;AALD,IAAA,iBAA2C,WAA3C,2CAA2C,CAAA;AACtB,IAAA,mBAA6C,WAA7C,6CAA6C,CAAA;AAC5E,IAAA,QAAqB,WAArB,qBAAqB,CAAA;AAGzC,eAAeA,gBAAgB,CAACC,WAAmB,EAAEC,OAAgB,EAAE;IAC5EC,CAAAA,GAAAA,QAAU,AAA4C,CAAA,WAA5C,CAACD,OAAO,CAACE,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,CAAC;IACvDC,OAAO,CAAC,WAAW,CAAC,CAACC,IAAI,CAACL,WAAW,CAAC,CAAC;IAEvC,MAAM,EAAEM,MAAM,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,iBAAoB,AAI3C,CAAA,qBAJ2C,CAACP,WAAW,EAAE;QACzDQ,UAAU,EAAEP,OAAO,CAACO,UAAU;QAC9BC,UAAU,EAAER,OAAO,CAACQ,UAAU;QAC9BH,MAAM,EAAEL,OAAO,CAACK,MAAM;KACvB,CAAC,AAAC;IAEH,MAAMI,qBAAqB,GAAGC,CAAAA,GAAAA,mBAAyC,AAAa,CAAA,0CAAb,CAACX,WAAW,CAAC,AAAC;IAErF,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAMU,qBAAqB,CAACT,OAAO,EAAEK,MAAM,CAAC,CAAC;CAC9C"}