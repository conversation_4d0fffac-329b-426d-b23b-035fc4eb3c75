{"version": 3, "sources": ["../../../../src/export/embed/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { Command } from '../../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../../utils/args';\n\nexport const expoExportEmbed: Command = async (argv) => {\n  const rawArgsMap: arg.Spec = {\n    // Types\n    '--entry-file': String,\n    '--platform': String,\n    '--transformer': String,\n    '--bundle-output': String,\n    '--bundle-encoding': String,\n    '--max-workers': Number,\n    '--sourcemap-output': String,\n    '--sourcemap-sources-root': String,\n    '--assets-dest': String,\n    '--asset-catalog-dest': String,\n    '--unstable-transform-profile': String,\n    '--config': String,\n\n    // This is here for compatibility with the `npx react-native bundle` command.\n    // devs should use `DEBUG=expo:*` instead.\n    '--verbose': Boolean,\n    '--help': <PERSON><PERSON><PERSON>,\n    // Aliases\n    '-h': '--help',\n    '-v': '--verbose',\n  };\n  const args = assertWithOptionsArgs(rawArgsMap, {\n    argv,\n    permissive: true,\n  });\n\n  if (args['--help']) {\n    printHelp(\n      `(Internal) Export the JavaScript bundle during a native build script for embedding in a native binary`,\n      chalk`npx expo export:embed {dim <dir>}`,\n      [\n        chalk`<dir>                                  Directory of the Expo project. {dim Default: Current working directory}`,\n        `--entry-file <path>                    Path to the root JS file, either absolute or relative to JS root`,\n        `--platform <string>                    Either \"ios\" or \"android\" (default: \"ios\")`,\n        `--transformer <string>                 Specify a custom transformer to be used`,\n        `--dev [boolean]                        If false, warnings are disabled and the bundle is minified (default: true)`,\n        `--minify [boolean]                     Allows overriding whether bundle is minified. This defaults to false if dev is true, and true if dev is false. Disabling minification can be useful for speeding up production builds for testing purposes.`,\n        `--bundle-output <string>               File name where to store the resulting bundle, ex. /tmp/groups.bundle`,\n        `--bundle-encoding <string>             Encoding the bundle should be written in (https://nodejs.org/api/buffer.html#buffer_buffer). (default: \"utf8\")`,\n        `--max-workers <number>                 Specifies the maximum number of workers the worker-pool will spawn for transforming files. This defaults to the number of the cores available on your machine.`,\n        `--sourcemap-output <string>            File name where to store the sourcemap file for resulting bundle, ex. /tmp/groups.map`,\n        `--sourcemap-sources-root <string>      Path to make sourcemap's sources entries relative to, ex. /root/dir`,\n        `--sourcemap-use-absolute-path          Report SourceMapURL using its full path`,\n        `--assets-dest <string>                 Directory name where to store assets referenced in the bundle`,\n        `--asset-catalog-dest <string>          Directory to create an iOS Asset Catalog for images`,\n        `--unstable-transform-profile <string>  Experimental, transform JS for a specific JS engine. Currently supported: hermes, hermes-canary, default`,\n        `--reset-cache                          Removes cached files`,\n        `-v, --verbose                          Enables debug logging`,\n\n        `--config <string>                      Path to the CLI configuration file`,\n        `--generate-static-view-configs         Generate static view configs for Fabric components. If there are no Fabric components in the bundle or Fabric is disabled, this is just no-op.`,\n        // This is seemingly unused.\n        `--read-global-cache                    Try to fetch transformed JS code from the global cache, if configured.`,\n\n        `-h, --help                             Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const [\n    { exportEmbedAsync },\n    { resolveOptions },\n    { logCmdError },\n    { resolveCustomBooleanArgsAsync },\n  ] = await Promise.all([\n    import('./exportEmbedAsync'),\n    import('./resolveOptions'),\n    import('../../utils/errors'),\n    import('../../utils/resolveArgs'),\n  ]);\n\n  return (async () => {\n    const parsed = await resolveCustomBooleanArgsAsync(argv ?? [], rawArgsMap, {\n      '--dev': Boolean,\n      '--minify': Boolean,\n      '--sourcemap-use-absolute-path': Boolean,\n      '--reset-cache': Boolean,\n      '--read-global-cache': Boolean,\n      '--generate-static-view-configs': Boolean,\n    });\n    return exportEmbedAsync(path.resolve(parsed.projectRoot), resolveOptions(args, parsed));\n  })().catch(logCmdError);\n};\n"], "names": ["expoExportEmbed", "argv", "rawArgsMap", "String", "Number", "Boolean", "args", "assertWithOptionsArgs", "permissive", "printHelp", "chalk", "join", "exportEmbedAsync", "resolveOptions", "logCmdError", "resolveCustomBooleanArgsAsync", "Promise", "all", "parsed", "path", "resolve", "projectRoot", "catch"], "mappings": "AAAA;;;;;;AAEkB,IAAA,MAAO,kCAAP,OAAO,EAAA;AACR,IAAA,KAAM,kCAAN,MAAM,EAAA;AAG0B,IAAA,KAAkB,WAAlB,kBAAkB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5D,MAAMA,eAAe,GAAY,OAAOC,IAAI,GAAK;IACtD,MAAMC,UAAU,GAAa;QAC3B,QAAQ;QACR,cAAc,EAAEC,MAAM;QACtB,YAAY,EAAEA,MAAM;QACpB,eAAe,EAAEA,MAAM;QACvB,iBAAiB,EAAEA,MAAM;QACzB,mBAAmB,EAAEA,MAAM;QAC3B,eAAe,EAAEC,MAAM;QACvB,oBAAoB,EAAED,MAAM;QAC5B,0BAA0B,EAAEA,MAAM;QAClC,eAAe,EAAEA,MAAM;QACvB,sBAAsB,EAAEA,MAAM;QAC9B,8BAA8B,EAAEA,MAAM;QACtC,UAAU,EAAEA,MAAM;QAElB,6EAA6E;QAC7E,0CAA0C;QAC1C,WAAW,EAAEE,OAAO;QACpB,QAAQ,EAAEA,OAAO;QACjB,UAAU;QACV,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,WAAW;KAClB,AAAC;IACF,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,KAAqB,AAGhC,CAAA,sBAHgC,CAACL,UAAU,EAAE;QAC7CD,IAAI;QACJO,UAAU,EAAE,IAAI;KACjB,CAAC,AAAC;IAEH,IAAIF,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBG,CAAAA,GAAAA,KAAS,AA6BR,CAAA,UA7BQ,CACP,CAAC,qGAAqG,CAAC,EACvGC,MAAK,QAAA,CAAC,iCAAiC,CAAC,EACxC;YACEA,MAAK,QAAA,CAAC,8GAA8G,CAAC;YACrH,CAAC,uGAAuG,CAAC;YACzG,CAAC,iFAAiF,CAAC;YACnF,CAAC,8EAA8E,CAAC;YAChF,CAAC,iHAAiH,CAAC;YACnH,CAAC,kPAAkP,CAAC;YACpP,CAAC,4GAA4G,CAAC;YAC9G,CAAC,qJAAqJ,CAAC;YACvJ,CAAC,qMAAqM,CAAC;YACvM,CAAC,4HAA4H,CAAC;YAC9H,CAAC,0GAA0G,CAAC;YAC5G,CAAC,8EAA8E,CAAC;YAChF,CAAC,oGAAoG,CAAC;YACtG,CAAC,0FAA0F,CAAC;YAC5F,CAAC,+IAA+I,CAAC;YACjJ,CAAC,2DAA2D,CAAC;YAC7D,CAAC,4DAA4D,CAAC;YAE9D,CAAC,yEAAyE,CAAC;YAC3E,CAAC,qLAAqL,CAAC;YACvL,4BAA4B;YAC5B,CAAC,6GAA6G,CAAC;YAE/G,CAAC,iDAAiD,CAAC;SACpD,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;KACH;IAED,MAAM,CACJ,EAAEC,gBAAgB,CAAA,EAAE,EACpB,EAAEC,cAAc,CAAA,EAAE,EAClB,EAAEC,WAAW,CAAA,EAAE,EACf,EAAEC,6BAA6B,CAAA,EAAE,GAClC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC;QACpB;mDAAO,oBAAoB;UAAC;QAC5B;mDAAO,kBAAkB;UAAC;QAC1B;mDAAO,oBAAoB;UAAC;QAC5B;mDAAO,yBAAyB;UAAC;KAClC,CAAC,AAAC;IAEH,OAAO,CAAC,UAAY;QAClB,MAAMC,MAAM,GAAG,MAAMH,6BAA6B,CAACd,IAAI,WAAJA,IAAI,GAAI,EAAE,EAAEC,UAAU,EAAE;YACzE,OAAO,EAAEG,OAAO;YAChB,UAAU,EAAEA,OAAO;YACnB,+BAA+B,EAAEA,OAAO;YACxC,eAAe,EAAEA,OAAO;YACxB,qBAAqB,EAAEA,OAAO;YAC9B,gCAAgC,EAAEA,OAAO;SAC1C,CAAC,AAAC;QACH,OAAOO,gBAAgB,CAACO,KAAI,QAAA,CAACC,OAAO,CAACF,MAAM,CAACG,WAAW,CAAC,EAAER,cAAc,CAACP,IAAI,EAAEY,MAAM,CAAC,CAAC,CAAC;KACzF,CAAC,EAAE,CAACI,KAAK,CAACR,WAAW,CAAC,CAAC;CACzB,AAAC;QArFWd,eAAe,GAAfA,eAAe"}