{"version": 3, "sources": ["../../../../src/export/embed/resolveOptions.ts"], "sourcesContent": ["import arg from 'arg';\nimport path from 'path';\n\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { resolveCustomBooleanArgsAsync } from '../../utils/resolveArgs';\n\nexport interface Options {\n  assetsDest?: string;\n  assetCatalogDest?: string;\n  entryFile: string;\n  resetCache: boolean;\n  resetGlobalCache: boolean;\n  transformer?: string;\n  minify?: boolean;\n  config?: string;\n  platform: string;\n  dev: boolean;\n  bundleOutput: string;\n  bundleEncoding?: string;\n  maxWorkers?: number;\n  sourcemapOutput?: string;\n  sourcemapSourcesRoot?: string;\n  sourcemapUseAbsolutePath: boolean;\n  verbose: boolean;\n  unstableTransformProfile: string;\n  generateStaticViewConfigs: boolean;\n}\n\nfunction assertIsBoolean(val: any): asserts val is boolean {\n  if (typeof val !== 'boolean') {\n    throw new CommandError(`Expected boolean, got ${typeof val}`);\n  }\n}\n\nexport function resolveOptions(\n  args: arg.Result<arg.Spec>,\n  parsed: Awaited<ReturnType<typeof resolveCustomBooleanArgsAsync>>\n): Options {\n  const dev = parsed.args['--dev'] ?? true;\n  assertIsBoolean(dev);\n\n  const generateStaticViewConfigs = parsed.args['--generate-static-view-configs'] ?? true;\n  assertIsBoolean(generateStaticViewConfigs);\n\n  const minify = parsed.args['--minify'] ?? true;\n  assertIsBoolean(minify);\n\n  const entryFile = args['--entry-file'];\n  if (!entryFile) {\n    throw new CommandError(`Missing required argument: --entry-file`);\n  }\n  const bundleOutput = args['--bundle-output'];\n  if (!bundleOutput) {\n    throw new CommandError(`Missing required argument: --bundle-output`);\n  }\n  return {\n    entryFile,\n    assetCatalogDest: args['--asset-catalog-dest'],\n    platform: args['--platform'] ?? 'ios',\n    transformer: args['--transformer'],\n    // TODO: Support `--dev false`\n    //   dev: false,\n    bundleOutput,\n    bundleEncoding: args['--bundle-encoding'] ?? 'utf8',\n    maxWorkers: args['--max-workers'],\n    sourcemapOutput: args['--sourcemap-output'],\n    sourcemapSourcesRoot: args['--sourcemap-sources-root'],\n    sourcemapUseAbsolutePath: !!parsed.args['--sourcemap-use-absolute-path'],\n    assetsDest: args['--assets-dest'],\n    unstableTransformProfile: args['--unstable-transform-profile'] ?? 'default',\n    resetCache: !!parsed.args['--reset-cache'],\n    resetGlobalCache: false,\n    verbose: args['--verbose'] ?? env.EXPO_DEBUG,\n    config: args['--config'] ? path.resolve(args['--config']) : undefined,\n    dev,\n    generateStaticViewConfigs,\n    minify,\n  };\n}\n"], "names": ["resolveOptions", "assertIsBoolean", "val", "CommandError", "args", "parsed", "dev", "generateStaticViewConfigs", "minify", "entryFile", "bundleOutput", "assetCatalogDest", "platform", "transformer", "bundleEncoding", "maxWorkers", "sourcemapOutput", "sourcemapSourcesRoot", "sourcemapUseAbsolutePath", "assetsDest", "unstableTransformProfile", "resetCache", "resetGlobalCache", "verbose", "env", "EXPO_DEBUG", "config", "path", "resolve", "undefined"], "mappings": "AAAA;;;;QAmCgBA,cAAc,GAAdA,cAAc;AAlCb,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEH,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AACR,IAAA,OAAoB,WAApB,oBAAoB,CAAA;;;;;;AAyBjD,SAASC,eAAe,CAACC,GAAQ,EAA0B;IACzD,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;QAC5B,MAAM,IAAIC,OAAY,aAAA,CAAC,CAAC,sBAAsB,EAAE,OAAOD,GAAG,CAAC,CAAC,CAAC,CAAC;KAC/D;CACF;AAEM,SAASF,cAAc,CAC5BI,IAA0B,EAC1BC,MAAiE,EACxD;QACGA,GAAoB;IAAhC,MAAMC,GAAG,GAAGD,CAAAA,GAAoB,GAApBA,MAAM,CAACD,IAAI,CAAC,OAAO,CAAC,YAApBC,GAAoB,GAAI,IAAI,AAAC;IACzCJ,eAAe,CAACK,GAAG,CAAC,CAAC;QAEaD,IAA6C;IAA/E,MAAME,yBAAyB,GAAGF,CAAAA,IAA6C,GAA7CA,MAAM,CAACD,IAAI,CAAC,gCAAgC,CAAC,YAA7CC,IAA6C,GAAI,IAAI,AAAC;IACxFJ,eAAe,CAACM,yBAAyB,CAAC,CAAC;QAE5BF,IAAuB;IAAtC,MAAMG,MAAM,GAAGH,CAAAA,IAAuB,GAAvBA,MAAM,CAACD,IAAI,CAAC,UAAU,CAAC,YAAvBC,IAAuB,GAAI,IAAI,AAAC;IAC/CJ,eAAe,CAACO,MAAM,CAAC,CAAC;IAExB,MAAMC,SAAS,GAAGL,IAAI,CAAC,cAAc,CAAC,AAAC;IACvC,IAAI,CAACK,SAAS,EAAE;QACd,MAAM,IAAIN,OAAY,aAAA,CAAC,CAAC,uCAAuC,CAAC,CAAC,CAAC;KACnE;IACD,MAAMO,YAAY,GAAGN,IAAI,CAAC,iBAAiB,CAAC,AAAC;IAC7C,IAAI,CAACM,YAAY,EAAE;QACjB,MAAM,IAAIP,OAAY,aAAA,CAAC,CAAC,0CAA0C,CAAC,CAAC,CAAC;KACtE;QAIWC,IAAkB,EAKZA,IAAyB,EAMfA,IAAoC,EAGrDA,IAAiB;IAjB5B,OAAO;QACLK,SAAS;QACTE,gBAAgB,EAAEP,IAAI,CAAC,sBAAsB,CAAC;QAC9CQ,QAAQ,EAAER,CAAAA,IAAkB,GAAlBA,IAAI,CAAC,YAAY,CAAC,YAAlBA,IAAkB,GAAI,KAAK;QACrCS,WAAW,EAAET,IAAI,CAAC,eAAe,CAAC;QAClC,8BAA8B;QAC9B,gBAAgB;QAChBM,YAAY;QACZI,cAAc,EAAEV,CAAAA,IAAyB,GAAzBA,IAAI,CAAC,mBAAmB,CAAC,YAAzBA,IAAyB,GAAI,MAAM;QACnDW,UAAU,EAAEX,IAAI,CAAC,eAAe,CAAC;QACjCY,eAAe,EAAEZ,IAAI,CAAC,oBAAoB,CAAC;QAC3Ca,oBAAoB,EAAEb,IAAI,CAAC,0BAA0B,CAAC;QACtDc,wBAAwB,EAAE,CAAC,CAACb,MAAM,CAACD,IAAI,CAAC,+BAA+B,CAAC;QACxEe,UAAU,EAAEf,IAAI,CAAC,eAAe,CAAC;QACjCgB,wBAAwB,EAAEhB,CAAAA,IAAoC,GAApCA,IAAI,CAAC,8BAA8B,CAAC,YAApCA,IAAoC,GAAI,SAAS;QAC3EiB,UAAU,EAAE,CAAC,CAAChB,MAAM,CAACD,IAAI,CAAC,eAAe,CAAC;QAC1CkB,gBAAgB,EAAE,KAAK;QACvBC,OAAO,EAAEnB,CAAAA,IAAiB,GAAjBA,IAAI,CAAC,WAAW,CAAC,YAAjBA,IAAiB,GAAIoB,IAAG,IAAA,CAACC,UAAU;QAC5CC,MAAM,EAAEtB,IAAI,CAAC,UAAU,CAAC,GAAGuB,KAAI,QAAA,CAACC,OAAO,CAACxB,IAAI,CAAC,UAAU,CAAC,CAAC,GAAGyB,SAAS;QACrEvB,GAAG;QACHC,yBAAyB;QACzBC,MAAM;KACP,CAAC;CACH"}