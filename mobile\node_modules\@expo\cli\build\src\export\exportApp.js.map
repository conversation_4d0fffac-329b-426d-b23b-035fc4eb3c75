{"version": 3, "sources": ["../../../src/export/exportApp.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { importCliSaveAssetsFromProject } from '../start/server/metro/resolveFromProject';\nimport { createTemplateHtmlFromExpoConfigAsync } from '../start/server/webTemplate';\nimport { copyAsync, ensureDirectoryAsync } from '../utils/dir';\nimport { env } from '../utils/env';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { createBundlesAsync } from './createBundles';\nimport { exportAssetsAsync, exportCssAssetsAsync } from './exportAssets';\nimport { unstable_exportStaticAsync } from './exportStaticAsync';\nimport { getVirtualFaviconAssetsAsync } from './favicon';\nimport { getPublicExpoManifestAsync } from './getPublicExpoManifest';\nimport { printBundleSizes } from './printBundleSizes';\nimport { Options } from './resolveOptions';\nimport {\n  writeAssetMapAsync,\n  writeBundlesAsync,\n  writeDebugHtmlAsync,\n  writeMetadataJsonAsync,\n  writeSourceMapsAsync,\n} from './writeContents';\n\n/**\n * The structure of the outputDir will be:\n *\n * ```\n * ├── assets\n * │   └── *\n * ├── bundles\n * │   ├── android-01ee6e3ab3e8c16a4d926c91808d5320.js\n * │   └── ios-ee8206cc754d3f7aa9123b7f909d94ea.js\n * └── metadata.json\n * ```\n */\nexport async function exportAppAsync(\n  projectRoot: string,\n  {\n    platforms,\n    outputDir,\n    clear,\n    dev,\n    dumpAssetmap,\n    dumpSourcemap,\n    minify,\n  }: Pick<\n    Options,\n    'dumpAssetmap' | 'dumpSourcemap' | 'dev' | 'clear' | 'outputDir' | 'platforms' | 'minify'\n  >\n): Promise<void> {\n  setNodeEnv(dev ? 'development' : 'production');\n  require('@expo/env').load(projectRoot);\n\n  const exp = await getPublicExpoManifestAsync(projectRoot);\n\n  const useWebSSG = exp.web?.output === 'static';\n\n  const publicPath = path.resolve(projectRoot, env.EXPO_PUBLIC_FOLDER);\n\n  const outputPath = path.resolve(projectRoot, outputDir);\n  const staticFolder = outputPath;\n  const assetsPath = path.join(staticFolder, 'assets');\n  const bundlesPath = path.join(staticFolder, 'bundles');\n\n  await Promise.all([assetsPath, bundlesPath].map(ensureDirectoryAsync));\n\n  await copyPublicFolderAsync(publicPath, staticFolder);\n\n  // Run metro bundler and create the JS bundles/source maps.\n  const bundles = await createBundlesAsync(\n    projectRoot,\n    { resetCache: !!clear },\n    {\n      platforms,\n      minify,\n      // TODO: Breaks asset exports\n      // platforms: useWebSSG ? platforms.filter((platform) => platform !== 'web') : platforms,\n      dev,\n      // TODO: Disable source map generation if we aren't outputting them.\n    }\n  );\n\n  const bundleEntries = Object.entries(bundles);\n  if (bundleEntries.length) {\n    // Log bundle size info to the user\n    printBundleSizes(\n      Object.fromEntries(\n        bundleEntries.map(([key, value]) => {\n          if (!dumpSourcemap) {\n            return [\n              key,\n              {\n                ...value,\n                // Remove source maps from the bundles if they aren't going to be written.\n                map: undefined,\n              },\n            ];\n          }\n\n          return [key, value];\n        })\n      )\n    );\n  }\n\n  // Write the JS bundles to disk, and get the bundle file names (this could change with async chunk loading support).\n  const { hashes, fileNames } = await writeBundlesAsync({ bundles, outputDir: bundlesPath });\n\n  Log.log('Finished saving JS Bundles');\n\n  if (platforms.includes('web')) {\n    if (useWebSSG) {\n      await unstable_exportStaticAsync(projectRoot, {\n        outputDir: outputPath,\n        // TODO: Expose\n        minify,\n      });\n      Log.log('Finished saving static files');\n    } else {\n      const cssLinks = await exportCssAssetsAsync({\n        outputDir,\n        bundles,\n      });\n      let html = await createTemplateHtmlFromExpoConfigAsync(projectRoot, {\n        scripts: [`/bundles/${fileNames.web}`],\n        cssLinks,\n      });\n      // Add the favicon assets to the HTML.\n      const modifyHtml = await getVirtualFaviconAssetsAsync(projectRoot, outputDir);\n      if (modifyHtml) {\n        html = modifyHtml(html);\n      }\n      // Generate SPA-styled HTML file.\n      // If web exists, then write the template HTML file.\n      await fs.promises.writeFile(path.join(staticFolder, 'index.html'), html);\n    }\n\n    // Save assets like a typical bundler, preserving the file paths on web.\n    const saveAssets = importCliSaveAssetsFromProject(projectRoot);\n    await Promise.all(\n      Object.entries(bundles).map(([platform, bundle]) => {\n        return saveAssets(bundle.assets, platform, staticFolder, undefined);\n      })\n    );\n  }\n\n  const { assets } = await exportAssetsAsync(projectRoot, {\n    exp,\n    outputDir: staticFolder,\n    bundles,\n  });\n\n  if (dumpAssetmap) {\n    Log.log('Dumping asset map');\n    await writeAssetMapAsync({ outputDir: staticFolder, assets });\n  }\n\n  // build source maps\n  if (dumpSourcemap) {\n    Log.log('Dumping source maps');\n    await writeSourceMapsAsync({\n      bundles,\n      hashes,\n      outputDir: bundlesPath,\n      fileNames,\n    });\n\n    Log.log('Preparing additional debugging files');\n    // If we output source maps, then add a debug HTML file which the user can open in\n    // the web browser to inspect the output like web.\n    await writeDebugHtmlAsync({\n      outputDir: staticFolder,\n      fileNames,\n    });\n  }\n\n  // Generate a `metadata.json` and the export is complete.\n  await writeMetadataJsonAsync({ outputDir: staticFolder, bundles, fileNames });\n}\n\n/**\n * Copy the contents of the public folder into the output folder.\n * This enables users to add static files like `favicon.ico` or `serve.json`.\n *\n * The contents of this folder are completely universal since they refer to\n * static network requests which fall outside the scope of React Native's magic\n * platform resolution patterns.\n */\nasync function copyPublicFolderAsync(publicFolder: string, outputFolder: string) {\n  if (fs.existsSync(publicFolder)) {\n    await copyAsync(publicFolder, outputFolder);\n  }\n}\n"], "names": ["exportAppAsync", "Log", "projectRoot", "platforms", "outputDir", "clear", "dev", "dumpAssetmap", "dumpSourcemap", "minify", "exp", "setNodeEnv", "require", "load", "getPublicExpoManifestAsync", "useWebSSG", "web", "output", "publicPath", "path", "resolve", "env", "EXPO_PUBLIC_FOLDER", "outputPath", "staticFolder", "assetsPath", "join", "bundlesPath", "Promise", "all", "map", "ensureDirectoryAsync", "copyPublicFolderAsync", "bundles", "createBundlesAsync", "resetCache", "bundleEntries", "Object", "entries", "length", "printBundleSizes", "fromEntries", "key", "value", "undefined", "hashes", "fileNames", "writeBundlesAsync", "log", "includes", "unstable_exportStaticAsync", "cssLinks", "exportCssAssetsAsync", "html", "createTemplateHtmlFromExpoConfigAsync", "scripts", "modifyHtml", "getVirtualFaviconAssetsAsync", "fs", "promises", "writeFile", "saveAssets", "importCliSaveAssetsFromProject", "platform", "bundle", "assets", "exportAssetsAsync", "writeAssetMapAsync", "writeSourceMapsAsync", "writeDebugHtmlAsync", "writeMetadataJsonAsync", "publicFolder", "outputFolder", "existsSync", "copyAsync"], "mappings": "AAAA;;;;QAoCsBA,cAAc,GAAdA,cAAc;AApCrB,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEXC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACgC,IAAA,mBAA0C,WAA1C,0CAA0C,CAAA;AACnC,IAAA,YAA6B,WAA7B,6BAA6B,CAAA;AACnC,IAAA,IAAc,WAAd,cAAc,CAAA;AAC1C,IAAA,IAAc,WAAd,cAAc,CAAA;AACP,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACV,IAAA,cAAiB,WAAjB,iBAAiB,CAAA;AACI,IAAA,aAAgB,WAAhB,gBAAgB,CAAA;AAC7B,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AACnB,IAAA,QAAW,WAAX,WAAW,CAAA;AACb,IAAA,sBAAyB,WAAzB,yBAAyB,CAAA;AACnC,IAAA,iBAAoB,WAApB,oBAAoB,CAAA;AAQ9C,IAAA,cAAiB,WAAjB,iBAAiB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcjB,eAAeD,cAAc,CAClCE,WAAmB,EACnB,EACEC,SAAS,CAAA,EACTC,SAAS,CAAA,EACTC,KAAK,CAAA,EACLC,GAAG,CAAA,EACHC,YAAY,CAAA,EACZC,aAAa,CAAA,EACbC,MAAM,CAAA,EAIP,EACc;QAMGC,GAAO;IALzBC,CAAAA,GAAAA,QAAU,AAAoC,CAAA,WAApC,CAACL,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,CAAC;IAC/CM,OAAO,CAAC,WAAW,CAAC,CAACC,IAAI,CAACX,WAAW,CAAC,CAAC;IAEvC,MAAMQ,GAAG,GAAG,MAAMI,CAAAA,GAAAA,sBAA0B,AAAa,CAAA,2BAAb,CAACZ,WAAW,CAAC,AAAC;IAE1D,MAAMa,SAAS,GAAGL,CAAAA,CAAAA,GAAO,GAAPA,GAAG,CAACM,GAAG,SAAQ,GAAfN,KAAAA,CAAe,GAAfA,GAAO,CAAEO,MAAM,CAAA,KAAK,QAAQ,AAAC;IAE/C,MAAMC,UAAU,GAAGC,KAAI,QAAA,CAACC,OAAO,CAAClB,WAAW,EAAEmB,IAAG,IAAA,CAACC,kBAAkB,CAAC,AAAC;IAErE,MAAMC,UAAU,GAAGJ,KAAI,QAAA,CAACC,OAAO,CAAClB,WAAW,EAAEE,SAAS,CAAC,AAAC;IACxD,MAAMoB,YAAY,GAAGD,UAAU,AAAC;IAChC,MAAME,UAAU,GAAGN,KAAI,QAAA,CAACO,IAAI,CAACF,YAAY,EAAE,QAAQ,CAAC,AAAC;IACrD,MAAMG,WAAW,GAAGR,KAAI,QAAA,CAACO,IAAI,CAACF,YAAY,EAAE,SAAS,CAAC,AAAC;IAEvD,MAAMI,OAAO,CAACC,GAAG,CAAC;QAACJ,UAAU;QAAEE,WAAW;KAAC,CAACG,GAAG,CAACC,IAAoB,qBAAA,CAAC,CAAC,CAAC;IAEvE,MAAMC,qBAAqB,CAACd,UAAU,EAAEM,YAAY,CAAC,CAAC;IAEtD,2DAA2D;IAC3D,MAAMS,OAAO,GAAG,MAAMC,CAAAA,GAAAA,cAAkB,AAWvC,CAAA,mBAXuC,CACtChC,WAAW,EACX;QAAEiC,UAAU,EAAE,CAAC,CAAC9B,KAAK;KAAE,EACvB;QACEF,SAAS;QACTM,MAAM;QACN,6BAA6B;QAC7B,yFAAyF;QACzFH,GAAG;KAEJ,CACF,AAAC;IAEF,MAAM8B,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACL,OAAO,CAAC,AAAC;IAC9C,IAAIG,aAAa,CAACG,MAAM,EAAE;QACxB,mCAAmC;QACnCC,CAAAA,GAAAA,iBAAgB,AAiBf,CAAA,iBAjBe,CACdH,MAAM,CAACI,WAAW,CAChBL,aAAa,CAACN,GAAG,CAAC,CAAC,CAACY,GAAG,EAAEC,KAAK,CAAC,GAAK;YAClC,IAAI,CAACnC,aAAa,EAAE;gBAClB,OAAO;oBACLkC,GAAG;oBACH;wBACE,GAAGC,KAAK;wBACR,0EAA0E;wBAC1Eb,GAAG,EAAEc,SAAS;qBACf;iBACF,CAAC;aACH;YAED,OAAO;gBAACF,GAAG;gBAAEC,KAAK;aAAC,CAAC;SACrB,CAAC,CACH,CACF,CAAC;KACH;IAED,oHAAoH;IACpH,MAAM,EAAEE,MAAM,CAAA,EAAEC,SAAS,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,cAAiB,AAAqC,CAAA,kBAArC,CAAC;QAAEd,OAAO;QAAE7B,SAAS,EAAEuB,WAAW;KAAE,CAAC,AAAC;IAE3F1B,GAAG,CAAC+C,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAEtC,IAAI7C,SAAS,CAAC8C,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7B,IAAIlC,SAAS,EAAE;YACb,MAAMmC,CAAAA,GAAAA,kBAA0B,AAI9B,CAAA,2BAJ8B,CAAChD,WAAW,EAAE;gBAC5CE,SAAS,EAAEmB,UAAU;gBACrB,eAAe;gBACfd,MAAM;aACP,CAAC,CAAC;YACHR,GAAG,CAAC+C,GAAG,CAAC,8BAA8B,CAAC,CAAC;SACzC,MAAM;YACL,MAAMG,QAAQ,GAAG,MAAMC,CAAAA,GAAAA,aAAoB,AAGzC,CAAA,qBAHyC,CAAC;gBAC1ChD,SAAS;gBACT6B,OAAO;aACR,CAAC,AAAC;YACH,IAAIoB,IAAI,GAAG,MAAMC,CAAAA,GAAAA,YAAqC,AAGpD,CAAA,sCAHoD,CAACpD,WAAW,EAAE;gBAClEqD,OAAO,EAAE;oBAAC,CAAC,SAAS,EAAET,SAAS,CAAC9B,GAAG,CAAC,CAAC;iBAAC;gBACtCmC,QAAQ;aACT,CAAC,AAAC;YACH,sCAAsC;YACtC,MAAMK,UAAU,GAAG,MAAMC,CAAAA,GAAAA,QAA4B,AAAwB,CAAA,6BAAxB,CAACvD,WAAW,EAAEE,SAAS,CAAC,AAAC;YAC9E,IAAIoD,UAAU,EAAE;gBACdH,IAAI,GAAGG,UAAU,CAACH,IAAI,CAAC,CAAC;aACzB;YACD,iCAAiC;YACjC,oDAAoD;YACpD,MAAMK,GAAE,QAAA,CAACC,QAAQ,CAACC,SAAS,CAACzC,KAAI,QAAA,CAACO,IAAI,CAACF,YAAY,EAAE,YAAY,CAAC,EAAE6B,IAAI,CAAC,CAAC;SAC1E;QAED,wEAAwE;QACxE,MAAMQ,UAAU,GAAGC,CAAAA,GAAAA,mBAA8B,AAAa,CAAA,+BAAb,CAAC5D,WAAW,CAAC,AAAC;QAC/D,MAAM0B,OAAO,CAACC,GAAG,CACfQ,MAAM,CAACC,OAAO,CAACL,OAAO,CAAC,CAACH,GAAG,CAAC,CAAC,CAACiC,QAAQ,EAAEC,MAAM,CAAC,GAAK;YAClD,OAAOH,UAAU,CAACG,MAAM,CAACC,MAAM,EAAEF,QAAQ,EAAEvC,YAAY,EAAEoB,SAAS,CAAC,CAAC;SACrE,CAAC,CACH,CAAC;KACH;IAED,MAAM,EAAEqB,MAAM,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,aAAiB,AAIxC,CAAA,kBAJwC,CAAChE,WAAW,EAAE;QACtDQ,GAAG;QACHN,SAAS,EAAEoB,YAAY;QACvBS,OAAO;KACR,CAAC,AAAC;IAEH,IAAI1B,YAAY,EAAE;QAChBN,GAAG,CAAC+C,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAC7B,MAAMmB,CAAAA,GAAAA,cAAkB,AAAqC,CAAA,mBAArC,CAAC;YAAE/D,SAAS,EAAEoB,YAAY;YAAEyC,MAAM;SAAE,CAAC,CAAC;KAC/D;IAED,oBAAoB;IACpB,IAAIzD,aAAa,EAAE;QACjBP,GAAG,CAAC+C,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAC/B,MAAMoB,CAAAA,GAAAA,cAAoB,AAKxB,CAAA,qBALwB,CAAC;YACzBnC,OAAO;YACPY,MAAM;YACNzC,SAAS,EAAEuB,WAAW;YACtBmB,SAAS;SACV,CAAC,CAAC;QAEH7C,GAAG,CAAC+C,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAChD,kFAAkF;QAClF,kDAAkD;QAClD,MAAMqB,CAAAA,GAAAA,cAAmB,AAGvB,CAAA,oBAHuB,CAAC;YACxBjE,SAAS,EAAEoB,YAAY;YACvBsB,SAAS;SACV,CAAC,CAAC;KACJ;IAED,yDAAyD;IACzD,MAAMwB,CAAAA,GAAAA,cAAsB,AAAiD,CAAA,uBAAjD,CAAC;QAAElE,SAAS,EAAEoB,YAAY;QAAES,OAAO;QAAEa,SAAS;KAAE,CAAC,CAAC;CAC/E;AAED;;;;;;;GAOG,CACH,eAAed,qBAAqB,CAACuC,YAAoB,EAAEC,YAAoB,EAAE;IAC/E,IAAId,GAAE,QAAA,CAACe,UAAU,CAACF,YAAY,CAAC,EAAE;QAC/B,MAAMG,CAAAA,GAAAA,IAAS,AAA4B,CAAA,UAA5B,CAACH,YAAY,EAAEC,YAAY,CAAC,CAAC;KAC7C;CACF"}