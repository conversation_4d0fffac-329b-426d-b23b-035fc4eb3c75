{"version": 3, "sources": ["../../../src/export/exportAssets.ts"], "sourcesContent": ["import { ExpoAppManifest } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\nimport fs from 'fs';\nimport minimatch from 'minimatch';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { resolveGoogleServicesFile } from '../start/server/middleware/resolveAssets';\nimport { uniqBy } from '../utils/array';\nimport { BundleOutput } from './fork-bundleAsync';\nimport { Asset, saveAssetsAsync } from './saveAssets';\n\nconst debug = require('debug')('expo:export:exportAssets') as typeof console.log;\n\n/**\n * Resolves the assetBundlePatterns from the manifest and returns a list of assets to bundle.\n *\n * @modifies {exp}\n */\nexport async function resolveAssetBundlePatternsAsync(\n  projectRoot: string,\n  exp: Pick<ExpoAppManifest, 'bundledAssets' | 'assetBundlePatterns'>,\n  assets: Asset[]\n) {\n  if (!exp.assetBundlePatterns?.length || !assets.length) {\n    delete exp.assetBundlePatterns;\n    return exp;\n  }\n  // Convert asset patterns to a list of asset strings that match them.\n  // Assets strings are formatted as `asset_<hash>.<type>` and represent\n  // the name that the file will have in the app bundle. The `asset_` prefix is\n  // needed because android doesn't support assets that start with numbers.\n\n  const fullPatterns: string[] = exp.assetBundlePatterns.map((p: string) =>\n    path.join(projectRoot, p)\n  );\n\n  logPatterns(fullPatterns);\n\n  const allBundledAssets = assets\n    .map((asset) => {\n      const shouldBundle = shouldBundleAsset(asset, fullPatterns);\n      if (shouldBundle) {\n        debug(`${shouldBundle ? 'Include' : 'Exclude'} asset ${asset.files?.[0]}`);\n        return asset.fileHashes.map(\n          (hash) => 'asset_' + hash + ('type' in asset && asset.type ? '.' + asset.type : '')\n        );\n      }\n      return [];\n    })\n    .flat();\n\n  // The assets returned by the RN packager has duplicates so make sure we\n  // only bundle each once.\n  exp.bundledAssets = [...new Set(allBundledAssets)];\n  delete exp.assetBundlePatterns;\n\n  return exp;\n}\n\nfunction logPatterns(patterns: string[]) {\n  // Only log the patterns in debug mode, if they aren't already defined in the app.json, then all files will be targeted.\n  Log.log('\\nProcessing asset bundle patterns:');\n  patterns.forEach((p) => Log.log('- ' + p));\n}\n\nfunction shouldBundleAsset(asset: Asset, patterns: string[]) {\n  const file = asset.files?.[0];\n  return !!(\n    '__packager_asset' in asset &&\n    asset.__packager_asset &&\n    file &&\n    patterns.some((pattern) => minimatch(file, pattern))\n  );\n}\n\nexport async function exportAssetsAsync(\n  projectRoot: string,\n  {\n    exp,\n    outputDir,\n    bundles,\n  }: {\n    exp: ExpoAppManifest;\n    bundles: Partial<Record<ModPlatform, BundleOutput>>;\n    outputDir: string;\n  }\n) {\n  const assets: Asset[] = uniqBy(\n    Object.values(bundles).flatMap((bundle) => bundle!.assets),\n    (asset) => asset.hash\n  );\n\n  if (assets[0]?.fileHashes) {\n    Log.log('Saving assets');\n    await saveAssetsAsync(projectRoot, { assets, outputDir });\n  }\n\n  // Add google services file if it exists\n  await resolveGoogleServicesFile(projectRoot, exp);\n\n  // Updates the manifest to reflect additional asset bundling + configs\n  await resolveAssetBundlePatternsAsync(projectRoot, exp, assets);\n\n  return { exp, assets };\n}\n\nexport async function exportCssAssetsAsync({\n  outputDir,\n  bundles,\n}: {\n  bundles: Partial<Record<ModPlatform, BundleOutput>>;\n  outputDir: string;\n}) {\n  const assets = uniqBy(\n    Object.values(bundles).flatMap((bundle) => bundle!.css),\n    (asset) => asset.filename\n  );\n\n  const cssDirectory = assets[0]?.filename;\n  if (!cssDirectory) return [];\n\n  await fs.promises.mkdir(path.join(outputDir, path.dirname(cssDirectory)), { recursive: true });\n\n  await Promise.all(\n    assets.map((v) => fs.promises.writeFile(path.join(outputDir, v.filename), v.source))\n  );\n\n  return assets.map((v) => '/' + v.filename);\n}\n"], "names": ["resolveAssetBundlePatternsAsync", "exportAssetsAsync", "exportCssAssetsAsync", "Log", "debug", "require", "projectRoot", "exp", "assets", "assetBundlePatterns", "length", "fullPatterns", "map", "p", "path", "join", "logPatterns", "allBundledAssets", "asset", "shouldBundle", "shouldBundleAsset", "files", "fileHashes", "hash", "type", "flat", "bundledAssets", "Set", "patterns", "log", "for<PERSON>ach", "file", "__packager_asset", "some", "pattern", "minimatch", "outputDir", "bundles", "uniqBy", "Object", "values", "flatMap", "bundle", "saveAssetsAsync", "resolveGoogleServicesFile", "css", "filename", "cssDirectory", "fs", "promises", "mkdir", "dirname", "recursive", "Promise", "all", "v", "writeFile", "source"], "mappings": "AAAA;;;;QAmBsBA,+BAA+B,GAA/BA,+BAA+B;QAyD/BC,iBAAiB,GAAjBA,iBAAiB;QA+BjBC,oBAAoB,GAApBA,oBAAoB;AAzG3B,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACG,IAAA,UAAW,kCAAX,WAAW,EAAA;AAChB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEXC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AAC2B,IAAA,cAA0C,WAA1C,0CAA0C,CAAA;AAC7D,IAAA,MAAgB,WAAhB,gBAAgB,CAAA;AAEA,IAAA,WAAc,WAAd,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC,AAAsB,AAAC;AAO1E,eAAeL,+BAA+B,CACnDM,WAAmB,EACnBC,GAAmE,EACnEC,MAAe,EACf;QACKD,IAAuB;IAA5B,IAAI,CAACA,CAAAA,CAAAA,IAAuB,GAAvBA,GAAG,CAACE,mBAAmB,SAAQ,GAA/BF,KAAAA,CAA+B,GAA/BA,IAAuB,CAAEG,MAAM,CAAA,IAAI,CAACF,MAAM,CAACE,MAAM,EAAE;QACtD,OAAOH,GAAG,CAACE,mBAAmB,CAAC;QAC/B,OAAOF,GAAG,CAAC;KACZ;IACD,qEAAqE;IACrE,sEAAsE;IACtE,6EAA6E;IAC7E,yEAAyE;IAEzE,MAAMI,YAAY,GAAaJ,GAAG,CAACE,mBAAmB,CAACG,GAAG,CAAC,CAACC,CAAS,GACnEC,KAAI,QAAA,CAACC,IAAI,CAACT,WAAW,EAAEO,CAAC,CAAC;IAAA,CAC1B,AAAC;IAEFG,WAAW,CAACL,YAAY,CAAC,CAAC;IAE1B,MAAMM,gBAAgB,GAAGT,MAAM,CAC5BI,GAAG,CAAC,CAACM,KAAK,GAAK;QACd,MAAMC,YAAY,GAAGC,iBAAiB,CAACF,KAAK,EAAEP,YAAY,CAAC,AAAC;QAC5D,IAAIQ,YAAY,EAAE;gBACuCD,GAAW;YAAlEd,KAAK,CAAC,CAAC,EAAEe,YAAY,GAAG,SAAS,GAAG,SAAS,CAAC,OAAO,EAAED,CAAAA,GAAW,GAAXA,KAAK,CAACG,KAAK,SAAK,GAAhBH,KAAAA,CAAgB,GAAhBA,GAAW,AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,OAAOA,KAAK,CAACI,UAAU,CAACV,GAAG,CACzB,CAACW,IAAI,GAAK,QAAQ,GAAGA,IAAI,GAAG,CAAC,MAAM,IAAIL,KAAK,IAAIA,KAAK,CAACM,IAAI,GAAG,GAAG,GAAGN,KAAK,CAACM,IAAI,GAAG,EAAE,CAAC;YAAA,CACpF,CAAC;SACH;QACD,OAAO,EAAE,CAAC;KACX,CAAC,CACDC,IAAI,EAAE,AAAC;IAEV,wEAAwE;IACxE,yBAAyB;IACzBlB,GAAG,CAACmB,aAAa,GAAG;WAAI,IAAIC,GAAG,CAACV,gBAAgB,CAAC;KAAC,CAAC;IACnD,OAAOV,GAAG,CAACE,mBAAmB,CAAC;IAE/B,OAAOF,GAAG,CAAC;CACZ;AAED,SAASS,WAAW,CAACY,QAAkB,EAAE;IACvC,wHAAwH;IACxHzB,GAAG,CAAC0B,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAC/CD,QAAQ,CAACE,OAAO,CAAC,CAACjB,CAAC,GAAKV,GAAG,CAAC0B,GAAG,CAAC,IAAI,GAAGhB,CAAC,CAAC;IAAA,CAAC,CAAC;CAC5C;AAED,SAASO,iBAAiB,CAACF,KAAY,EAAEU,QAAkB,EAAE;QAC9CV,GAAW;IAAxB,MAAMa,IAAI,GAAGb,CAAAA,GAAW,GAAXA,KAAK,CAACG,KAAK,SAAK,GAAhBH,KAAAA,CAAgB,GAAhBA,GAAW,AAAE,CAAC,CAAC,CAAC,AAAC;IAC9B,OAAO,CAAC,CAAC,CACP,kBAAkB,IAAIA,KAAK,IAC3BA,KAAK,CAACc,gBAAgB,IACtBD,IAAI,IACJH,QAAQ,CAACK,IAAI,CAAC,CAACC,OAAO,GAAKC,CAAAA,GAAAA,UAAS,AAAe,CAAA,QAAf,CAACJ,IAAI,EAAEG,OAAO,CAAC;IAAA,CAAC,CACrD,CAAC;CACH;AAEM,eAAejC,iBAAiB,CACrCK,WAAmB,EACnB,EACEC,GAAG,CAAA,EACH6B,SAAS,CAAA,EACTC,OAAO,CAAA,EAKR,EACD;QAMI7B,GAAS;IALb,MAAMA,MAAM,GAAY8B,CAAAA,GAAAA,MAAM,AAG7B,CAAA,OAH6B,CAC5BC,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC,CAACI,OAAO,CAAC,CAACC,MAAM,GAAKA,MAAM,CAAElC,MAAM;IAAA,CAAC,EAC1D,CAACU,KAAK,GAAKA,KAAK,CAACK,IAAI;IAAA,CACtB,AAAC;IAEF,IAAIf,CAAAA,GAAS,GAATA,MAAM,CAAC,CAAC,CAAC,SAAY,GAArBA,KAAAA,CAAqB,GAArBA,GAAS,CAAEc,UAAU,EAAE;QACzBnB,GAAG,CAAC0B,GAAG,CAAC,eAAe,CAAC,CAAC;QACzB,MAAMc,CAAAA,GAAAA,WAAe,AAAoC,CAAA,gBAApC,CAACrC,WAAW,EAAE;YAAEE,MAAM;YAAE4B,SAAS;SAAE,CAAC,CAAC;KAC3D;IAED,wCAAwC;IACxC,MAAMQ,CAAAA,GAAAA,cAAyB,AAAkB,CAAA,0BAAlB,CAACtC,WAAW,EAAEC,GAAG,CAAC,CAAC;IAElD,sEAAsE;IACtE,MAAMP,+BAA+B,CAACM,WAAW,EAAEC,GAAG,EAAEC,MAAM,CAAC,CAAC;IAEhE,OAAO;QAAED,GAAG;QAAEC,MAAM;KAAE,CAAC;CACxB;AAEM,eAAeN,oBAAoB,CAAC,EACzCkC,SAAS,CAAA,EACTC,OAAO,CAAA,EAIR,EAAE;QAMoB7B,GAAS;IAL9B,MAAMA,MAAM,GAAG8B,CAAAA,GAAAA,MAAM,AAGpB,CAAA,OAHoB,CACnBC,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC,CAACI,OAAO,CAAC,CAACC,MAAM,GAAKA,MAAM,CAAEG,GAAG;IAAA,CAAC,EACvD,CAAC3B,KAAK,GAAKA,KAAK,CAAC4B,QAAQ;IAAA,CAC1B,AAAC;IAEF,MAAMC,YAAY,GAAGvC,CAAAA,GAAS,GAATA,MAAM,CAAC,CAAC,CAAC,SAAU,GAAnBA,KAAAA,CAAmB,GAAnBA,GAAS,CAAEsC,QAAQ,AAAC;IACzC,IAAI,CAACC,YAAY,EAAE,OAAO,EAAE,CAAC;IAE7B,MAAMC,GAAE,QAAA,CAACC,QAAQ,CAACC,KAAK,CAACpC,KAAI,QAAA,CAACC,IAAI,CAACqB,SAAS,EAAEtB,KAAI,QAAA,CAACqC,OAAO,CAACJ,YAAY,CAAC,CAAC,EAAE;QAAEK,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IAE/F,MAAMC,OAAO,CAACC,GAAG,CACf9C,MAAM,CAACI,GAAG,CAAC,CAAC2C,CAAC,GAAKP,GAAE,QAAA,CAACC,QAAQ,CAACO,SAAS,CAAC1C,KAAI,QAAA,CAACC,IAAI,CAACqB,SAAS,EAAEmB,CAAC,CAACT,QAAQ,CAAC,EAAES,CAAC,CAACE,MAAM,CAAC;IAAA,CAAC,CACrF,CAAC;IAEF,OAAOjD,MAAM,CAACI,GAAG,CAAC,CAAC2C,CAAC,GAAK,GAAG,GAAGA,CAAC,CAACT,QAAQ;IAAA,CAAC,CAAC;CAC5C"}