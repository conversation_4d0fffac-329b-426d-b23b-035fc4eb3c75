{"version": 3, "sources": ["../../../src/export/exportAsync.ts"], "sourcesContent": ["import path from 'path';\n\nimport * as Log from '../log';\nimport { FileNotifier } from '../utils/FileNotifier';\nimport { ensureDirectoryAsync, removeAsync } from '../utils/dir';\nimport { exportAppAsync } from './exportApp';\nimport { Options } from './resolveOptions';\n\nexport async function exportAsync(projectRoot: string, options: Options) {\n  // Ensure the output directory is created\n  const outputPath = path.resolve(projectRoot, options.outputDir);\n  // Delete the output directory if it exists\n  await removeAsync(outputPath);\n  // Create the output directory\n  await ensureDirectoryAsync(outputPath);\n\n  // Export the app\n  await exportAppAsync(projectRoot, options);\n\n  // Stop any file watchers to prevent the CLI from hanging.\n  FileNotifier.stopAll();\n\n  // Final notes\n  Log.log(`Export was successful. Your exported files can be found in ${options.outputDir}`);\n}\n"], "names": ["exportAsync", "Log", "projectRoot", "options", "outputPath", "path", "resolve", "outputDir", "removeAsync", "ensureDirectoryAsync", "exportAppAsync", "FileNotifier", "stopAll", "log"], "mappings": "AAAA;;;;QAQsBA,WAAW,GAAXA,WAAW;AARhB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEXC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACc,IAAA,aAAuB,WAAvB,uBAAuB,CAAA;AACF,IAAA,IAAc,WAAd,cAAc,CAAA;AACjC,IAAA,UAAa,WAAb,aAAa,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGrC,eAAeD,WAAW,CAACE,WAAmB,EAAEC,OAAgB,EAAE;IACvE,yCAAyC;IACzC,MAAMC,UAAU,GAAGC,KAAI,QAAA,CAACC,OAAO,CAACJ,WAAW,EAAEC,OAAO,CAACI,SAAS,CAAC,AAAC;IAChE,2CAA2C;IAC3C,MAAMC,CAAAA,GAAAA,IAAW,AAAY,CAAA,YAAZ,CAACJ,UAAU,CAAC,CAAC;IAC9B,8BAA8B;IAC9B,MAAMK,CAAAA,GAAAA,IAAoB,AAAY,CAAA,qBAAZ,CAACL,UAAU,CAAC,CAAC;IAEvC,iBAAiB;IACjB,MAAMM,CAAAA,GAAAA,UAAc,AAAsB,CAAA,eAAtB,CAACR,WAAW,EAAEC,OAAO,CAAC,CAAC;IAE3C,0DAA0D;IAC1DQ,aAAY,aAAA,CAACC,OAAO,EAAE,CAAC;IAEvB,cAAc;IACdX,GAAG,CAACY,GAAG,CAAC,CAAC,2DAA2D,EAAEV,OAAO,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC;CAC5F"}