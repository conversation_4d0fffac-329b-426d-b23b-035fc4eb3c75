{"version": 3, "sources": ["../../../src/export/exportStaticAsync.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\nimport prettyBytes from 'pretty-bytes';\nimport { inspect } from 'util';\n\nimport { Log } from '../log';\nimport { DevServerManager } from '../start/server/DevServerManager';\nimport { MetroBundlerDevServer } from '../start/server/metro/MetroBundlerDevServer';\nimport { logMetroErrorAsync } from '../start/server/metro/metroErrorInterface';\nimport { getVirtualFaviconAssetsAsync } from './favicon';\n\nconst debug = require('debug')('expo:export:generateStaticRoutes') as typeof console.log;\n\ntype Options = { outputDir: string; minify: boolean };\n\n/** @private */\nexport async function unstable_exportStaticAsync(projectRoot: string, options: Options) {\n  // NOTE(EvanBacon): Please don't use this feature.\n  Log.warn('Static exporting with Metro is an experimental feature.');\n\n  const devServerManager = new DevServerManager(projectRoot, {\n    minify: options.minify,\n    mode: 'production',\n    location: {},\n  });\n\n  await devServerManager.startAsync([\n    {\n      type: 'metro',\n    },\n  ]);\n\n  await exportFromServerAsync(projectRoot, devServerManager, options);\n\n  await devServerManager.stopAsync();\n}\n\n/** Match `(page)` -> `page` */\nfunction matchGroupName(name: string): string | undefined {\n  return name.match(/^\\(([^/]+?)\\)$/)?.[1];\n}\n\nexport async function getFilesToExportFromServerAsync(\n  projectRoot: string,\n  {\n    manifest,\n    renderAsync,\n  }: {\n    manifest: any;\n    renderAsync: (pathname: string) => Promise<string>;\n  }\n): Promise<Map<string, string>> {\n  // name : contents\n  const files = new Map<string, string>();\n\n  await Promise.all(\n    getHtmlFiles({ manifest }).map(async (outputPath) => {\n      const pathname = outputPath.replace(/(index)?\\.html$/, '');\n      try {\n        files.set(outputPath, '');\n        const data = await renderAsync(pathname);\n        files.set(outputPath, data);\n      } catch (e: any) {\n        await logMetroErrorAsync({ error: e, projectRoot });\n        throw new Error('Failed to statically export route: ' + pathname);\n      }\n    })\n  );\n\n  return files;\n}\n\n/** Perform all fs commits */\nexport async function exportFromServerAsync(\n  projectRoot: string,\n  devServerManager: DevServerManager,\n  { outputDir, minify }: Options\n): Promise<void> {\n  const injectFaviconTag = await getVirtualFaviconAssetsAsync(projectRoot, outputDir);\n\n  const devServer = devServerManager.getDefaultDevServer();\n  assert(devServer instanceof MetroBundlerDevServer);\n\n  const [manifest, resources, renderAsync] = await Promise.all([\n    devServer.getRoutesAsync(),\n    devServer.getStaticResourcesAsync({ mode: 'production', minify }),\n    devServer.getStaticRenderFunctionAsync({\n      mode: 'production',\n      minify,\n    }),\n  ]);\n\n  debug('Routes:\\n', inspect(manifest, { colors: true, depth: null }));\n\n  const files = await getFilesToExportFromServerAsync(projectRoot, {\n    manifest,\n    async renderAsync(pathname: string) {\n      const template = await renderAsync(pathname);\n      let html = await devServer.composeResourcesWithHtml({\n        mode: 'production',\n        resources,\n        template,\n      });\n\n      if (injectFaviconTag) {\n        html = injectFaviconTag(html);\n      }\n\n      return html;\n    },\n  });\n\n  resources.forEach((resource) => {\n    files.set(resource.filename, resource.source);\n  });\n\n  fs.mkdirSync(path.join(outputDir), { recursive: true });\n\n  Log.log('');\n  Log.log(chalk.bold`Exporting ${files.size} files:`);\n  await Promise.all(\n    [...files.entries()]\n      .sort(([a], [b]) => a.localeCompare(b))\n      .map(async ([file, contents]) => {\n        const length = Buffer.byteLength(contents, 'utf8');\n        Log.log(file, chalk.gray`(${prettyBytes(length)})`);\n        const outputPath = path.join(outputDir, file);\n        await fs.promises.mkdir(path.dirname(outputPath), { recursive: true });\n        await fs.promises.writeFile(outputPath, contents);\n      })\n  );\n  Log.log('');\n}\n\nexport function getHtmlFiles({ manifest }: { manifest: any }): string[] {\n  const htmlFiles = new Set<string>();\n\n  function traverseScreens(screens: string | { screens: any; path: string }, basePath = '') {\n    for (const value of Object.values(screens)) {\n      if (typeof value === 'string') {\n        let filePath = basePath + value;\n        if (value === '') {\n          filePath =\n            basePath === ''\n              ? 'index'\n              : basePath.endsWith('/')\n              ? basePath + 'index'\n              : basePath.slice(0, -1);\n        }\n        // TODO: Dedupe requests for alias routes.\n        addOptionalGroups(filePath);\n      } else if (typeof value === 'object' && value?.screens) {\n        const newPath = basePath + value.path + '/';\n        traverseScreens(value.screens, newPath);\n      }\n    }\n  }\n\n  function addOptionalGroups(path: string) {\n    const variations = getPathVariations(path);\n    for (const variation of variations) {\n      htmlFiles.add(variation);\n    }\n  }\n\n  traverseScreens(manifest.screens);\n\n  return Array.from(htmlFiles).map((value) => {\n    const parts = value.split('/');\n    // Replace `:foo` with `[foo]` and `*foo` with `[...foo]`\n    const partsWithGroups = parts.map((part) => {\n      if (part.startsWith(':')) {\n        return `[${part.slice(1)}]`;\n      } else if (part.startsWith('*')) {\n        return `[...${part.slice(1)}]`;\n      }\n      return part;\n    });\n    return partsWithGroups.join('/') + '.html';\n  });\n}\n\n// Given a route like `(foo)/bar/(baz)`, return all possible variations of the route.\n// e.g. `(foo)/bar/(baz)`, `(foo)/bar/baz`, `foo/bar/(baz)`, `foo/bar/baz`,\nexport function getPathVariations(routePath: string): string[] {\n  const variations = new Set<string>([routePath]);\n  const segments = routePath.split('/');\n\n  function generateVariations(segments: string[], index: number): void {\n    if (index >= segments.length) {\n      return;\n    }\n\n    const newSegments = [...segments];\n    while (\n      index < newSegments.length &&\n      matchGroupName(newSegments[index]) &&\n      newSegments.length > 1\n    ) {\n      newSegments.splice(index, 1);\n      variations.add(newSegments.join('/'));\n      generateVariations(newSegments, index + 1);\n    }\n\n    generateVariations(segments, index + 1);\n  }\n\n  generateVariations(segments, 0);\n\n  return Array.from(variations);\n}\n"], "names": ["unstable_exportStaticAsync", "getFilesToExportFromServerAsync", "exportFromServerAsync", "getHtmlFiles", "getPathVariations", "debug", "require", "projectRoot", "options", "Log", "warn", "devServerManager", "DevServerManager", "minify", "mode", "location", "startAsync", "type", "stopAsync", "matchGroupName", "name", "match", "manifest", "renderAsync", "files", "Map", "Promise", "all", "map", "outputPath", "pathname", "replace", "set", "data", "e", "logMetroErrorAsync", "error", "Error", "outputDir", "injectFaviconTag", "getVirtualFaviconAssetsAsync", "devServer", "getDefaultDevServer", "assert", "MetroBundlerDevServer", "resources", "getRoutesAsync", "getStaticResourcesAsync", "getStaticRenderFunctionAsync", "inspect", "colors", "depth", "template", "html", "composeResourcesWithHtml", "for<PERSON>ach", "resource", "filename", "source", "fs", "mkdirSync", "path", "join", "recursive", "log", "chalk", "bold", "size", "entries", "sort", "a", "b", "localeCompare", "file", "contents", "length", "<PERSON><PERSON><PERSON>", "byteLength", "gray", "prettyBytes", "promises", "mkdir", "dirname", "writeFile", "htmlFiles", "Set", "traverseScreens", "screens", "basePath", "value", "Object", "values", "filePath", "endsWith", "slice", "addOptionalGroups", "newPath", "variations", "variation", "add", "Array", "from", "parts", "split", "partsWithGroups", "part", "startsWith", "routePath", "segments", "generateVariations", "index", "newSegments", "splice"], "mappings": "AAMA;;;;QAkBsBA,0BAA0B,GAA1BA,0BAA0B;QA0B1BC,+BAA+B,GAA/BA,+BAA+B;QA+B/BC,qBAAqB,GAArBA,qBAAqB;QA6D3BC,YAAY,GAAZA,YAAY;QAkDZC,iBAAiB,GAAjBA,iBAAiB;AA1Ld,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACT,IAAA,MAAO,kCAAP,OAAO,EAAA;AACV,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,YAAc,kCAAd,cAAc,EAAA;AACd,IAAA,KAAM,WAAN,MAAM,CAAA;AAEV,IAAA,IAAQ,WAAR,QAAQ,CAAA;AACK,IAAA,iBAAkC,WAAlC,kCAAkC,CAAA;AAC7B,IAAA,sBAA6C,WAA7C,6CAA6C,CAAA;AAChD,IAAA,oBAA2C,WAA3C,2CAA2C,CAAA;AACjC,IAAA,QAAW,WAAX,WAAW,CAAA;;;;;;AAExD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC,AAAsB,AAAC;AAKlF,eAAeN,0BAA0B,CAACO,WAAmB,EAAEC,OAAgB,EAAE;IACtF,kDAAkD;IAClDC,IAAG,IAAA,CAACC,IAAI,CAAC,yDAAyD,CAAC,CAAC;IAEpE,MAAMC,gBAAgB,GAAG,IAAIC,iBAAgB,iBAAA,CAACL,WAAW,EAAE;QACzDM,MAAM,EAAEL,OAAO,CAACK,MAAM;QACtBC,IAAI,EAAE,YAAY;QAClBC,QAAQ,EAAE,EAAE;KACb,CAAC,AAAC;IAEH,MAAMJ,gBAAgB,CAACK,UAAU,CAAC;QAChC;YACEC,IAAI,EAAE,OAAO;SACd;KACF,CAAC,CAAC;IAEH,MAAMf,qBAAqB,CAACK,WAAW,EAAEI,gBAAgB,EAAEH,OAAO,CAAC,CAAC;IAEpE,MAAMG,gBAAgB,CAACO,SAAS,EAAE,CAAC;CACpC;AAED,+BAA+B,CAC/B,SAASC,cAAc,CAACC,IAAY,EAAsB;QACjDA,GAA4B;IAAnC,OAAOA,CAAAA,GAA4B,GAA5BA,IAAI,CAACC,KAAK,kBAAkB,SAAK,GAAjCD,KAAAA,CAAiC,GAAjCA,GAA4B,AAAE,CAAC,CAAC,CAAC,CAAC;CAC1C;AAEM,eAAenB,+BAA+B,CACnDM,WAAmB,EACnB,EACEe,QAAQ,CAAA,EACRC,WAAW,CAAA,EAIZ,EAC6B;IAC9B,kBAAkB;IAClB,MAAMC,KAAK,GAAG,IAAIC,GAAG,EAAkB,AAAC;IAExC,MAAMC,OAAO,CAACC,GAAG,CACfxB,YAAY,CAAC;QAAEmB,QAAQ;KAAE,CAAC,CAACM,GAAG,CAAC,OAAOC,UAAU,GAAK;QACnD,MAAMC,QAAQ,GAAGD,UAAU,CAACE,OAAO,oBAAoB,EAAE,CAAC,AAAC;QAC3D,IAAI;YACFP,KAAK,CAACQ,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC,CAAC;YAC1B,MAAMI,IAAI,GAAG,MAAMV,WAAW,CAACO,QAAQ,CAAC,AAAC;YACzCN,KAAK,CAACQ,GAAG,CAACH,UAAU,EAAEI,IAAI,CAAC,CAAC;SAC7B,CAAC,OAAOC,CAAC,EAAO;YACf,MAAMC,CAAAA,GAAAA,oBAAkB,AAA2B,CAAA,mBAA3B,CAAC;gBAAEC,KAAK,EAAEF,CAAC;gBAAE3B,WAAW;aAAE,CAAC,CAAC;YACpD,MAAM,IAAI8B,KAAK,CAAC,qCAAqC,GAAGP,QAAQ,CAAC,CAAC;SACnE;KACF,CAAC,CACH,CAAC;IAEF,OAAON,KAAK,CAAC;CACd;AAGM,eAAetB,qBAAqB,CACzCK,WAAmB,EACnBI,gBAAkC,EAClC,EAAE2B,SAAS,CAAA,EAAEzB,MAAM,CAAA,EAAW,EACf;IACf,MAAM0B,gBAAgB,GAAG,MAAMC,CAAAA,GAAAA,QAA4B,AAAwB,CAAA,6BAAxB,CAACjC,WAAW,EAAE+B,SAAS,CAAC,AAAC;IAEpF,MAAMG,SAAS,GAAG9B,gBAAgB,CAAC+B,mBAAmB,EAAE,AAAC;IACzDC,CAAAA,GAAAA,OAAM,AAA4C,CAAA,QAA5C,CAACF,SAAS,YAAYG,sBAAqB,sBAAA,CAAC,CAAC;IAEnD,MAAM,CAACtB,QAAQ,EAAEuB,SAAS,EAAEtB,WAAW,CAAC,GAAG,MAAMG,OAAO,CAACC,GAAG,CAAC;QAC3Dc,SAAS,CAACK,cAAc,EAAE;QAC1BL,SAAS,CAACM,uBAAuB,CAAC;YAAEjC,IAAI,EAAE,YAAY;YAAED,MAAM;SAAE,CAAC;QACjE4B,SAAS,CAACO,4BAA4B,CAAC;YACrClC,IAAI,EAAE,YAAY;YAClBD,MAAM;SACP,CAAC;KACH,CAAC,AAAC;IAEHR,KAAK,CAAC,WAAW,EAAE4C,CAAAA,GAAAA,KAAO,AAAyC,CAAA,QAAzC,CAAC3B,QAAQ,EAAE;QAAE4B,MAAM,EAAE,IAAI;QAAEC,KAAK,EAAE,IAAI;KAAE,CAAC,CAAC,CAAC;IAErE,MAAM3B,KAAK,GAAG,MAAMvB,+BAA+B,CAACM,WAAW,EAAE;QAC/De,QAAQ;QACR,MAAMC,WAAW,EAACO,QAAgB,EAAE;YAClC,MAAMsB,QAAQ,GAAG,MAAM7B,WAAW,CAACO,QAAQ,CAAC,AAAC;YAC7C,IAAIuB,IAAI,GAAG,MAAMZ,SAAS,CAACa,wBAAwB,CAAC;gBAClDxC,IAAI,EAAE,YAAY;gBAClB+B,SAAS;gBACTO,QAAQ;aACT,CAAC,AAAC;YAEH,IAAIb,gBAAgB,EAAE;gBACpBc,IAAI,GAAGd,gBAAgB,CAACc,IAAI,CAAC,CAAC;aAC/B;YAED,OAAOA,IAAI,CAAC;SACb;KACF,CAAC,AAAC;IAEHR,SAAS,CAACU,OAAO,CAAC,CAACC,QAAQ,GAAK;QAC9BhC,KAAK,CAACQ,GAAG,CAACwB,QAAQ,CAACC,QAAQ,EAAED,QAAQ,CAACE,MAAM,CAAC,CAAC;KAC/C,CAAC,CAAC;IAEHC,GAAE,QAAA,CAACC,SAAS,CAACC,KAAI,QAAA,CAACC,IAAI,CAACxB,SAAS,CAAC,EAAE;QAAEyB,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IAExDtD,IAAG,IAAA,CAACuD,GAAG,CAAC,EAAE,CAAC,CAAC;IACZvD,IAAG,IAAA,CAACuD,GAAG,CAACC,MAAK,QAAA,CAACC,IAAI,CAAC,UAAU,EAAE1C,KAAK,CAAC2C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACpD,MAAMzC,OAAO,CAACC,GAAG,CACf;WAAIH,KAAK,CAAC4C,OAAO,EAAE;KAAC,CACjBC,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,GAAKD,CAAC,CAACE,aAAa,CAACD,CAAC,CAAC;IAAA,CAAC,CACtC3C,GAAG,CAAC,OAAO,CAAC6C,IAAI,EAAEC,QAAQ,CAAC,GAAK;QAC/B,MAAMC,MAAM,GAAGC,MAAM,CAACC,UAAU,CAACH,QAAQ,EAAE,MAAM,CAAC,AAAC;QACnDjE,IAAG,IAAA,CAACuD,GAAG,CAACS,IAAI,EAAER,MAAK,QAAA,CAACa,IAAI,CAAC,CAAC,EAAEC,CAAAA,GAAAA,YAAW,AAAQ,CAAA,QAAR,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,MAAM9C,UAAU,GAAGgC,KAAI,QAAA,CAACC,IAAI,CAACxB,SAAS,EAAEmC,IAAI,CAAC,AAAC;QAC9C,MAAMd,GAAE,QAAA,CAACqB,QAAQ,CAACC,KAAK,CAACpB,KAAI,QAAA,CAACqB,OAAO,CAACrD,UAAU,CAAC,EAAE;YAAEkC,SAAS,EAAE,IAAI;SAAE,CAAC,CAAC;QACvE,MAAMJ,GAAE,QAAA,CAACqB,QAAQ,CAACG,SAAS,CAACtD,UAAU,EAAE6C,QAAQ,CAAC,CAAC;KACnD,CAAC,CACL,CAAC;IACFjE,IAAG,IAAA,CAACuD,GAAG,CAAC,EAAE,CAAC,CAAC;CACb;AAEM,SAAS7D,YAAY,CAAC,EAAEmB,QAAQ,CAAA,EAAqB,EAAY;IACtE,MAAM8D,SAAS,GAAG,IAAIC,GAAG,EAAU,AAAC;IAEpC,SAASC,eAAe,CAACC,OAAgD,EAAEC,QAAQ,GAAG,EAAE,EAAE;QACxF,KAAK,MAAMC,KAAK,IAAIC,MAAM,CAACC,MAAM,CAACJ,OAAO,CAAC,CAAE;YAC1C,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;gBAC7B,IAAIG,QAAQ,GAAGJ,QAAQ,GAAGC,KAAK,AAAC;gBAChC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBAChBG,QAAQ,GACNJ,QAAQ,KAAK,EAAE,GACX,OAAO,GACPA,QAAQ,CAACK,QAAQ,CAAC,GAAG,CAAC,GACtBL,QAAQ,GAAG,OAAO,GAClBA,QAAQ,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBAC7B;gBACD,0CAA0C;gBAC1CC,iBAAiB,CAACH,QAAQ,CAAC,CAAC;aAC7B,MAAM,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAIA,CAAAA,KAAK,QAAS,GAAdA,KAAAA,CAAc,GAAdA,KAAK,CAAEF,OAAO,CAAA,EAAE;gBACtD,MAAMS,OAAO,GAAGR,QAAQ,GAAGC,KAAK,CAAC5B,IAAI,GAAG,GAAG,AAAC;gBAC5CyB,eAAe,CAACG,KAAK,CAACF,OAAO,EAAES,OAAO,CAAC,CAAC;aACzC;SACF;KACF;IAED,SAASD,iBAAiB,CAAClC,IAAY,EAAE;QACvC,MAAMoC,UAAU,GAAG7F,iBAAiB,CAACyD,IAAI,CAAC,AAAC;QAC3C,KAAK,MAAMqC,SAAS,IAAID,UAAU,CAAE;YAClCb,SAAS,CAACe,GAAG,CAACD,SAAS,CAAC,CAAC;SAC1B;KACF;IAEDZ,eAAe,CAAChE,QAAQ,CAACiE,OAAO,CAAC,CAAC;IAElC,OAAOa,KAAK,CAACC,IAAI,CAACjB,SAAS,CAAC,CAACxD,GAAG,CAAC,CAAC6D,KAAK,GAAK;QAC1C,MAAMa,KAAK,GAAGb,KAAK,CAACc,KAAK,CAAC,GAAG,CAAC,AAAC;QAC/B,yDAAyD;QACzD,MAAMC,eAAe,GAAGF,KAAK,CAAC1E,GAAG,CAAC,CAAC6E,IAAI,GAAK;YAC1C,IAAIA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACxB,OAAO,CAAC,CAAC,EAAED,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7B,MAAM,IAAIW,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC/B,OAAO,CAAC,IAAI,EAAED,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;YACD,OAAOW,IAAI,CAAC;SACb,CAAC,AAAC;QACH,OAAOD,eAAe,CAAC1C,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;KAC5C,CAAC,CAAC;CACJ;AAIM,SAAS1D,iBAAiB,CAACuG,SAAiB,EAAY;IAC7D,MAAMV,UAAU,GAAG,IAAIZ,GAAG,CAAS;QAACsB,SAAS;KAAC,CAAC,AAAC;IAChD,MAAMC,SAAQ,GAAGD,SAAS,CAACJ,KAAK,CAAC,GAAG,CAAC,AAAC;IAEtC,SAASM,kBAAkB,CAACD,QAAkB,EAAEE,KAAa,EAAQ;QACnE,IAAIA,KAAK,IAAIF,QAAQ,CAACjC,MAAM,EAAE;YAC5B,OAAO;SACR;QAED,MAAMoC,WAAW,GAAG;eAAIH,QAAQ;SAAC,AAAC;QAClC,MACEE,KAAK,GAAGC,WAAW,CAACpC,MAAM,IAC1BxD,cAAc,CAAC4F,WAAW,CAACD,KAAK,CAAC,CAAC,IAClCC,WAAW,CAACpC,MAAM,GAAG,CAAC,CACtB;YACAoC,WAAW,CAACC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7Bb,UAAU,CAACE,GAAG,CAACY,WAAW,CAACjD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACtC+C,kBAAkB,CAACE,WAAW,EAAED,KAAK,GAAG,CAAC,CAAC,CAAC;SAC5C;QAEDD,kBAAkB,CAACD,QAAQ,EAAEE,KAAK,GAAG,CAAC,CAAC,CAAC;KACzC;IAEDD,kBAAkB,CAACD,SAAQ,EAAE,CAAC,CAAC,CAAC;IAEhC,OAAOR,KAAK,CAACC,IAAI,CAACJ,UAAU,CAAC,CAAC;CAC/B"}