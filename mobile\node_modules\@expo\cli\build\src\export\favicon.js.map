{"version": 3, "sources": ["../../../src/export/favicon.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport { generateFaviconAsync, generateImageAsync } from '@expo/image-utils';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { getUserDefinedFile } from './publicFolder';\n\nconst debug = require('debug')('expo:favicon') as typeof console.log;\n\n/** @returns the file system path for a user-defined favicon.ico file in the public folder. */\nexport function getUserDefinedFaviconFile(projectRoot: string): string | null {\n  return getUserDefinedFile(projectRoot, ['./favicon.ico']);\n}\n\nexport async function getVirtualFaviconAssetsAsync(\n  projectRoot: string,\n  outputDir: string\n): Promise<((html: string) => string) | null> {\n  const existing = getUserDefinedFaviconFile(projectRoot);\n  if (existing) {\n    debug('Using user-defined favicon.ico file.');\n    return null;\n  }\n\n  const data = await getFaviconFromExpoConfigAsync(projectRoot);\n\n  if (!data) {\n    return null;\n  }\n\n  await Promise.all(\n    [data].map((asset) => {\n      const assetPath = path.join(outputDir, asset.path);\n      debug('Writing asset to disk: ' + assetPath);\n      return fs.promises.writeFile(assetPath, asset.source);\n    })\n  );\n\n  return injectFaviconTag;\n}\n\nfunction injectFaviconTag(html: string): string {\n  if (!html.includes('</head>')) {\n    return html;\n  }\n  return html.replace('</head>', `<link rel=\"shortcut icon\" href=\"/favicon.ico\" /></head>`);\n}\n\nexport async function getFaviconFromExpoConfigAsync(projectRoot: string) {\n  const { exp } = getConfig(projectRoot);\n\n  const src = exp.web?.favicon ?? null;\n  if (!src) {\n    return null;\n  }\n\n  const dims = [16, 32, 48];\n  const cacheType = 'favicon';\n\n  const size = dims[dims.length - 1];\n  const { source } = await generateImageAsync(\n    { projectRoot, cacheType },\n    {\n      resizeMode: 'contain',\n      src,\n      backgroundColor: 'transparent',\n      width: size,\n      height: size,\n      name: `favicon-${size}.png`,\n    }\n  );\n\n  const faviconBuffer = await generateFaviconAsync(source, dims);\n\n  return { source: faviconBuffer, path: 'favicon.ico' };\n}\n"], "names": ["getUserDefinedFaviconFile", "getVirtualFaviconAssetsAsync", "getFaviconFromExpoConfigAsync", "debug", "require", "projectRoot", "getUserDefinedFile", "outputDir", "existing", "data", "Promise", "all", "map", "asset", "assetPath", "path", "join", "fs", "promises", "writeFile", "source", "injectFaviconTag", "html", "includes", "replace", "exp", "getConfig", "src", "web", "favicon", "dims", "cacheType", "size", "length", "generateImageAsync", "resizeMode", "backgroundColor", "width", "height", "name", "favi<PERSON><PERSON><PERSON><PERSON>", "generateFaviconAsync"], "mappings": "AAAA;;;;QAUgBA,yBAAyB,GAAzBA,yBAAyB;QAInBC,4BAA4B,GAA5BA,4BAA4B;QAkC5BC,6BAA6B,GAA7BA,6BAA6B;AAhDzB,IAAA,OAAc,WAAd,cAAc,CAAA;AACiB,IAAA,WAAmB,WAAnB,mBAAmB,CAAA;AAC7D,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEY,IAAA,aAAgB,WAAhB,gBAAgB,CAAA;;;;;;AAEnD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,AAAsB,AAAC;AAG9D,SAASJ,yBAAyB,CAACK,WAAmB,EAAiB;IAC5E,OAAOC,CAAAA,GAAAA,aAAkB,AAAgC,CAAA,mBAAhC,CAACD,WAAW,EAAE;QAAC,eAAe;KAAC,CAAC,CAAC;CAC3D;AAEM,eAAeJ,4BAA4B,CAChDI,WAAmB,EACnBE,SAAiB,EAC2B;IAC5C,MAAMC,QAAQ,GAAGR,yBAAyB,CAACK,WAAW,CAAC,AAAC;IACxD,IAAIG,QAAQ,EAAE;QACZL,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;KACb;IAED,MAAMM,IAAI,GAAG,MAAMP,6BAA6B,CAACG,WAAW,CAAC,AAAC;IAE9D,IAAI,CAACI,IAAI,EAAE;QACT,OAAO,IAAI,CAAC;KACb;IAED,MAAMC,OAAO,CAACC,GAAG,CACf;QAACF,IAAI;KAAC,CAACG,GAAG,CAAC,CAACC,KAAK,GAAK;QACpB,MAAMC,SAAS,GAAGC,KAAI,QAAA,CAACC,IAAI,CAACT,SAAS,EAAEM,KAAK,CAACE,IAAI,CAAC,AAAC;QACnDZ,KAAK,CAAC,yBAAyB,GAAGW,SAAS,CAAC,CAAC;QAC7C,OAAOG,GAAE,QAAA,CAACC,QAAQ,CAACC,SAAS,CAACL,SAAS,EAAED,KAAK,CAACO,MAAM,CAAC,CAAC;KACvD,CAAC,CACH,CAAC;IAEF,OAAOC,gBAAgB,CAAC;CACzB;AAED,SAASA,gBAAgB,CAACC,IAAY,EAAU;IAC9C,IAAI,CAACA,IAAI,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC7B,OAAOD,IAAI,CAAC;KACb;IACD,OAAOA,IAAI,CAACE,OAAO,CAAC,SAAS,EAAE,CAAC,uDAAuD,CAAC,CAAC,CAAC;CAC3F;AAEM,eAAetB,6BAA6B,CAACG,WAAmB,EAAE;QAG3DoB,GAAO;IAFnB,MAAM,EAAEA,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAAa,CAAA,UAAb,CAACrB,WAAW,CAAC,AAAC;QAE3BoB,IAAgB;IAA5B,MAAME,GAAG,GAAGF,CAAAA,IAAgB,GAAhBA,CAAAA,GAAO,GAAPA,GAAG,CAACG,GAAG,SAAS,GAAhBH,KAAAA,CAAgB,GAAhBA,GAAO,CAAEI,OAAO,YAAhBJ,IAAgB,GAAI,IAAI,AAAC;IACrC,IAAI,CAACE,GAAG,EAAE;QACR,OAAO,IAAI,CAAC;KACb;IAED,MAAMG,IAAI,GAAG;AAAC,UAAE;AAAE,UAAE;AAAE,UAAE;KAAC,AAAC;IAC1B,MAAMC,SAAS,GAAG,SAAS,AAAC;IAE5B,MAAMC,IAAI,GAAGF,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,AAAC;IACnC,MAAM,EAAEb,MAAM,CAAA,EAAE,GAAG,MAAMc,CAAAA,GAAAA,WAAkB,AAU1C,CAAA,mBAV0C,CACzC;QAAE7B,WAAW;QAAE0B,SAAS;KAAE,EAC1B;QACEI,UAAU,EAAE,SAAS;QACrBR,GAAG;QACHS,eAAe,EAAE,aAAa;QAC9BC,KAAK,EAAEL,IAAI;QACXM,MAAM,EAAEN,IAAI;QACZO,IAAI,EAAE,CAAC,QAAQ,EAAEP,IAAI,CAAC,IAAI,CAAC;KAC5B,CACF,AAAC;IAEF,MAAMQ,aAAa,GAAG,MAAMC,CAAAA,GAAAA,WAAoB,AAAc,CAAA,qBAAd,CAACrB,MAAM,EAAEU,IAAI,CAAC,AAAC;IAE/D,OAAO;QAAEV,MAAM,EAAEoB,aAAa;QAAEzB,IAAI,EAAE,aAAa;KAAE,CAAC;CACvD"}