{"version": 3, "sources": ["../../../src/export/fork-bundleAsync.ts"], "sourcesContent": ["import { ExpoConfig, getConfigFilePaths, Platform } from '@expo/config';\nimport {\n  buildHermesBundleAsync,\n  isEnableHermesManaged,\n  maybeThrowFromInconsistentEngineAsync,\n} from '@expo/dev-server/build/HermesBundler';\nimport {\n  importMetroFromProject,\n  importMetroServerFromProject,\n} from '@expo/dev-server/build/metro/importMetroFromProject';\nimport type { LoadOptions } from '@expo/metro-config';\nimport chalk from 'chalk';\nimport Metro from 'metro';\nimport type { BundleOptions as MetroBundleOptions } from 'metro/src/shared/types';\n\nimport { CSSAsset, getCssModulesFromBundler } from '../start/server/metro/getCssModulesFromBundler';\nimport { loadMetroConfigAsync } from '../start/server/metro/instantiateMetro';\n\nexport type MetroDevServerOptions = LoadOptions & {\n  logger: import('@expo/bunyan');\n  quiet?: boolean;\n};\nexport type BundleOptions = {\n  entryPoint: string;\n  platform: 'android' | 'ios' | 'web';\n  dev?: boolean;\n  minify?: boolean;\n  sourceMapUrl?: string;\n};\nexport type BundleAssetWithFileHashes = Metro.AssetData & {\n  fileHashes: string[]; // added by the hashAssets asset plugin\n};\nexport type BundleOutput = {\n  code: string;\n  map?: string;\n  hermesBytecodeBundle?: Uint8Array;\n  hermesSourcemap?: string;\n  css: CSSAsset[];\n  assets: readonly BundleAssetWithFileHashes[];\n};\n\nlet nextBuildID = 0;\n\n// Fork of @expo/dev-server bundleAsync to add Metro logging back.\n\nasync function assertEngineMismatchAsync(projectRoot: string, exp: ExpoConfig, platform: Platform) {\n  const isHermesManaged = isEnableHermesManaged(exp, platform);\n\n  const paths = getConfigFilePaths(projectRoot);\n  const configFilePath = paths.dynamicConfigPath ?? paths.staticConfigPath ?? 'app.json';\n  await maybeThrowFromInconsistentEngineAsync(\n    projectRoot,\n    configFilePath,\n    platform,\n    isHermesManaged\n  );\n}\n\nexport async function bundleAsync(\n  projectRoot: string,\n  expoConfig: ExpoConfig,\n  options: MetroDevServerOptions,\n  bundles: BundleOptions[]\n): Promise<BundleOutput[]> {\n  // Assert early so the user doesn't have to wait until bundling is complete to find out that\n  // Hermes won't be available.\n  await Promise.all(\n    bundles.map(({ platform }) => assertEngineMismatchAsync(projectRoot, expoConfig, platform))\n  );\n\n  const metro = importMetroFromProject(projectRoot);\n  const Server = importMetroServerFromProject(projectRoot);\n\n  const { config, reporter } = await loadMetroConfigAsync(projectRoot, options, {\n    exp: expoConfig,\n  });\n\n  const metroServer = await metro.runMetro(config, {\n    watch: false,\n  });\n\n  const buildAsync = async (bundle: BundleOptions): Promise<BundleOutput> => {\n    const buildID = `bundle_${nextBuildID++}_${bundle.platform}`;\n    const isHermes = isEnableHermesManaged(expoConfig, bundle.platform);\n    const bundleOptions: MetroBundleOptions = {\n      ...Server.DEFAULT_BUNDLE_OPTIONS,\n      bundleType: 'bundle',\n      platform: bundle.platform,\n      entryFile: bundle.entryPoint,\n      dev: bundle.dev ?? false,\n      minify: !isHermes && (bundle.minify ?? !bundle.dev),\n      inlineSourceMap: false,\n      sourceMapUrl: bundle.sourceMapUrl,\n      createModuleIdFactory: config.serializer.createModuleIdFactory,\n      onProgress: (transformedFileCount: number, totalFileCount: number) => {\n        if (!options.quiet) {\n          reporter.update({\n            buildID,\n            type: 'bundle_transform_progressed',\n            transformedFileCount,\n            totalFileCount,\n          });\n        }\n      },\n    };\n    const bundleDetails = {\n      ...bundleOptions,\n      buildID,\n    };\n    reporter.update({\n      buildID,\n      type: 'bundle_build_started',\n      bundleDetails,\n    });\n    try {\n      const { code, map } = await metroServer.build(bundleOptions);\n      const [assets, css] = await Promise.all([\n        metroServer.getAssets(bundleOptions),\n        getCssModulesFromBundler(config, metroServer.getBundler(), bundleOptions),\n      ]);\n\n      reporter.update({\n        buildID,\n        type: 'bundle_build_done',\n      });\n      return { code, map, assets: assets as readonly BundleAssetWithFileHashes[], css };\n    } catch (error) {\n      reporter.update({\n        buildID,\n        type: 'bundle_build_failed',\n      });\n\n      throw error;\n    }\n  };\n\n  const maybeAddHermesBundleAsync = async (\n    bundle: BundleOptions,\n    bundleOutput: BundleOutput\n  ): Promise<BundleOutput> => {\n    const { platform } = bundle;\n    const isHermesManaged = isEnableHermesManaged(expoConfig, platform);\n    if (isHermesManaged) {\n      const platformTag = chalk.bold(\n        { ios: 'iOS', android: 'Android', web: 'Web' }[platform] || platform\n      );\n\n      reporter.terminal.log(`${platformTag} Building Hermes bytecode for the bundle`);\n\n      const hermesBundleOutput = await buildHermesBundleAsync(\n        projectRoot,\n        bundleOutput.code,\n        bundleOutput.map!,\n        bundle.minify ?? !bundle.dev\n      );\n      bundleOutput.hermesBytecodeBundle = hermesBundleOutput.hbc;\n      bundleOutput.hermesSourcemap = hermesBundleOutput.sourcemap;\n    }\n    return bundleOutput;\n  };\n\n  try {\n    const intermediateOutputs = await Promise.all(bundles.map((bundle) => buildAsync(bundle)));\n    const bundleOutputs: BundleOutput[] = [];\n    for (let i = 0; i < bundles.length; ++i) {\n      // hermesc does not support parallel building even we spawn processes.\n      // we should build them sequentially.\n      bundleOutputs.push(await maybeAddHermesBundleAsync(bundles[i], intermediateOutputs[i]));\n    }\n    return bundleOutputs;\n  } catch (error) {\n    // New line so errors don't show up inline with the progress bar\n    console.log('');\n    throw error;\n  } finally {\n    metroServer.end();\n  }\n}\n"], "names": ["bundleAsync", "nextBuildID", "assertEngineMismatchAsync", "projectRoot", "exp", "platform", "isHermesManaged", "isEnableHermesManaged", "paths", "getConfigFilePaths", "config<PERSON><PERSON><PERSON><PERSON>", "dynamicConfigPath", "staticConfigPath", "maybeThrowFromInconsistentEngineAsync", "expoConfig", "options", "bundles", "Promise", "all", "map", "metro", "importMetroFromProject", "Server", "importMetroServerFromProject", "config", "reporter", "loadMetroConfigAsync", "metroServer", "runMetro", "watch", "buildAsync", "bundle", "buildID", "isHermes", "bundleOptions", "DEFAULT_BUNDLE_OPTIONS", "bundleType", "entryFile", "entryPoint", "dev", "minify", "inlineSourceMap", "sourceMapUrl", "createModuleIdFactory", "serializer", "onProgress", "transformedFileCount", "totalFileCount", "quiet", "update", "type", "bundleDetails", "code", "build", "assets", "css", "getAssets", "getCssModulesFromBundler", "getBundler", "error", "maybeAddHermesBundleAsync", "bundleOutput", "platformTag", "chalk", "bold", "ios", "android", "web", "terminal", "log", "hermesBundleOutput", "buildHermesBundleAsync", "hermesBytecodeBundle", "hbc", "hermesSourcemap", "sourcemap", "intermediateOutputs", "bundleOutputs", "i", "length", "push", "console", "end"], "mappings": "AAAA;;;;QA0DsBA,WAAW,GAAXA,WAAW;AA1DwB,IAAA,OAAc,WAAd,cAAc,CAAA;AAKhE,IAAA,cAAsC,WAAtC,sCAAsC,CAAA;AAItC,IAAA,uBAAqD,WAArD,qDAAqD,CAAA;AAE1C,IAAA,MAAO,kCAAP,OAAO,EAAA;AAI0B,IAAA,yBAAgD,WAAhD,gDAAgD,CAAA;AAC9D,IAAA,iBAAwC,WAAxC,wCAAwC,CAAA;;;;;;AAyB7E,IAAIC,WAAW,GAAG,CAAC,AAAC;AAEpB,kEAAkE;AAElE,eAAeC,yBAAyB,CAACC,WAAmB,EAAEC,GAAe,EAAEC,QAAkB,EAAE;IACjG,MAAMC,eAAe,GAAGC,CAAAA,GAAAA,cAAqB,AAAe,CAAA,sBAAf,CAACH,GAAG,EAAEC,QAAQ,CAAC,AAAC;IAE7D,MAAMG,KAAK,GAAGC,CAAAA,GAAAA,OAAkB,AAAa,CAAA,mBAAb,CAACN,WAAW,CAAC,AAAC;QACvBK,kBAAuB,EAAvBA,GAAiD;IAAxE,MAAME,cAAc,GAAGF,CAAAA,GAAiD,GAAjDA,CAAAA,kBAAuB,GAAvBA,KAAK,CAACG,iBAAiB,YAAvBH,kBAAuB,GAAIA,KAAK,CAACI,gBAAgB,YAAjDJ,GAAiD,GAAI,UAAU,AAAC;IACvF,MAAMK,CAAAA,GAAAA,cAAqC,AAK1C,CAAA,sCAL0C,CACzCV,WAAW,EACXO,cAAc,EACdL,QAAQ,EACRC,eAAe,CAChB,CAAC;CACH;AAEM,eAAeN,WAAW,CAC/BG,WAAmB,EACnBW,UAAsB,EACtBC,OAA8B,EAC9BC,OAAwB,EACC;IACzB,4FAA4F;IAC5F,6BAA6B;IAC7B,MAAMC,OAAO,CAACC,GAAG,CACfF,OAAO,CAACG,GAAG,CAAC,CAAC,EAAEd,QAAQ,CAAA,EAAE,GAAKH,yBAAyB,CAACC,WAAW,EAAEW,UAAU,EAAET,QAAQ,CAAC;IAAA,CAAC,CAC5F,CAAC;IAEF,MAAMe,KAAK,GAAGC,CAAAA,GAAAA,uBAAsB,AAAa,CAAA,uBAAb,CAAClB,WAAW,CAAC,AAAC;IAClD,MAAMmB,MAAM,GAAGC,CAAAA,GAAAA,uBAA4B,AAAa,CAAA,6BAAb,CAACpB,WAAW,CAAC,AAAC;IAEzD,MAAM,EAAEqB,MAAM,CAAA,EAAEC,QAAQ,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,iBAAoB,AAErD,CAAA,qBAFqD,CAACvB,WAAW,EAAEY,OAAO,EAAE;QAC5EX,GAAG,EAAEU,UAAU;KAChB,CAAC,AAAC;IAEH,MAAMa,WAAW,GAAG,MAAMP,KAAK,CAACQ,QAAQ,CAACJ,MAAM,EAAE;QAC/CK,KAAK,EAAE,KAAK;KACb,CAAC,AAAC;IAEH,MAAMC,UAAU,GAAG,OAAOC,MAAqB,GAA4B;QACzE,MAAMC,OAAO,GAAG,CAAC,OAAO,EAAE/B,WAAW,EAAE,CAAC,CAAC,EAAE8B,MAAM,CAAC1B,QAAQ,CAAC,CAAC,AAAC;QAC7D,MAAM4B,QAAQ,GAAG1B,CAAAA,GAAAA,cAAqB,AAA6B,CAAA,sBAA7B,CAACO,UAAU,EAAEiB,MAAM,CAAC1B,QAAQ,CAAC,AAAC;YAM7D0B,IAAU,EACOA,OAAa;QANrC,MAAMG,aAAa,GAAuB;YACxC,GAAGZ,MAAM,CAACa,sBAAsB;YAChCC,UAAU,EAAE,QAAQ;YACpB/B,QAAQ,EAAE0B,MAAM,CAAC1B,QAAQ;YACzBgC,SAAS,EAAEN,MAAM,CAACO,UAAU;YAC5BC,GAAG,EAAER,CAAAA,IAAU,GAAVA,MAAM,CAACQ,GAAG,YAAVR,IAAU,GAAI,KAAK;YACxBS,MAAM,EAAE,CAACP,QAAQ,IAAI,CAACF,CAAAA,OAAa,GAAbA,MAAM,CAACS,MAAM,YAAbT,OAAa,GAAI,CAACA,MAAM,CAACQ,GAAG,CAAC;YACnDE,eAAe,EAAE,KAAK;YACtBC,YAAY,EAAEX,MAAM,CAACW,YAAY;YACjCC,qBAAqB,EAAEnB,MAAM,CAACoB,UAAU,CAACD,qBAAqB;YAC9DE,UAAU,EAAE,CAACC,oBAA4B,EAAEC,cAAsB,GAAK;gBACpE,IAAI,CAAChC,OAAO,CAACiC,KAAK,EAAE;oBAClBvB,QAAQ,CAACwB,MAAM,CAAC;wBACdjB,OAAO;wBACPkB,IAAI,EAAE,6BAA6B;wBACnCJ,oBAAoB;wBACpBC,cAAc;qBACf,CAAC,CAAC;iBACJ;aACF;SACF,AAAC;QACF,MAAMI,aAAa,GAAG;YACpB,GAAGjB,aAAa;YAChBF,OAAO;SACR,AAAC;QACFP,QAAQ,CAACwB,MAAM,CAAC;YACdjB,OAAO;YACPkB,IAAI,EAAE,sBAAsB;YAC5BC,aAAa;SACd,CAAC,CAAC;QACH,IAAI;YACF,MAAM,EAAEC,IAAI,CAAA,EAAEjC,GAAG,CAAA,EAAE,GAAG,MAAMQ,WAAW,CAAC0B,KAAK,CAACnB,aAAa,CAAC,AAAC;YAC7D,MAAM,CAACoB,MAAM,EAAEC,GAAG,CAAC,GAAG,MAAMtC,OAAO,CAACC,GAAG,CAAC;gBACtCS,WAAW,CAAC6B,SAAS,CAACtB,aAAa,CAAC;gBACpCuB,CAAAA,GAAAA,yBAAwB,AAAiD,CAAA,yBAAjD,CAACjC,MAAM,EAAEG,WAAW,CAAC+B,UAAU,EAAE,EAAExB,aAAa,CAAC;aAC1E,CAAC,AAAC;YAEHT,QAAQ,CAACwB,MAAM,CAAC;gBACdjB,OAAO;gBACPkB,IAAI,EAAE,mBAAmB;aAC1B,CAAC,CAAC;YACH,OAAO;gBAAEE,IAAI;gBAAEjC,GAAG;gBAAEmC,MAAM,EAAEA,MAAM;gBAA0CC,GAAG;aAAE,CAAC;SACnF,CAAC,OAAOI,KAAK,EAAE;YACdlC,QAAQ,CAACwB,MAAM,CAAC;gBACdjB,OAAO;gBACPkB,IAAI,EAAE,qBAAqB;aAC5B,CAAC,CAAC;YAEH,MAAMS,KAAK,CAAC;SACb;KACF,AAAC;IAEF,MAAMC,yBAAyB,GAAG,OAChC7B,MAAqB,EACrB8B,YAA0B,GACA;QAC1B,MAAM,EAAExD,QAAQ,CAAA,EAAE,GAAG0B,MAAM,AAAC;QAC5B,MAAMzB,eAAe,GAAGC,CAAAA,GAAAA,cAAqB,AAAsB,CAAA,sBAAtB,CAACO,UAAU,EAAET,QAAQ,CAAC,AAAC;QACpE,IAAIC,eAAe,EAAE;YACnB,MAAMwD,WAAW,GAAGC,MAAK,QAAA,CAACC,IAAI,CAC5B;gBAAEC,GAAG,EAAE,KAAK;gBAAEC,OAAO,EAAE,SAAS;gBAAEC,GAAG,EAAE,KAAK;aAAE,CAAC9D,QAAQ,CAAC,IAAIA,QAAQ,CACrE,AAAC;YAEFoB,QAAQ,CAAC2C,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAEP,WAAW,CAAC,wCAAwC,CAAC,CAAC,CAAC;gBAM9E/B,OAAa;YAJf,MAAMuC,kBAAkB,GAAG,MAAMC,CAAAA,GAAAA,cAAsB,AAKtD,CAAA,uBALsD,CACrDpE,WAAW,EACX0D,YAAY,CAACT,IAAI,EACjBS,YAAY,CAAC1C,GAAG,EAChBY,CAAAA,OAAa,GAAbA,MAAM,CAACS,MAAM,YAAbT,OAAa,GAAI,CAACA,MAAM,CAACQ,GAAG,CAC7B,AAAC;YACFsB,YAAY,CAACW,oBAAoB,GAAGF,kBAAkB,CAACG,GAAG,CAAC;YAC3DZ,YAAY,CAACa,eAAe,GAAGJ,kBAAkB,CAACK,SAAS,CAAC;SAC7D;QACD,OAAOd,YAAY,CAAC;KACrB,AAAC;IAEF,IAAI;QACF,MAAMe,mBAAmB,GAAG,MAAM3D,OAAO,CAACC,GAAG,CAACF,OAAO,CAACG,GAAG,CAAC,CAACY,MAAM,GAAKD,UAAU,CAACC,MAAM,CAAC;QAAA,CAAC,CAAC,AAAC;QAC3F,MAAM8C,aAAa,GAAmB,EAAE,AAAC;QACzC,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9D,OAAO,CAAC+D,MAAM,EAAE,EAAED,CAAC,CAAE;YACvC,sEAAsE;YACtE,qCAAqC;YACrCD,aAAa,CAACG,IAAI,CAAC,MAAMpB,yBAAyB,CAAC5C,OAAO,CAAC8D,CAAC,CAAC,EAAEF,mBAAmB,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;SACzF;QACD,OAAOD,aAAa,CAAC;KACtB,CAAC,OAAOlB,KAAK,EAAE;QACd,gEAAgE;QAChEsB,OAAO,CAACZ,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,MAAMV,KAAK,CAAC;KACb,QAAS;QACRhC,WAAW,CAACuD,GAAG,EAAE,CAAC;KACnB;CACF"}