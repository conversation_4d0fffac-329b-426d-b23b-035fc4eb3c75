{"version": 3, "sources": ["../../../src/export/getPublicExpoManifest.ts"], "sourcesContent": ["import { ExpoAppManifest, getConfig } from '@expo/config';\n\nimport { env } from '../utils/env';\nimport { CommandError } from '../utils/errors';\nimport { getResolvedLocalesAsync } from './getResolvedLocales';\n\n/** Get the public Expo manifest from the local project config. */\nexport async function getPublicExpoManifestAsync(projectRoot: string): Promise<ExpoAppManifest> {\n  // Read the config in public mode which strips the `hooks`.\n  const { exp } = getConfig(projectRoot, {\n    isPublicConfig: true,\n    // This shouldn't be needed since the CLI is vendored in `expo`.\n    skipSDKVersionRequirement: true,\n  });\n\n  // Only allow projects to be published with UNVERSIONED if a correct token is set in env\n  if (exp.sdkVersion === 'UNVERSIONED' && !env.EXPO_SKIP_MANIFEST_VALIDATION_TOKEN) {\n    throw new CommandError('INVALID_OPTIONS', 'Cannot publish with sdkVersion UNVERSIONED.');\n  }\n\n  return {\n    ...exp,\n    locales: await getResolvedLocalesAsync(projectRoot, exp),\n    sdkVersion: exp.sdkVersion!,\n  };\n}\n"], "names": ["getPublicExpoManifestAsync", "projectRoot", "exp", "getConfig", "isPublicConfig", "skipSDKVersionRequirement", "sdkVersion", "env", "EXPO_SKIP_MANIFEST_VALIDATION_TOKEN", "CommandError", "locales", "getResolvedLocalesAsync"], "mappings": "AAAA;;;;QAOsBA,0BAA0B,GAA1BA,0BAA0B;AAPL,IAAA,OAAc,WAAd,cAAc,CAAA;AAErC,IAAA,IAAc,WAAd,cAAc,CAAA;AACL,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACN,IAAA,mBAAsB,WAAtB,sBAAsB,CAAA;AAGvD,eAAeA,0BAA0B,CAACC,WAAmB,EAA4B;IAC9F,2DAA2D;IAC3D,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAIvB,CAAA,UAJuB,CAACF,WAAW,EAAE;QACrCG,cAAc,EAAE,IAAI;QACpB,gEAAgE;QAChEC,yBAAyB,EAAE,IAAI;KAChC,CAAC,AAAC;IAEH,wFAAwF;IACxF,IAAIH,GAAG,CAACI,UAAU,KAAK,aAAa,IAAI,CAACC,IAAG,IAAA,CAACC,mCAAmC,EAAE;QAChF,MAAM,IAAIC,OAAY,aAAA,CAAC,iBAAiB,EAAE,6CAA6C,CAAC,CAAC;KAC1F;IAED,OAAO;QACL,GAAGP,GAAG;QACNQ,OAAO,EAAE,MAAMC,CAAAA,GAAAA,mBAAuB,AAAkB,CAAA,wBAAlB,CAACV,WAAW,EAAEC,GAAG,CAAC;QACxDI,UAAU,EAAEJ,GAAG,CAACI,UAAU;KAC3B,CAAC;CACH"}