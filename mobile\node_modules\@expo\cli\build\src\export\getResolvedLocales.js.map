{"version": 3, "sources": ["../../../src/export/getResolvedLocales.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport Json<PERSON><PERSON>, { JSONObject } from '@expo/json-file';\nimport path from 'path';\n\nimport { CommandError } from '../utils/errors';\n\ntype LocaleMap = Record<string, JSONObject>;\n\n// Similar to how we resolve locales in `@expo/config-plugins`\nexport async function getResolvedLocalesAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'locales'>\n): Promise<LocaleMap> {\n  if (!exp.locales) {\n    return {};\n  }\n\n  const locales: LocaleMap = {};\n  for (const [lang, localeJsonPath] of Object.entries(exp.locales)) {\n    if (typeof localeJsonPath === 'string') {\n      try {\n        locales[lang] = await JsonFile.readAsync(path.join(projectRoot, localeJsonPath));\n      } catch (error: any) {\n        throw new CommandError('EXPO_CONFIG', JSON.stringify(error));\n      }\n    } else {\n      // In the off chance that someone defined the locales json in the config, pass it directly to the object.\n      // We do this to make the types more elegant.\n      locales[lang] = localeJsonPath;\n    }\n  }\n  return locales;\n}\n"], "names": ["getResolvedLocalesAsync", "projectRoot", "exp", "locales", "lang", "localeJsonPath", "Object", "entries", "JsonFile", "readAsync", "path", "join", "error", "CommandError", "JSON", "stringify"], "mappings": "AAAA;;;;QASsBA,uBAAuB,GAAvBA,uBAAuB;AARR,IAAA,SAAiB,kCAAjB,iBAAiB,EAAA;AACrC,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEM,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;;;;;;AAKvC,eAAeA,uBAAuB,CAC3CC,WAAmB,EACnBC,GAAgC,EACZ;IACpB,IAAI,CAACA,GAAG,CAACC,OAAO,EAAE;QAChB,OAAO,EAAE,CAAC;KACX;IAED,MAAMA,OAAO,GAAc,EAAE,AAAC;IAC9B,KAAK,MAAM,CAACC,IAAI,EAAEC,cAAc,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,GAAG,CAACC,OAAO,CAAC,CAAE;QAChE,IAAI,OAAOE,cAAc,KAAK,QAAQ,EAAE;YACtC,IAAI;gBACFF,OAAO,CAACC,IAAI,CAAC,GAAG,MAAMI,SAAQ,QAAA,CAACC,SAAS,CAACC,KAAI,QAAA,CAACC,IAAI,CAACV,WAAW,EAAEI,cAAc,CAAC,CAAC,CAAC;aAClF,CAAC,OAAOO,KAAK,EAAO;gBACnB,MAAM,IAAIC,OAAY,aAAA,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,CAAC,CAAC;aAC9D;SACF,MAAM;YACL,yGAAyG;YACzG,6CAA6C;YAC7CT,OAAO,CAACC,IAAI,CAAC,GAAGC,cAAc,CAAC;SAChC;KACF;IACD,OAAOF,OAAO,CAAC;CAChB"}