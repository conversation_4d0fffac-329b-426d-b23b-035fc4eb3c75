{"version": 3, "sources": ["../../../src/export/html.ts"], "sourcesContent": ["// <link rel=\"preload\" href=\"/_expo/static/css/xxxxxx.css\" as=\"style\">\nexport function appendLinkToHtml(\n  html: string,\n  links: { rel: string; href: string; as?: string }[]\n) {\n  return html.replace(\n    '</head>',\n    links\n      .map((link) => {\n        let linkTag = `<link rel=\"${link.rel}\"`;\n\n        if (link.href) linkTag += ` href=\"${link.href}\"`;\n        if (link.as) linkTag += ` as=\"${link.as}\"`;\n\n        linkTag += '>';\n\n        return linkTag;\n      })\n      .join('') + '</head>'\n  );\n}\n\nexport function appendScriptsToHtml(html: string, scripts: string[]) {\n  return html.replace(\n    '</body>',\n    scripts.map((script) => `<script src=\"${script}\" defer></script>`).join('') + '</body>'\n  );\n}\n"], "names": ["appendLinkToHtml", "appendScriptsToHtml", "html", "links", "replace", "map", "link", "linkTag", "rel", "href", "as", "join", "scripts", "script"], "mappings": "AACA;;;;QAAgBA,gBAAgB,GAAhBA,gBAAgB;QAqBhBC,mBAAmB,GAAnBA,mBAAmB;AArB5B,SAASD,gBAAgB,CAC9BE,IAAY,EACZC,KAAmD,EACnD;IACA,OAAOD,IAAI,CAACE,OAAO,CACjB,SAAS,EACTD,KAAK,CACFE,GAAG,CAAC,CAACC,IAAI,GAAK;QACb,IAAIC,OAAO,GAAG,CAAC,WAAW,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,AAAC;QAExC,IAAIF,IAAI,CAACG,IAAI,EAAEF,OAAO,IAAI,CAAC,OAAO,EAAED,IAAI,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC;QACjD,IAAIH,IAAI,CAACI,EAAE,EAAEH,OAAO,IAAI,CAAC,KAAK,EAAED,IAAI,CAACI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE3CH,OAAO,IAAI,GAAG,CAAC;QAEf,OAAOA,OAAO,CAAC;KAChB,CAAC,CACDI,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CACxB,CAAC;CACH;AAEM,SAASV,mBAAmB,CAACC,IAAY,EAAEU,OAAiB,EAAE;IACnE,OAAOV,IAAI,CAACE,OAAO,CACjB,SAAS,EACTQ,OAAO,CAACP,GAAG,CAAC,CAACQ,MAAM,GAAK,CAAC,aAAa,EAAEA,MAAM,CAAC,iBAAiB,CAAC;IAAA,CAAC,CAACF,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CACxF,CAAC;CACH"}