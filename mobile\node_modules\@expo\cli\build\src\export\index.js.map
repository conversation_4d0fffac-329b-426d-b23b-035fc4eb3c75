{"version": 3, "sources": ["../../../src/export/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoExport: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--clear': Boolean,\n      '--dump-assetmap': Boolean,\n      '--dev': Boolean,\n      '--dump-sourcemap': Boolean,\n      '--max-workers': Number,\n      '--output-dir': String,\n      '--platform': [String],\n      '--no-minify': <PERSON>ole<PERSON>,\n\n      // Hack: This is added because EAS CLI always includes the flag.\n      // If supplied, we'll do nothing with the value, but at least the process won't crash.\n      // Note that we also don't show this value in the `--help` prompt since we don't want people to use it.\n      '--experimental-bundle': <PERSON><PERSON><PERSON>,\n\n      // Aliases\n      '-h': '--help',\n      // '-s': '--dump-sourcemap',\n      // '-d': '--dump-assetmap',\n      '-c': '--clear',\n      '-p': '--platform',\n      // Interop with Metro docs and RedBox errors.\n      '--reset-cache': '--clear',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Export the static files of the app for hosting it on a web server`,\n      chalk`npx expo export {dim <dir>}`,\n      [\n        chalk`<dir>                      Directory of the Expo project. {dim Default: Current working directory}`,\n        `--dev                      Configure static files for developing locally using a non-https server`,\n        chalk`--output-dir <dir>         The directory to export the static files to. {dim Default: dist}`,\n        `--max-workers <number>     Maximum number of tasks to allow the bundler to spawn`,\n        `--dump-assetmap            Dump the asset map for further processing`,\n        `--dump-sourcemap           Dump the source map for debugging the JS bundle`,\n        chalk`-p, --platform <platform>  Options: android, ios, web, all. {dim Default: all}`,\n        `--no-minify                Prevent minifying source`,\n        `-c, --clear                Clear the bundler cache`,\n        `-h, --help                 Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const projectRoot = getProjectRoot(args);\n  const { resolveOptionsAsync } = await import('./resolveOptions');\n  const options = await resolveOptionsAsync(projectRoot, args).catch(logCmdError);\n\n  const { exportAsync } = await import('./exportAsync');\n  return exportAsync(projectRoot, options).catch(logCmdError);\n};\n"], "names": ["expoExport", "argv", "args", "assertArgs", "Boolean", "Number", "String", "printHelp", "chalk", "join", "projectRoot", "getProjectRoot", "resolveOptionsAsync", "options", "catch", "logCmdError", "exportAsync"], "mappings": "AAAA;;;;;;AACkB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAG6B,IAAA,KAAe,WAAf,eAAe,CAAA;AACzC,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtC,MAAMA,UAAU,GAAY,OAAOC,IAAI,GAAK;IACjD,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,KAAU,AA4BtB,CAAA,WA5BsB,CACrB;QACE,QAAQ;QACR,QAAQ,EAAEC,OAAO;QACjB,SAAS,EAAEA,OAAO;QAClB,iBAAiB,EAAEA,OAAO;QAC1B,OAAO,EAAEA,OAAO;QAChB,kBAAkB,EAAEA,OAAO;QAC3B,eAAe,EAAEC,MAAM;QACvB,cAAc,EAAEC,MAAM;QACtB,YAAY,EAAE;YAACA,MAAM;SAAC;QACtB,aAAa,EAAEF,OAAO;QAEtB,gEAAgE;QAChE,sFAAsF;QACtF,uGAAuG;QACvG,uBAAuB,EAAEA,OAAO;QAEhC,UAAU;QACV,IAAI,EAAE,QAAQ;QACd,4BAA4B;QAC5B,2BAA2B;QAC3B,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,YAAY;QAClB,6CAA6C;QAC7C,eAAe,EAAE,SAAS;KAC3B,EACDH,IAAI,CACL,AAAC;IAEF,IAAIC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBK,CAAAA,GAAAA,KAAS,AAeR,CAAA,UAfQ,CACP,CAAC,iEAAiE,CAAC,EACnEC,MAAK,QAAA,CAAC,2BAA2B,CAAC,EAClC;YACEA,MAAK,QAAA,CAAC,kGAAkG,CAAC;YACzG,CAAC,iGAAiG,CAAC;YACnGA,MAAK,QAAA,CAAC,2FAA2F,CAAC;YAClG,CAAC,gFAAgF,CAAC;YAClF,CAAC,oEAAoE,CAAC;YACtE,CAAC,0EAA0E,CAAC;YAC5EA,MAAK,QAAA,CAAC,8EAA8E,CAAC;YACrF,CAAC,mDAAmD,CAAC;YACrD,CAAC,kDAAkD,CAAC;YACpD,CAAC,qCAAqC,CAAC;SACxC,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;KACH;IAED,MAAMC,WAAW,GAAGC,CAAAA,GAAAA,KAAc,AAAM,CAAA,eAAN,CAACT,IAAI,CAAC,AAAC;IACzC,MAAM,EAAEU,mBAAmB,CAAA,EAAE,GAAG,MAAM;+CAAO,kBAAkB;MAAC,AAAC;IACjE,MAAMC,OAAO,GAAG,MAAMD,mBAAmB,CAACF,WAAW,EAAER,IAAI,CAAC,CAACY,KAAK,CAACC,OAAW,YAAA,CAAC,AAAC;IAEhF,MAAM,EAAEC,WAAW,CAAA,EAAE,GAAG,MAAM;+CAAO,eAAe;MAAC,AAAC;IACtD,OAAOA,WAAW,CAACN,WAAW,EAAEG,OAAO,CAAC,CAACC,KAAK,CAACC,OAAW,YAAA,CAAC,CAAC;CAC7D,AAAC;QAxDWf,UAAU,GAAVA,UAAU"}