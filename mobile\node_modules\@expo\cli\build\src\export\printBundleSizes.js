"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.printBundleSizes = printBundleSizes;
exports.createFilesTable = createFilesTable;
var _chalk = _interopRequireDefault(require("chalk"));
var _prettyBytes = _interopRequireDefault(require("pretty-bytes"));
var _textTable = _interopRequireDefault(require("text-table"));
var Log = _interopRequireWildcard(require("../log"));
var _ansi = require("../utils/ansi");
var _link = require("../utils/link");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _interopRequireWildcard(obj) {
    if (obj && obj.__esModule) {
        return obj;
    } else {
        var newObj = {};
        if (obj != null) {
            for(var key in obj){
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};
                    if (desc.get || desc.set) {
                        Object.defineProperty(newObj, key, desc);
                    } else {
                        newObj[key] = obj[key];
                    }
                }
            }
        }
        newObj.default = obj;
        return newObj;
    }
}
function printBundleSizes(bundles) {
    const files = [];
    for (const [platform, bundleOutput] of Object.entries(bundles)){
        if (bundleOutput.hermesBytecodeBundle) {
            files.push([
                _chalk.default.bold(`index.${platform}.hbc`),
                bundleOutput.hermesBytecodeBundle
            ]);
        } else if (bundleOutput.code) {
            files.push([
                _chalk.default.bold(`index.${platform}.js`),
                bundleOutput.code
            ]);
        }
        if (bundleOutput.hermesSourcemap) {
            files.push([
                _chalk.default.dim(`index.${platform}.hbc.map`),
                bundleOutput.hermesSourcemap
            ]);
        } else if (bundleOutput.map) {
            files.push([
                _chalk.default.dim(`index.${platform}.js.map`),
                bundleOutput.map
            ]);
        }
    }
    Log.log();
    Log.log(createFilesTable(files.sort((a, b)=>a[1].length - b[1].length
    )));
    Log.log();
    Log.log(_chalk.default`💡 JavaScript bundle sizes affect startup time. {dim ${(0, _link).learnMore(`https://expo.fyi/javascript-bundle-sizes`)}}`);
    Log.log();
    return files;
}
function createFilesTable(files) {
    const tableData = files.map((item, index)=>{
        const fileBranch = index === 0 ? files.length > 1 ? "\u250C" : "\u2500" : index === files.length - 1 ? "\u2514" : "\u251C";
        return [
            `${fileBranch} ${item[0]}`,
            (0, _prettyBytes).default(Buffer.byteLength(item[1], "utf8"))
        ];
    });
    var ref;
    return (0, _textTable).default([
        [
            "Bundle",
            "Size"
        ].map((v)=>_chalk.default.underline(v)
        ),
        ...tableData
    ], {
        align: [
            "l",
            "r"
        ],
        stringLength: (str)=>{
            var ref1;
            return (ref = (ref1 = (0, _ansi).stripAnsi(str)) == null ? void 0 : ref1.length) != null ? ref : 0;
        }
    });
}

//# sourceMappingURL=printBundleSizes.js.map