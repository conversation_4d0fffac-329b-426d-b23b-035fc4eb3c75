{"version": 3, "sources": ["../../../src/export/printBundleSizes.ts"], "sourcesContent": ["import { Platform } from '@expo/config';\nimport chalk from 'chalk';\nimport prettyBytes from 'pretty-bytes';\nimport table from 'text-table';\n\nimport * as Log from '../log';\nimport { stripAnsi } from '../utils/ansi';\nimport { learnMore } from '../utils/link';\nimport { BundleOutput } from './fork-bundleAsync';\n\nexport function printBundleSizes(bundles: Partial<Record<Platform, BundleOutput>>) {\n  const files: [string, string | Uint8Array][] = [];\n\n  for (const [platform, bundleOutput] of Object.entries(bundles) as [\n    Platform,\n    Pick<BundleOutput, 'hermesBytecodeBundle' | 'code' | 'hermesSourcemap' | 'map'>\n  ][]) {\n    if (bundleOutput.hermesBytecodeBundle) {\n      files.push([chalk.bold(`index.${platform}.hbc`), bundleOutput.hermesBytecodeBundle]);\n    } else if (bundleOutput.code) {\n      files.push([chalk.bold(`index.${platform}.js`), bundleOutput.code]);\n    }\n    if (bundleOutput.hermesSourcemap) {\n      files.push([chalk.dim(`index.${platform}.hbc.map`), bundleOutput.hermesSourcemap]);\n    } else if (bundleOutput.map) {\n      files.push([chalk.dim(`index.${platform}.js.map`), bundleOutput.map]);\n    }\n  }\n\n  Log.log();\n  Log.log(createFilesTable(files.sort((a, b) => a[1].length - b[1].length)));\n  Log.log();\n  Log.log(\n    chalk`💡 JavaScript bundle sizes affect startup time. {dim ${learnMore(\n      `https://expo.fyi/javascript-bundle-sizes`\n    )}}`\n  );\n  Log.log();\n\n  return files;\n}\n\nexport function createFilesTable(files: [string, string | Uint8Array][]): string {\n  const tableData = files.map((item, index) => {\n    const fileBranch =\n      index === 0 ? (files.length > 1 ? '┌' : '─') : index === files.length - 1 ? '└' : '├';\n\n    return [`${fileBranch} ${item[0]}`, prettyBytes(Buffer.byteLength(item[1], 'utf8'))];\n  });\n  return table([['Bundle', 'Size'].map((v) => chalk.underline(v)), ...tableData], {\n    align: ['l', 'r'],\n    stringLength: (str) => stripAnsi(str)?.length ?? 0,\n  });\n}\n"], "names": ["printBundleSizes", "createFilesTable", "Log", "bundles", "files", "platform", "bundleOutput", "Object", "entries", "hermesBytecodeBundle", "push", "chalk", "bold", "code", "hermesSourcemap", "dim", "map", "log", "sort", "a", "b", "length", "learnMore", "tableData", "item", "index", "fileBranch", "prettyBytes", "<PERSON><PERSON><PERSON>", "byteLength", "stripAnsi", "table", "v", "underline", "align", "stringLength", "str"], "mappings": "AAAA;;;;QAUgBA,gBAAgB,GAAhBA,gBAAgB;QAgChBC,gBAAgB,GAAhBA,gBAAgB;AAzCd,IAAA,MAAO,kCAAP,OAAO,EAAA;AACD,IAAA,YAAc,kCAAd,cAAc,EAAA;AACpB,IAAA,UAAY,kCAAZ,YAAY,EAAA;AAElBC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACW,IAAA,KAAe,WAAf,eAAe,CAAA;AACf,IAAA,KAAe,WAAf,eAAe,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGlC,SAASF,gBAAgB,CAACG,OAAgD,EAAE;IACjF,MAAMC,KAAK,GAAoC,EAAE,AAAC;IAElD,KAAK,MAAM,CAACC,QAAQ,EAAEC,YAAY,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,OAAO,CAAC,CAGzD;QACH,IAAIG,YAAY,CAACG,oBAAoB,EAAE;YACrCL,KAAK,CAACM,IAAI,CAAC;gBAACC,MAAK,QAAA,CAACC,IAAI,CAAC,CAAC,MAAM,EAAEP,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAAEC,YAAY,CAACG,oBAAoB;aAAC,CAAC,CAAC;SACtF,MAAM,IAAIH,YAAY,CAACO,IAAI,EAAE;YAC5BT,KAAK,CAACM,IAAI,CAAC;gBAACC,MAAK,QAAA,CAACC,IAAI,CAAC,CAAC,MAAM,EAAEP,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAAEC,YAAY,CAACO,IAAI;aAAC,CAAC,CAAC;SACrE;QACD,IAAIP,YAAY,CAACQ,eAAe,EAAE;YAChCV,KAAK,CAACM,IAAI,CAAC;gBAACC,MAAK,QAAA,CAACI,GAAG,CAAC,CAAC,MAAM,EAAEV,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAAEC,YAAY,CAACQ,eAAe;aAAC,CAAC,CAAC;SACpF,MAAM,IAAIR,YAAY,CAACU,GAAG,EAAE;YAC3BZ,KAAK,CAACM,IAAI,CAAC;gBAACC,MAAK,QAAA,CAACI,GAAG,CAAC,CAAC,MAAM,EAAEV,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAAEC,YAAY,CAACU,GAAG;aAAC,CAAC,CAAC;SACvE;KACF;IAEDd,GAAG,CAACe,GAAG,EAAE,CAAC;IACVf,GAAG,CAACe,GAAG,CAAChB,gBAAgB,CAACG,KAAK,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC,CAAC,CAAC,CAACE,MAAM,GAAGD,CAAC,CAAC,CAAC,CAAC,CAACC,MAAM;IAAA,CAAC,CAAC,CAAC,CAAC;IAC3EnB,GAAG,CAACe,GAAG,EAAE,CAAC;IACVf,GAAG,CAACe,GAAG,CACLN,MAAK,QAAA,CAAC,uDAAuD,EAAEW,CAAAA,GAAAA,KAAS,AAEvE,CAAA,UAFuE,CACtE,CAAC,wCAAwC,CAAC,CAC3C,CAAC,CAAC,CAAC,CACL,CAAC;IACFpB,GAAG,CAACe,GAAG,EAAE,CAAC;IAEV,OAAOb,KAAK,CAAC;CACd;AAEM,SAASH,gBAAgB,CAACG,KAAsC,EAAU;IAC/E,MAAMmB,SAAS,GAAGnB,KAAK,CAACY,GAAG,CAAC,CAACQ,IAAI,EAAEC,KAAK,GAAK;QAC3C,MAAMC,UAAU,GACdD,KAAK,KAAK,CAAC,GAAIrB,KAAK,CAACiB,MAAM,GAAG,CAAC,GAAG,QAAG,GAAK,QAAG,GAAMI,KAAK,KAAKrB,KAAK,CAACiB,MAAM,GAAG,CAAC,GAAG,QAAG,GAAK,QAAG,AAAC;QAExF,OAAO;YAAC,CAAC,EAAEK,UAAU,CAAC,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAAEG,CAAAA,GAAAA,YAAW,AAAoC,CAAA,QAApC,CAACC,MAAM,CAACC,UAAU,CAACL,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;SAAC,CAAC;KACtF,CAAC,AAAC;QAGsBM,GAAsB;IAF/C,OAAOC,CAAAA,GAAAA,UAAK,AAGV,CAAA,QAHU,CAAC;QAAC;YAAC,QAAQ;YAAE,MAAM;SAAC,CAACf,GAAG,CAAC,CAACgB,CAAC,GAAKrB,MAAK,QAAA,CAACsB,SAAS,CAACD,CAAC,CAAC;QAAA,CAAC;WAAKT,SAAS;KAAC,EAAE;QAC9EW,KAAK,EAAE;YAAC,GAAG;YAAE,GAAG;SAAC;QACjBC,YAAY,EAAE,CAACC,GAAG;gBAAKN,IAAc;YAAdA,OAAAA,CAAAA,GAAsB,GAAtBA,CAAAA,IAAc,GAAdA,CAAAA,GAAAA,KAAS,AAAK,CAAA,UAAL,CAACM,GAAG,CAAC,SAAQ,GAAtBN,KAAAA,CAAsB,GAAtBA,IAAc,CAAET,MAAM,YAAtBS,GAAsB,GAAI,CAAC,CAAA;SAAA;KACnD,CAAC,CAAC;CACJ"}