{"version": 3, "sources": ["../../../src/export/publicFolder.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { env } from '../utils/env';\n\nconst debug = require('debug')('expo:public-folder') as typeof console.log;\n\n/** @returns the file system path for a user-defined file in the public folder. */\nexport function getUserDefinedFile(projectRoot: string, possiblePaths: string[]): string | null {\n  const publicPath = path.join(projectRoot, env.EXPO_PUBLIC_FOLDER);\n\n  for (const possiblePath of possiblePaths) {\n    const fullPath = path.join(publicPath, possiblePath);\n    if (fs.existsSync(fullPath)) {\n      debug(`Found user-defined public file: ` + possiblePath);\n      return fullPath;\n    }\n  }\n\n  return null;\n}\n"], "names": ["getUserDefinedFile", "debug", "require", "projectRoot", "possiblePaths", "publicPath", "path", "join", "env", "EXPO_PUBLIC_FOLDER", "<PERSON><PERSON><PERSON>", "fullPath", "fs", "existsSync"], "mappings": "AAAA;;;;QAQgBA,kBAAkB,GAAlBA,kBAAkB;AARnB,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEH,IAAA,IAAc,WAAd,cAAc,CAAA;;;;;;AAElC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,AAAsB,AAAC;AAGpE,SAASF,kBAAkB,CAACG,WAAmB,EAAEC,aAAuB,EAAiB;IAC9F,MAAMC,UAAU,GAAGC,KAAI,QAAA,CAACC,IAAI,CAACJ,WAAW,EAAEK,IAAG,IAAA,CAACC,kBAAkB,CAAC,AAAC;IAElE,KAAK,MAAMC,YAAY,IAAIN,aAAa,CAAE;QACxC,MAAMO,QAAQ,GAAGL,KAAI,QAAA,CAACC,IAAI,CAACF,UAAU,EAAEK,YAAY,CAAC,AAAC;QACrD,IAAIE,GAAE,QAAA,CAACC,UAAU,CAACF,QAAQ,CAAC,EAAE;YAC3BV,KAAK,CAAC,CAAC,gCAAgC,CAAC,GAAGS,YAAY,CAAC,CAAC;YACzD,OAAOC,QAAQ,CAAC;SACjB;KACF;IAED,OAAO,IAAI,CAAC;CACb"}