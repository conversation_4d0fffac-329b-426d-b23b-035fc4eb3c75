{"version": 3, "sources": ["../../../src/export/resolveOptions.ts"], "sourcesContent": ["import { getConfig, Platform } from '@expo/config';\n\nimport { getPlatformBundlers, PlatformBundlers } from '../start/server/platformBundlers';\nimport { CommandError } from '../utils/errors';\n\nexport type Options = {\n  outputDir: string;\n  platforms: Platform[];\n  maxWorkers?: number;\n  dev: boolean;\n  clear: boolean;\n  minify: boolean;\n  dumpAssetmap: boolean;\n  dumpSourcemap: boolean;\n};\n\n/** Returns an array of platforms based on the input platform identifier and runtime constraints. */\nexport function resolvePlatformOption(\n  platformBundlers: PlatformBundlers,\n  platform: string[] = ['all']\n): Platform[] {\n  const platformsAvailable: Partial<PlatformBundlers> = Object.fromEntries(\n    Object.entries(platformBundlers).filter(([, bundler]) => bundler === 'metro')\n  );\n\n  if (!Object.keys(platformsAvailable).length) {\n    throw new CommandError(\n      `No platforms are configured to use the Metro bundler in the project Expo config.`\n    );\n  }\n\n  const assertPlatformBundler = (platform: Platform): Platform => {\n    if (!platformsAvailable[platform]) {\n      throw new CommandError(\n        'BAD_ARGS',\n        `Platform \"${platform}\" is not configured to use the Metro bundler in the project Expo config.`\n      );\n    }\n\n    return platform;\n  };\n\n  const knownPlatforms = ['android', 'ios', 'web'] as Platform[];\n  const assertPlatformIsKnown = (platform: string): Platform => {\n    if (!knownPlatforms.includes(platform as Platform)) {\n      throw new CommandError(\n        `Unsupported platform \"${platform}\". Options are: ${knownPlatforms.join(',')},all`\n      );\n    }\n\n    return platform as Platform;\n  };\n\n  return (\n    platform\n      // Expand `all` to all available platforms.\n      .map((platform) => (platform === 'all' ? Object.keys(platformsAvailable) : platform))\n      .flat()\n      // Remove duplicated platforms\n      .filter((platform, index, list) => list.indexOf(platform) === index)\n      // Assert platforms are valid\n      .map((platform) => assertPlatformIsKnown(platform))\n      .map((platform) => assertPlatformBundler(platform))\n  );\n}\n\nexport async function resolveOptionsAsync(projectRoot: string, args: any): Promise<Options> {\n  const { exp } = getConfig(projectRoot, { skipPlugins: true, skipSDKVersionRequirement: true });\n  const platformBundlers = getPlatformBundlers(exp);\n\n  return {\n    platforms: resolvePlatformOption(platformBundlers, args['--platform']),\n    outputDir: args['--output-dir'] ?? 'dist',\n    minify: !args['--no-minify'],\n    clear: !!args['--clear'],\n    dev: !!args['--dev'],\n    maxWorkers: args['--max-workers'],\n    dumpAssetmap: !!args['--dump-assetmap'],\n    dumpSourcemap: !!args['--dump-sourcemap'],\n  };\n}\n"], "names": ["resolvePlatformOption", "resolveOptionsAsync", "platformBundlers", "platform", "platformsAvailable", "Object", "fromEntries", "entries", "filter", "bundler", "keys", "length", "CommandError", "assertPlatformBundler", "knownPlatforms", "assertPlatformIsKnown", "includes", "join", "map", "flat", "index", "list", "indexOf", "projectRoot", "args", "exp", "getConfig", "skip<PERSON>lug<PERSON>", "skipSDKVersionRequirement", "getPlatformBundlers", "platforms", "outputDir", "minify", "clear", "dev", "maxWorkers", "dumpAssetmap", "dumpSourcemap"], "mappings": "AAAA;;;;QAiBgBA,qBAAqB,GAArBA,qBAAqB;QAiDfC,mBAAmB,GAAnBA,mBAAmB;AAlEL,IAAA,OAAc,WAAd,cAAc,CAAA;AAEI,IAAA,iBAAkC,WAAlC,kCAAkC,CAAA;AAC3D,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AAcvC,SAASD,qBAAqB,CACnCE,gBAAkC,EAClCC,SAAkB,GAAG;IAAC,KAAK;CAAC,EAChB;IACZ,MAAMC,kBAAkB,GAA8BC,MAAM,CAACC,WAAW,CACtED,MAAM,CAACE,OAAO,CAACL,gBAAgB,CAAC,CAACM,MAAM,CAAC,CAAC,GAAGC,OAAO,CAAC,GAAKA,OAAO,KAAK,OAAO;IAAA,CAAC,CAC9E,AAAC;IAEF,IAAI,CAACJ,MAAM,CAACK,IAAI,CAACN,kBAAkB,CAAC,CAACO,MAAM,EAAE;QAC3C,MAAM,IAAIC,OAAY,aAAA,CACpB,CAAC,gFAAgF,CAAC,CACnF,CAAC;KACH;IAED,MAAMC,qBAAqB,GAAG,CAACV,QAAkB,GAAe;QAC9D,IAAI,CAACC,kBAAkB,CAACD,QAAQ,CAAC,EAAE;YACjC,MAAM,IAAIS,OAAY,aAAA,CACpB,UAAU,EACV,CAAC,UAAU,EAAET,QAAQ,CAAC,wEAAwE,CAAC,CAChG,CAAC;SACH;QAED,OAAOA,QAAQ,CAAC;KACjB,AAAC;IAEF,MAAMW,cAAc,GAAG;QAAC,SAAS;QAAE,KAAK;QAAE,KAAK;KAAC,AAAc,AAAC;IAC/D,MAAMC,qBAAqB,GAAG,CAACZ,QAAgB,GAAe;QAC5D,IAAI,CAACW,cAAc,CAACE,QAAQ,CAACb,QAAQ,CAAa,EAAE;YAClD,MAAM,IAAIS,OAAY,aAAA,CACpB,CAAC,sBAAsB,EAAET,QAAQ,CAAC,gBAAgB,EAAEW,cAAc,CAACG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CACnF,CAAC;SACH;QAED,OAAOd,QAAQ,CAAa;KAC7B,AAAC;IAEF,OACEA,SAAQ,AACN,2CAA2C;KAC1Ce,GAAG,CAAC,CAACf,QAAQ,GAAMA,QAAQ,KAAK,KAAK,GAAGE,MAAM,CAACK,IAAI,CAACN,kBAAkB,CAAC,GAAGD,QAAQ;IAAC,CAAC,CACpFgB,IAAI,EAAE,AACP,8BAA8B;KAC7BX,MAAM,CAAC,CAACL,QAAQ,EAAEiB,KAAK,EAAEC,IAAI,GAAKA,IAAI,CAACC,OAAO,CAACnB,QAAQ,CAAC,KAAKiB,KAAK;IAAA,CAAC,AACpE,6BAA6B;KAC5BF,GAAG,CAAC,CAACf,QAAQ,GAAKY,qBAAqB,CAACZ,QAAQ,CAAC;IAAA,CAAC,CAClDe,GAAG,CAAC,CAACf,QAAQ,GAAKU,qBAAqB,CAACV,QAAQ,CAAC;IAAA,CAAC,CACrD;CACH;AAEM,eAAeF,mBAAmB,CAACsB,WAAmB,EAAEC,IAAS,EAAoB;IAC1F,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAAqE,CAAA,UAArE,CAACH,WAAW,EAAE;QAAEI,WAAW,EAAE,IAAI;QAAEC,yBAAyB,EAAE,IAAI;KAAE,CAAC,AAAC;IAC/F,MAAM1B,gBAAgB,GAAG2B,CAAAA,GAAAA,iBAAmB,AAAK,CAAA,oBAAL,CAACJ,GAAG,CAAC,AAAC;QAIrCD,GAAoB;IAFjC,OAAO;QACLM,SAAS,EAAE9B,qBAAqB,CAACE,gBAAgB,EAAEsB,IAAI,CAAC,YAAY,CAAC,CAAC;QACtEO,SAAS,EAAEP,CAAAA,GAAoB,GAApBA,IAAI,CAAC,cAAc,CAAC,YAApBA,GAAoB,GAAI,MAAM;QACzCQ,MAAM,EAAE,CAACR,IAAI,CAAC,aAAa,CAAC;QAC5BS,KAAK,EAAE,CAAC,CAACT,IAAI,CAAC,SAAS,CAAC;QACxBU,GAAG,EAAE,CAAC,CAACV,IAAI,CAAC,OAAO,CAAC;QACpBW,UAAU,EAAEX,IAAI,CAAC,eAAe,CAAC;QACjCY,YAAY,EAAE,CAAC,CAACZ,IAAI,CAAC,iBAAiB,CAAC;QACvCa,aAAa,EAAE,CAAC,CAACb,IAAI,CAAC,kBAAkB,CAAC;KAC1C,CAAC;CACH"}