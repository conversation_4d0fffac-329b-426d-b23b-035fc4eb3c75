{"version": 3, "sources": ["../../../src/export/saveAssets.ts"], "sourcesContent": ["import { BundleAssetWithFileHashes } from '@expo/dev-server';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { chunk } from '../utils/array';\nimport { copyAsync } from '../utils/dir';\n\nconst debug = require('debug')('expo:export:saveAssets') as typeof console.log;\n\nexport type ManifestAsset = { fileHashes: string[]; files: string[]; hash: string };\n\nexport type Asset = ManifestAsset | BundleAssetWithFileHashes;\n\nfunction logAssetTask(projectRoot: string, action: 'uploading' | 'saving', pathName: string) {\n  debug(`${action} ${pathName}`);\n\n  const relativePath = pathName.replace(projectRoot, '');\n  Log.log(`${action} ${relativePath}`);\n}\n\nfunction collectAssetPaths(assets: Asset[]): Record<string, string> {\n  // Collect paths by key, also effectively handles duplicates in the array\n  const paths: { [fileHash: string]: string } = {};\n  assets.forEach((asset) => {\n    asset.files.forEach((path: string, index: number) => {\n      paths[asset.fileHashes[index]] = path;\n    });\n  });\n  return paths;\n}\n\nexport async function saveAssetsAsync(\n  projectRoot: string,\n  { assets, outputDir }: { assets: Asset[]; outputDir: string }\n) {\n  // Collect paths by key, also effectively handles duplicates in the array\n  const paths = collectAssetPaths(assets);\n\n  // save files one chunk at a time\n  for (const keys of chunk(Object.entries(paths), 5)) {\n    await Promise.all(\n      keys.map(([key, pathName]) => {\n        logAssetTask(projectRoot, 'saving', pathName);\n        // copy file over to assetPath\n        return copyAsync(pathName, path.join(outputDir, 'assets', key));\n      })\n    );\n  }\n  Log.log('Files successfully saved.');\n}\n"], "names": ["saveAssetsAsync", "Log", "debug", "require", "logAssetTask", "projectRoot", "action", "pathName", "relativePath", "replace", "log", "collectAssetPaths", "assets", "paths", "for<PERSON>ach", "asset", "files", "path", "index", "fileHashes", "outputDir", "keys", "chunk", "Object", "entries", "Promise", "all", "map", "key", "copyAsync", "join"], "mappings": "AAAA;;;;QA+BsBA,eAAe,GAAfA,eAAe;AA9BpB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEXC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACO,IAAA,MAAgB,WAAhB,gBAAgB,CAAA;AACZ,IAAA,IAAc,WAAd,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,wBAAwB,CAAC,AAAsB,AAAC;AAM/E,SAASC,YAAY,CAACC,WAAmB,EAAEC,MAA8B,EAAEC,QAAgB,EAAE;IAC3FL,KAAK,CAAC,CAAC,EAAEI,MAAM,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/B,MAAMC,YAAY,GAAGD,QAAQ,CAACE,OAAO,CAACJ,WAAW,EAAE,EAAE,CAAC,AAAC;IACvDJ,GAAG,CAACS,GAAG,CAAC,CAAC,EAAEJ,MAAM,CAAC,CAAC,EAAEE,YAAY,CAAC,CAAC,CAAC,CAAC;CACtC;AAED,SAASG,iBAAiB,CAACC,MAAe,EAA0B;IAClE,yEAAyE;IACzE,MAAMC,KAAK,GAAmC,EAAE,AAAC;IACjDD,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,GAAK;QACxBA,KAAK,CAACC,KAAK,CAACF,OAAO,CAAC,CAACG,IAAY,EAAEC,KAAa,GAAK;YACnDL,KAAK,CAACE,KAAK,CAACI,UAAU,CAACD,KAAK,CAAC,CAAC,GAAGD,IAAI,CAAC;SACvC,CAAC,CAAC;KACJ,CAAC,CAAC;IACH,OAAOJ,KAAK,CAAC;CACd;AAEM,eAAeb,eAAe,CACnCK,WAAmB,EACnB,EAAEO,MAAM,CAAA,EAAEQ,SAAS,CAAA,EAA0C,EAC7D;IACA,yEAAyE;IACzE,MAAMP,KAAK,GAAGF,iBAAiB,CAACC,MAAM,CAAC,AAAC;IAExC,iCAAiC;IACjC,KAAK,MAAMS,IAAI,IAAIC,CAAAA,GAAAA,MAAK,AAA0B,CAAA,MAA1B,CAACC,MAAM,CAACC,OAAO,CAACX,KAAK,CAAC,EAAE,CAAC,CAAC,CAAE;QAClD,MAAMY,OAAO,CAACC,GAAG,CACfL,IAAI,CAACM,GAAG,CAAC,CAAC,CAACC,GAAG,EAAErB,QAAQ,CAAC,GAAK;YAC5BH,YAAY,CAACC,WAAW,EAAE,QAAQ,EAAEE,QAAQ,CAAC,CAAC;YAC9C,8BAA8B;YAC9B,OAAOsB,CAAAA,GAAAA,IAAS,AAA+C,CAAA,UAA/C,CAACtB,QAAQ,EAAEU,KAAI,QAAA,CAACa,IAAI,CAACV,SAAS,EAAE,QAAQ,EAAEQ,GAAG,CAAC,CAAC,CAAC;SACjE,CAAC,CACH,CAAC;KACH;IACD3B,GAAG,CAACS,GAAG,CAAC,2BAA2B,CAAC,CAAC;CACtC"}