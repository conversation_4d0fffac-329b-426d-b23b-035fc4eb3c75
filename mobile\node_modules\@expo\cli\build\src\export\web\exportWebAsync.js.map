{"version": 3, "sources": ["../../../../src/export/web/exportWebAsync.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { Log } from '../../log';\nimport { WebSupportProjectPrerequisite } from '../../start/doctor/web/WebSupportProjectPrerequisite';\nimport { getPlatformBundlers } from '../../start/server/platformBundlers';\nimport { WebpackBundlerDevServer } from '../../start/server/webpack/WebpackBundlerDevServer';\nimport { CommandError } from '../../utils/errors';\nimport { setNodeEnv } from '../../utils/nodeEnv';\nimport { Options } from './resolveOptions';\n\nexport async function exportWebAsync(projectRoot: string, options: Options) {\n  // Ensure webpack is available\n  await new WebSupportProjectPrerequisite(projectRoot).assertAsync();\n\n  setNodeEnv(options.dev ? 'development' : 'production');\n  require('@expo/env').load(projectRoot);\n\n  const { exp } = getConfig(projectRoot);\n  const platformBundlers = getPlatformBundlers(exp);\n  // Create a bundler interface\n  const bundler = new WebpackBundlerDevServer(projectRoot, platformBundlers, false);\n\n  // If the user set `web.bundler: 'metro'` then they should use `expo export` instead.\n  if (!bundler.isTargetingWeb()) {\n    throw new CommandError(\n      chalk`{bold expo export:web} can only be used with Webpack. Use {bold expo export} for other bundlers.`\n    );\n  }\n\n  Log.log(`Exporting with Webpack...`);\n\n  // Bundle the app\n  await bundler.bundleAsync({\n    mode: options.dev ? 'development' : 'production',\n    clear: options.clear,\n  });\n}\n"], "names": ["exportWebAsync", "projectRoot", "options", "WebSupportProjectPrerequisite", "assertAsync", "setNodeEnv", "dev", "require", "load", "exp", "getConfig", "platformBundlers", "getPlatformBundlers", "bundler", "WebpackBundlerDevServer", "isTargetingWeb", "CommandError", "chalk", "Log", "log", "bundleAsync", "mode", "clear"], "mappings": "AAAA;;;;QAWsBA,cAAc,GAAdA,cAAc;AAXV,IAAA,OAAc,WAAd,cAAc,CAAA;AACtB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEL,IAAA,IAAW,WAAX,WAAW,CAAA;AACe,IAAA,8BAAsD,WAAtD,sDAAsD,CAAA;AAChE,IAAA,iBAAqC,WAArC,qCAAqC,CAAA;AACjC,IAAA,wBAAoD,WAApD,oDAAoD,CAAA;AAC/D,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AACtB,IAAA,QAAqB,WAArB,qBAAqB,CAAA;;;;;;AAGzC,eAAeA,cAAc,CAACC,WAAmB,EAAEC,OAAgB,EAAE;IAC1E,8BAA8B;IAC9B,MAAM,IAAIC,8BAA6B,8BAAA,CAACF,WAAW,CAAC,CAACG,WAAW,EAAE,CAAC;IAEnEC,CAAAA,GAAAA,QAAU,AAA4C,CAAA,WAA5C,CAACH,OAAO,CAACI,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,CAAC;IACvDC,OAAO,CAAC,WAAW,CAAC,CAACC,IAAI,CAACP,WAAW,CAAC,CAAC;IAEvC,MAAM,EAAEQ,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAAa,CAAA,UAAb,CAACT,WAAW,CAAC,AAAC;IACvC,MAAMU,gBAAgB,GAAGC,CAAAA,GAAAA,iBAAmB,AAAK,CAAA,oBAAL,CAACH,GAAG,CAAC,AAAC;IAClD,6BAA6B;IAC7B,MAAMI,OAAO,GAAG,IAAIC,wBAAuB,wBAAA,CAACb,WAAW,EAAEU,gBAAgB,EAAE,KAAK,CAAC,AAAC;IAElF,qFAAqF;IACrF,IAAI,CAACE,OAAO,CAACE,cAAc,EAAE,EAAE;QAC7B,MAAM,IAAIC,OAAY,aAAA,CACpBC,MAAK,QAAA,CAAC,gGAAgG,CAAC,CACxG,CAAC;KACH;IAEDC,IAAG,IAAA,CAACC,GAAG,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC;IAErC,iBAAiB;IACjB,MAAMN,OAAO,CAACO,WAAW,CAAC;QACxBC,IAAI,EAAEnB,OAAO,CAACI,GAAG,GAAG,aAAa,GAAG,YAAY;QAChDgB,KAAK,EAAEpB,OAAO,CAACoB,KAAK;KACrB,CAAC,CAAC;CACJ"}