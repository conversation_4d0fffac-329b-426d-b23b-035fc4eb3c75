{"version": 3, "sources": ["../../../../src/export/web/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../../utils/args';\nimport { logCmdError } from '../../utils/errors';\n\nexport const expoExportWeb: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--clear': <PERSON><PERSON><PERSON>,\n      '--dev': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n      '-c': '--clear',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Export the static files of the web app for hosting on a web server`,\n      chalk`npx expo export:web {dim <dir>}`,\n      [\n        chalk`<dir>                         Directory of the Expo project. {dim Default: Current working directory}`,\n        `--dev                         Bundle in development mode`,\n        `-c, --clear                   Clear the bundler cache`,\n        `-h, --help                    Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const projectRoot = getProjectRoot(args);\n  const { resolveOptionsAsync } = await import('./resolveOptions');\n  const options = await resolveOptionsAsync(args).catch(logCmdError);\n\n  const { exportWebAsync } = await import('./exportWebAsync');\n  return exportWebAsync(projectRoot, options).catch(logCmdError);\n};\n"], "names": ["expoExportWeb", "argv", "args", "assertArgs", "Boolean", "printHelp", "chalk", "join", "projectRoot", "getProjectRoot", "resolveOptionsAsync", "options", "catch", "logCmdError", "exportWebAsync"], "mappings": "AAAA;;;;;;AACkB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAG6B,IAAA,KAAkB,WAAlB,kBAAkB,CAAA;AAC5C,IAAA,OAAoB,WAApB,oBAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,MAAMA,aAAa,GAAY,OAAOC,IAAI,GAAK;IACpD,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,KAAU,AAWtB,CAAA,WAXsB,CACrB;QACE,QAAQ;QACR,QAAQ,EAAEC,OAAO;QACjB,SAAS,EAAEA,OAAO;QAClB,OAAO,EAAEA,OAAO;QAChB,UAAU;QACV,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,SAAS;KAChB,EACDH,IAAI,CACL,AAAC;IAEF,IAAIC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBG,CAAAA,GAAAA,KAAS,AASR,CAAA,UATQ,CACP,CAAC,kEAAkE,CAAC,EACpEC,MAAK,QAAA,CAAC,+BAA+B,CAAC,EACtC;YACEA,MAAK,QAAA,CAAC,qGAAqG,CAAC;YAC5G,CAAC,wDAAwD,CAAC;YAC1D,CAAC,qDAAqD,CAAC;YACvD,CAAC,wCAAwC,CAAC;SAC3C,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;KACH;IAED,MAAMC,WAAW,GAAGC,CAAAA,GAAAA,KAAc,AAAM,CAAA,eAAN,CAACP,IAAI,CAAC,AAAC;IACzC,MAAM,EAAEQ,mBAAmB,CAAA,EAAE,GAAG,MAAM;+CAAO,kBAAkB;MAAC,AAAC;IACjE,MAAMC,OAAO,GAAG,MAAMD,mBAAmB,CAACR,IAAI,CAAC,CAACU,KAAK,CAACC,OAAW,YAAA,CAAC,AAAC;IAEnE,MAAM,EAAEC,cAAc,CAAA,EAAE,GAAG,MAAM;+CAAO,kBAAkB;MAAC,AAAC;IAC5D,OAAOA,cAAc,CAACN,WAAW,EAAEG,OAAO,CAAC,CAACC,KAAK,CAACC,OAAW,YAAA,CAAC,CAAC;CAChE,AAAC;QAjCWb,aAAa,GAAbA,aAAa"}