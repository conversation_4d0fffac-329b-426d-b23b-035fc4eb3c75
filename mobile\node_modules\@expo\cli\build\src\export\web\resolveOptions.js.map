{"version": 3, "sources": ["../../../../src/export/web/resolveOptions.ts"], "sourcesContent": ["export type Options = {\n  dev: boolean;\n  clear: boolean;\n};\n\nexport async function resolveOptionsAsync(args: any): Promise<Options> {\n  return {\n    clear: !!args['--clear'],\n    dev: !!args['--dev'],\n  };\n}\n"], "names": ["resolveOptionsAsync", "args", "clear", "dev"], "mappings": "AAAA;;;;QAKsBA,mBAAmB,GAAnBA,mBAAmB;AAAlC,eAAeA,mBAAmB,CAACC,IAAS,EAAoB;IACrE,OAAO;QACLC,KAAK,EAAE,CAAC,CAACD,IAAI,CAAC,SAAS,CAAC;QACxBE,GAAG,EAAE,CAAC,CAACF,IAAI,CAAC,OAAO,CAAC;KACrB,CAAC;CACH"}