"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.writeBundlesAsync = writeBundlesAsync;
exports.writeSourceMapsAsync = writeSourceMapsAsync;
exports.writeMetadataJsonAsync = writeMetadataJsonAsync;
exports.writeAssetMapAsync = writeAssetMapAsync;
exports.writeDebugHtmlAsync = writeDebugHtmlAsync;
var _crypto = _interopRequireDefault(require("crypto"));
var _promises = _interopRequireDefault(require("fs/promises"));
var _path = _interopRequireDefault(require("path"));
var _createMetadataJson = require("./createMetadataJson");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const debug = require("debug")("expo:export:write");
/**
 * @param props.platform native platform for the bundle
 * @param props.format extension to use for the name
 * @param props.hash crypto hash for the bundle contents
 * @returns filename for the JS bundle.
 */ function createBundleFileName({ platform , format , hash  }) {
    return `${platform}-${hash}.${format === "javascript" ? "js" : "hbc"}`;
}
/**
 * @param bundle JS bundle as a string
 * @returns crypto hash for the provided bundle
 */ function createBundleHash(bundle) {
    return _crypto.default.createHash("md5").update(bundle).digest("hex");
}
async function writeBundlesAsync({ bundles , outputDir  }) {
    const hashes = {};
    const fileNames = {};
    for (const [platform, bundleOutput] of Object.entries(bundles)){
        var _hermesBytecodeBundle;
        const bundle = (_hermesBytecodeBundle = bundleOutput.hermesBytecodeBundle) != null ? _hermesBytecodeBundle : bundleOutput.code;
        const hash = createBundleHash(bundle);
        const fileName = createBundleFileName({
            platform,
            format: bundleOutput.hermesBytecodeBundle ? "bytecode" : "javascript",
            hash
        });
        hashes[platform] = hash;
        fileNames[platform] = fileName;
        await _promises.default.writeFile(_path.default.join(outputDir, fileName), bundle);
    }
    return {
        hashes,
        fileNames
    };
}
async function writeSourceMapsAsync({ bundles , hashes , fileNames , outputDir  }) {
    return (await Promise.all(Object.entries(bundles).map(async ([platform, bundle])=>{
        var _hermesSourcemap;
        const sourceMap = (_hermesSourcemap = bundle.hermesSourcemap) != null ? _hermesSourcemap : bundle.map;
        if (!sourceMap) {
            debug(`Skip writing sourcemap (platform: ${platform})`);
            return null;
        }
        var _hermesBytecodeBundle, ref;
        const hash = (ref = hashes == null ? void 0 : hashes[platform]) != null ? ref : createBundleHash((_hermesBytecodeBundle = bundle.hermesBytecodeBundle) != null ? _hermesBytecodeBundle : bundle.code);
        const mapName = `${platform}-${hash}.map`;
        await _promises.default.writeFile(_path.default.join(outputDir, mapName), sourceMap);
        var ref1;
        const jsBundleFileName = (ref1 = fileNames == null ? void 0 : fileNames[platform]) != null ? ref1 : createBundleFileName({
            platform,
            format: bundle.hermesBytecodeBundle ? "bytecode" : "javascript",
            hash
        });
        const jsPath = _path.default.join(outputDir, jsBundleFileName);
        // Add correct mapping to sourcemap paths
        const mappingComment = `\n//# sourceMappingURL=${mapName}`;
        await _promises.default.appendFile(jsPath, mappingComment);
        return {
            platform,
            fileName: mapName,
            hash,
            map: sourceMap,
            comment: mappingComment
        };
    }))).filter(Boolean);
}
async function writeMetadataJsonAsync({ outputDir , bundles , fileNames  }) {
    const contents = (0, _createMetadataJson).createMetadataJson({
        bundles,
        fileNames
    });
    const metadataPath = _path.default.join(outputDir, "metadata.json");
    debug(`Writing metadata.json to ${metadataPath}`);
    await _promises.default.writeFile(metadataPath, JSON.stringify(contents));
    return contents;
}
async function writeAssetMapAsync({ outputDir , assets  }) {
    // Convert the assets array to a k/v pair where the asset hash is the key and the asset is the value.
    const contents = Object.fromEntries(assets.map((asset)=>[
            asset.hash,
            asset
        ]
    ));
    await _promises.default.writeFile(_path.default.join(outputDir, "assetmap.json"), JSON.stringify(contents));
    return contents;
}
async function writeDebugHtmlAsync({ outputDir , fileNames  }) {
    // Make a debug html so user can debug their bundles
    const contents = `
      ${Object.values(fileNames).map((fileName)=>`<script src="${_path.default.join("bundles", fileName)}"></script>`
    ).join("\n      ")}
      Open up this file in Chrome. In the JavaScript developer console, navigate to the Source tab.
      You can see a red colored folder containing the original source code from your bundle.
      `;
    await _promises.default.writeFile(_path.default.join(outputDir, "debug.html"), contents);
    return contents;
}

//# sourceMappingURL=writeContents.js.map