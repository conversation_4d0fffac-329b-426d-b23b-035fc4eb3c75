{"version": 3, "sources": ["../../../src/export/writeContents.ts"], "sourcesContent": ["import { Platform } from '@expo/config';\nimport crypto from 'crypto';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport { createMetadataJson } from './createMetadataJson';\nimport { BundleOutput } from './fork-bundleAsync';\nimport { Asset } from './saveAssets';\n\nconst debug = require('debug')('expo:export:write') as typeof console.log;\n\n/**\n * @param props.platform native platform for the bundle\n * @param props.format extension to use for the name\n * @param props.hash crypto hash for the bundle contents\n * @returns filename for the JS bundle.\n */\nfunction createBundleFileName({\n  platform,\n  format,\n  hash,\n}: {\n  platform: string;\n  format: 'javascript' | 'bytecode';\n  hash: string;\n}): string {\n  return `${platform}-${hash}.${format === 'javascript' ? 'js' : 'hbc'}`;\n}\n\n/**\n * @param bundle JS bundle as a string\n * @returns crypto hash for the provided bundle\n */\nfunction createBundleHash(bundle: string | Uint8Array): string {\n  return crypto.createHash('md5').update(bundle).digest('hex');\n}\n\nexport async function writeBundlesAsync({\n  bundles,\n  outputDir,\n}: {\n  bundles: Partial<Record<Platform, Pick<BundleOutput, 'hermesBytecodeBundle' | 'code'>>>;\n  outputDir: string;\n}) {\n  const hashes: Partial<Record<Platform, string>> = {};\n  const fileNames: Partial<Record<Platform, string>> = {};\n\n  for (const [platform, bundleOutput] of Object.entries(bundles) as [\n    Platform,\n    Pick<BundleOutput, 'hermesBytecodeBundle' | 'code'>\n  ][]) {\n    const bundle = bundleOutput.hermesBytecodeBundle ?? bundleOutput.code;\n    const hash = createBundleHash(bundle);\n    const fileName = createBundleFileName({\n      platform,\n      format: bundleOutput.hermesBytecodeBundle ? 'bytecode' : 'javascript',\n      hash,\n    });\n\n    hashes[platform] = hash;\n    fileNames[platform] = fileName;\n    await fs.writeFile(path.join(outputDir, fileName), bundle);\n  }\n\n  return { hashes, fileNames };\n}\n\ntype SourceMapWriteResult = {\n  platform: string;\n  fileName: string;\n  hash: string;\n  map: string;\n  comment: string;\n};\n\nexport async function writeSourceMapsAsync({\n  bundles,\n  hashes,\n  fileNames,\n  outputDir,\n}: {\n  bundles: Record<\n    string,\n    Pick<BundleOutput, 'hermesSourcemap' | 'map' | 'hermesBytecodeBundle' | 'code'>\n  >;\n  hashes?: Record<string, string>;\n  fileNames?: Record<string, string>;\n  outputDir: string;\n}): Promise<SourceMapWriteResult[]> {\n  return (\n    await Promise.all(\n      Object.entries(bundles).map(async ([platform, bundle]) => {\n        const sourceMap = bundle.hermesSourcemap ?? bundle.map;\n        if (!sourceMap) {\n          debug(`Skip writing sourcemap (platform: ${platform})`);\n          return null;\n        }\n\n        const hash =\n          hashes?.[platform] ?? createBundleHash(bundle.hermesBytecodeBundle ?? bundle.code);\n        const mapName = `${platform}-${hash}.map`;\n        await fs.writeFile(path.join(outputDir, mapName), sourceMap);\n\n        const jsBundleFileName =\n          fileNames?.[platform] ??\n          createBundleFileName({\n            platform,\n            format: bundle.hermesBytecodeBundle ? 'bytecode' : 'javascript',\n            hash,\n          });\n        const jsPath = path.join(outputDir, jsBundleFileName);\n\n        // Add correct mapping to sourcemap paths\n        const mappingComment = `\\n//# sourceMappingURL=${mapName}`;\n        await fs.appendFile(jsPath, mappingComment);\n        return {\n          platform,\n          fileName: mapName,\n          hash,\n          map: sourceMap,\n          comment: mappingComment,\n        };\n      })\n    )\n  ).filter(Boolean) as SourceMapWriteResult[];\n}\n\nexport async function writeMetadataJsonAsync({\n  outputDir,\n  bundles,\n  fileNames,\n}: {\n  outputDir: string;\n  bundles: Record<string, Pick<BundleOutput, 'assets'>>;\n  fileNames: Record<string, string>;\n}) {\n  const contents = createMetadataJson({\n    bundles,\n    fileNames,\n  });\n  const metadataPath = path.join(outputDir, 'metadata.json');\n  debug(`Writing metadata.json to ${metadataPath}`);\n  await fs.writeFile(metadataPath, JSON.stringify(contents));\n  return contents;\n}\n\nexport async function writeAssetMapAsync({\n  outputDir,\n  assets,\n}: {\n  outputDir: string;\n  assets: Asset[];\n}) {\n  // Convert the assets array to a k/v pair where the asset hash is the key and the asset is the value.\n  const contents = Object.fromEntries(assets.map((asset) => [asset.hash, asset]));\n  await fs.writeFile(path.join(outputDir, 'assetmap.json'), JSON.stringify(contents));\n  return contents;\n}\n\nexport async function writeDebugHtmlAsync({\n  outputDir,\n  fileNames,\n}: {\n  outputDir: string;\n  fileNames: Record<string, string>;\n}) {\n  // Make a debug html so user can debug their bundles\n  const contents = `\n      ${Object.values(fileNames)\n        .map((fileName) => `<script src=\"${path.join('bundles', fileName)}\"></script>`)\n        .join('\\n      ')}\n      Open up this file in Chrome. In the JavaScript developer console, navigate to the Source tab.\n      You can see a red colored folder containing the original source code from your bundle.\n      `;\n\n  await fs.writeFile(path.join(outputDir, 'debug.html'), contents);\n  return contents;\n}\n"], "names": ["writeBundlesAsync", "writeSourceMapsAsync", "writeMetadataJsonAsync", "writeAssetMapAsync", "writeDebugHtmlAsync", "debug", "require", "createBundleFileName", "platform", "format", "hash", "createBundleHash", "bundle", "crypto", "createHash", "update", "digest", "bundles", "outputDir", "hashes", "fileNames", "bundleOutput", "Object", "entries", "hermesBytecodeBundle", "code", "fileName", "fs", "writeFile", "path", "join", "Promise", "all", "map", "sourceMap", "hermesSourcemap", "mapName", "jsBundleFileName", "jsPath", "mappingComment", "appendFile", "comment", "filter", "Boolean", "contents", "createMetadataJson", "metadataPath", "JSON", "stringify", "assets", "fromEntries", "asset", "values"], "mappings": "AAAA;;;;QAqCsBA,iBAAiB,GAAjBA,iBAAiB;QAsCjBC,oBAAoB,GAApBA,oBAAoB;QAoDpBC,sBAAsB,GAAtBA,sBAAsB;QAmBtBC,kBAAkB,GAAlBA,kBAAkB;QAalBC,mBAAmB,GAAnBA,mBAAmB;AA9JtB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACZ,IAAA,SAAa,kCAAb,aAAa,EAAA;AACX,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEY,IAAA,mBAAsB,WAAtB,sBAAsB,CAAA;;;;;;AAIzD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,AAAsB,AAAC;AAE1E;;;;;GAKG,CACH,SAASC,oBAAoB,CAAC,EAC5BC,QAAQ,CAAA,EACRC,MAAM,CAAA,EACNC,IAAI,CAAA,EAKL,EAAU;IACT,OAAO,CAAC,EAAEF,QAAQ,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC,EAAED,MAAM,KAAK,YAAY,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;CACxE;AAED;;;GAGG,CACH,SAASE,gBAAgB,CAACC,MAA2B,EAAU;IAC7D,OAAOC,OAAM,QAAA,CAACC,UAAU,CAAC,KAAK,CAAC,CAACC,MAAM,CAACH,MAAM,CAAC,CAACI,MAAM,CAAC,KAAK,CAAC,CAAC;CAC9D;AAEM,eAAehB,iBAAiB,CAAC,EACtCiB,OAAO,CAAA,EACPC,SAAS,CAAA,EAIV,EAAE;IACD,MAAMC,MAAM,GAAsC,EAAE,AAAC;IACrD,MAAMC,SAAS,GAAsC,EAAE,AAAC;IAExD,KAAK,MAAM,CAACZ,QAAQ,EAAEa,YAAY,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,OAAO,CAAC,CAGzD;YACYI,qBAAiC;QAAhD,MAAMT,MAAM,GAAGS,CAAAA,qBAAiC,GAAjCA,YAAY,CAACG,oBAAoB,YAAjCH,qBAAiC,GAAIA,YAAY,CAACI,IAAI,AAAC;QACtE,MAAMf,IAAI,GAAGC,gBAAgB,CAACC,MAAM,CAAC,AAAC;QACtC,MAAMc,QAAQ,GAAGnB,oBAAoB,CAAC;YACpCC,QAAQ;YACRC,MAAM,EAAEY,YAAY,CAACG,oBAAoB,GAAG,UAAU,GAAG,YAAY;YACrEd,IAAI;SACL,CAAC,AAAC;QAEHS,MAAM,CAACX,QAAQ,CAAC,GAAGE,IAAI,CAAC;QACxBU,SAAS,CAACZ,QAAQ,CAAC,GAAGkB,QAAQ,CAAC;QAC/B,MAAMC,SAAE,QAAA,CAACC,SAAS,CAACC,KAAI,QAAA,CAACC,IAAI,CAACZ,SAAS,EAAEQ,QAAQ,CAAC,EAAEd,MAAM,CAAC,CAAC;KAC5D;IAED,OAAO;QAAEO,MAAM;QAAEC,SAAS;KAAE,CAAC;CAC9B;AAUM,eAAenB,oBAAoB,CAAC,EACzCgB,OAAO,CAAA,EACPE,MAAM,CAAA,EACNC,SAAS,CAAA,EACTF,SAAS,CAAA,EASV,EAAmC;IAClC,OAAO,CACL,MAAMa,OAAO,CAACC,GAAG,CACfV,MAAM,CAACC,OAAO,CAACN,OAAO,CAAC,CAACgB,GAAG,CAAC,OAAO,CAACzB,QAAQ,EAAEI,MAAM,CAAC,GAAK;YACtCA,gBAAsB;QAAxC,MAAMsB,SAAS,GAAGtB,CAAAA,gBAAsB,GAAtBA,MAAM,CAACuB,eAAe,YAAtBvB,gBAAsB,GAAIA,MAAM,CAACqB,GAAG,AAAC;QACvD,IAAI,CAACC,SAAS,EAAE;YACd7B,KAAK,CAAC,CAAC,kCAAkC,EAAEG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;SACb;YAGwCI,qBAA2B,EAAlEO,GAAkB;QADpB,MAAMT,IAAI,GACRS,CAAAA,GAAkB,GAAlBA,MAAM,QAAY,GAAlBA,KAAAA,CAAkB,GAAlBA,MAAM,AAAE,CAACX,QAAQ,CAAC,YAAlBW,GAAkB,GAAIR,gBAAgB,CAACC,CAAAA,qBAA2B,GAA3BA,MAAM,CAACY,oBAAoB,YAA3BZ,qBAA2B,GAAIA,MAAM,CAACa,IAAI,CAAC,AAAC;QACrF,MAAMW,OAAO,GAAG,CAAC,EAAE5B,QAAQ,CAAC,CAAC,EAAEE,IAAI,CAAC,IAAI,CAAC,AAAC;QAC1C,MAAMiB,SAAE,QAAA,CAACC,SAAS,CAACC,KAAI,QAAA,CAACC,IAAI,CAACZ,SAAS,EAAEkB,OAAO,CAAC,EAAEF,SAAS,CAAC,CAAC;YAG3Dd,IAAqB;QADvB,MAAMiB,gBAAgB,GACpBjB,CAAAA,IAAqB,GAArBA,SAAS,QAAY,GAArBA,KAAAA,CAAqB,GAArBA,SAAS,AAAE,CAACZ,QAAQ,CAAC,YAArBY,IAAqB,GACrBb,oBAAoB,CAAC;YACnBC,QAAQ;YACRC,MAAM,EAAEG,MAAM,CAACY,oBAAoB,GAAG,UAAU,GAAG,YAAY;YAC/Dd,IAAI;SACL,CAAC,AAAC;QACL,MAAM4B,MAAM,GAAGT,KAAI,QAAA,CAACC,IAAI,CAACZ,SAAS,EAAEmB,gBAAgB,CAAC,AAAC;QAEtD,yCAAyC;QACzC,MAAME,cAAc,GAAG,CAAC,uBAAuB,EAAEH,OAAO,CAAC,CAAC,AAAC;QAC3D,MAAMT,SAAE,QAAA,CAACa,UAAU,CAACF,MAAM,EAAEC,cAAc,CAAC,CAAC;QAC5C,OAAO;YACL/B,QAAQ;YACRkB,QAAQ,EAAEU,OAAO;YACjB1B,IAAI;YACJuB,GAAG,EAAEC,SAAS;YACdO,OAAO,EAAEF,cAAc;SACxB,CAAC;KACH,CAAC,CACH,CACF,CAACG,MAAM,CAACC,OAAO,CAAC,CAA2B;CAC7C;AAEM,eAAezC,sBAAsB,CAAC,EAC3CgB,SAAS,CAAA,EACTD,OAAO,CAAA,EACPG,SAAS,CAAA,EAKV,EAAE;IACD,MAAMwB,QAAQ,GAAGC,CAAAA,GAAAA,mBAAkB,AAGjC,CAAA,mBAHiC,CAAC;QAClC5B,OAAO;QACPG,SAAS;KACV,CAAC,AAAC;IACH,MAAM0B,YAAY,GAAGjB,KAAI,QAAA,CAACC,IAAI,CAACZ,SAAS,EAAE,eAAe,CAAC,AAAC;IAC3Db,KAAK,CAAC,CAAC,yBAAyB,EAAEyC,YAAY,CAAC,CAAC,CAAC,CAAC;IAClD,MAAMnB,SAAE,QAAA,CAACC,SAAS,CAACkB,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC,CAAC,CAAC;IAC3D,OAAOA,QAAQ,CAAC;CACjB;AAEM,eAAezC,kBAAkB,CAAC,EACvCe,SAAS,CAAA,EACT+B,MAAM,CAAA,EAIP,EAAE;IACD,qGAAqG;IACrG,MAAML,QAAQ,GAAGtB,MAAM,CAAC4B,WAAW,CAACD,MAAM,CAAChB,GAAG,CAAC,CAACkB,KAAK,GAAK;YAACA,KAAK,CAACzC,IAAI;YAAEyC,KAAK;SAAC;IAAA,CAAC,CAAC,AAAC;IAChF,MAAMxB,SAAE,QAAA,CAACC,SAAS,CAACC,KAAI,QAAA,CAACC,IAAI,CAACZ,SAAS,EAAE,eAAe,CAAC,EAAE6B,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC,CAAC,CAAC;IACpF,OAAOA,QAAQ,CAAC;CACjB;AAEM,eAAexC,mBAAmB,CAAC,EACxCc,SAAS,CAAA,EACTE,SAAS,CAAA,EAIV,EAAE;IACD,oDAAoD;IACpD,MAAMwB,QAAQ,GAAG,CAAC;MACd,EAAEtB,MAAM,CAAC8B,MAAM,CAAChC,SAAS,CAAC,CACvBa,GAAG,CAAC,CAACP,QAAQ,GAAK,CAAC,aAAa,EAAEG,KAAI,QAAA,CAACC,IAAI,CAAC,SAAS,EAAEJ,QAAQ,CAAC,CAAC,WAAW,CAAC;IAAA,CAAC,CAC9EI,IAAI,CAAC,UAAU,CAAC,CAAC;;;MAGpB,CAAC,AAAC;IAEN,MAAMH,SAAE,QAAA,CAACC,SAAS,CAACC,KAAI,QAAA,CAACC,IAAI,CAACZ,SAAS,EAAE,YAAY,CAAC,EAAE0B,QAAQ,CAAC,CAAC;IACjE,OAAOA,QAAQ,CAAC;CACjB"}