{"version": 3, "sources": ["../../../src/install/checkPackages.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport * as PackageManager from '@expo/package-manager';\nimport chalk from 'chalk';\n\nimport * as Log from '../log';\nimport {\n  getVersionedDependenciesAsync,\n  logIncorrectDependencies,\n} from '../start/doctor/dependencies/validateDependenciesVersions';\nimport { isInteractive } from '../utils/interactive';\nimport { learnMore } from '../utils/link';\nimport { confirmAsync } from '../utils/prompts';\nimport { joinWithCommasAnd } from '../utils/strings';\nimport { fixPackagesAsync } from './installAsync';\nimport { Options } from './resolveOptions';\n\nconst debug = require('debug')('expo:install:check') as typeof console.log;\n\n// Exposed for testing.\nexport async function checkPackagesAsync(\n  projectRoot: string,\n  {\n    packages,\n    packageManager,\n    options: { fix },\n    packageManagerArguments,\n  }: {\n    /**\n     * List of packages to version\n     * @example ['uuid', 'react-native-reanimated@latest']\n     */\n    packages: string[];\n    /** Package manager to use when installing the versioned packages. */\n    packageManager: PackageManager.NodePackageManager;\n\n    /** How the check should resolve */\n    options: Pick<Options, 'fix'>;\n    /**\n     * Extra parameters to pass to the `packageManager` when installing versioned packages.\n     * @example ['--no-save']\n     */\n    packageManagerArguments: string[];\n  }\n) {\n  // Read the project Expo config without plugins.\n  const { exp, pkg } = getConfig(projectRoot, {\n    // Sometimes users will add a plugin to the config before installing the library,\n    // this wouldn't work unless we dangerously disable plugin serialization.\n    skipPlugins: true,\n  });\n\n  if (pkg.expo?.install?.exclude?.length) {\n    Log.log(\n      chalk`Skipped ${fix ? 'fixing' : 'checking'} dependencies: ${joinWithCommasAnd(\n        pkg.expo.install.exclude\n      )}. These dependencies are listed in {bold expo.install.exclude} in package.json. ${learnMore(\n        'https://expo.dev/more/expo-cli/#configuring-dependency-validation'\n      )}`\n    );\n  }\n\n  const dependencies = await getVersionedDependenciesAsync(projectRoot, exp, pkg, packages);\n\n  if (!dependencies.length) {\n    Log.exit(chalk.greenBright('Dependencies are up to date'), 0);\n  }\n\n  logIncorrectDependencies(dependencies);\n\n  const value =\n    // If `--fix` then always fix.\n    fix ||\n    // Otherwise prompt to fix when not running in CI.\n    (isInteractive() && (await confirmAsync({ message: 'Fix dependencies?' }).catch(() => false)));\n\n  if (value) {\n    debug('Installing fixed dependencies:', dependencies);\n    // Install the corrected dependencies.\n    return fixPackagesAsync(projectRoot, {\n      packageManager,\n      packages: dependencies,\n      packageManagerArguments,\n      sdkVersion: exp.sdkVersion!,\n    });\n  }\n  // Exit with non-zero exit code if any of the dependencies are out of date.\n  Log.exit(chalk.red('Found outdated dependencies'), 1);\n}\n"], "names": ["checkPackagesAsync", "Log", "debug", "require", "projectRoot", "packages", "packageManager", "options", "fix", "packageManagerArguments", "pkg", "exp", "getConfig", "skip<PERSON>lug<PERSON>", "expo", "install", "exclude", "length", "log", "chalk", "joinWithCommasAnd", "learnMore", "dependencies", "getVersionedDependenciesAsync", "exit", "<PERSON><PERSON><PERSON>", "logIncorrectDependencies", "value", "isInteractive", "<PERSON><PERSON><PERSON>", "message", "catch", "fixPackagesAsync", "sdkVersion", "red"], "mappings": "AAAA;;;;QAmBsBA,kBAAkB,GAAlBA,kBAAkB;AAnBd,IAAA,OAAc,WAAd,cAAc,CAAA;AAEtB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEbC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AAIR,IAAA,6BAA2D,WAA3D,2DAA2D,CAAA;AACpC,IAAA,YAAsB,WAAtB,sBAAsB,CAAA;AAC1B,IAAA,KAAe,WAAf,eAAe,CAAA;AACZ,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACb,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACnB,IAAA,aAAgB,WAAhB,gBAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGjD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,AAAsB,AAAC;AAGpE,eAAeH,kBAAkB,CACtCI,WAAmB,EACnB,EACEC,QAAQ,CAAA,EACRC,cAAc,CAAA,EACdC,OAAO,EAAE,EAAEC,GAAG,CAAA,EAAE,CAAA,EAChBC,uBAAuB,CAAA,EAiBxB,EACD;QAQIC,GAAQ;IAPZ,gDAAgD;IAChD,MAAM,EAAEC,GAAG,CAAA,EAAED,GAAG,CAAA,EAAE,GAAGE,CAAAA,GAAAA,OAAS,AAI5B,CAAA,UAJ4B,CAACR,WAAW,EAAE;QAC1C,iFAAiF;QACjF,yEAAyE;QACzES,WAAW,EAAE,IAAI;KAClB,CAAC,AAAC;IAEH,IAAIH,CAAAA,GAAQ,GAARA,GAAG,CAACI,IAAI,SAAS,GAAjBJ,KAAAA,CAAiB,GAAjBA,QAAAA,GAAQ,CAAEK,OAAO,SAAA,GAAjBL,KAAAA,CAAiB,GAAjBA,aAAmBM,OAAO,SAAT,GAAjBN,KAAAA,CAAiB,QAAWO,MAAM,AAAjB,EAAmB;QACtChB,GAAG,CAACiB,GAAG,CACLC,MAAK,QAAA,CAAC,QAAQ,EAAEX,GAAG,GAAG,QAAQ,GAAG,UAAU,CAAC,eAAe,EAAEY,CAAAA,GAAAA,QAAiB,AAE7E,CAAA,kBAF6E,CAC5EV,GAAG,CAACI,IAAI,CAACC,OAAO,CAACC,OAAO,CACzB,CAAC,gFAAgF,EAAEK,CAAAA,GAAAA,KAAS,AAE5F,CAAA,UAF4F,CAC3F,mEAAmE,CACpE,CAAC,CAAC,CACJ,CAAC;KACH;IAED,MAAMC,YAAY,GAAG,MAAMC,CAAAA,GAAAA,6BAA6B,AAAiC,CAAA,8BAAjC,CAACnB,WAAW,EAAEO,GAAG,EAAED,GAAG,EAAEL,QAAQ,CAAC,AAAC;IAE1F,IAAI,CAACiB,YAAY,CAACL,MAAM,EAAE;QACxBhB,GAAG,CAACuB,IAAI,CAACL,MAAK,QAAA,CAACM,WAAW,CAAC,6BAA6B,CAAC,EAAE,CAAC,CAAC,CAAC;KAC/D;IAEDC,CAAAA,GAAAA,6BAAwB,AAAc,CAAA,yBAAd,CAACJ,YAAY,CAAC,CAAC;IAEvC,MAAMK,KAAK,GACT,8BAA8B;IAC9BnB,GAAG,IACH,kDAAkD;IAClD,CAACoB,CAAAA,GAAAA,YAAa,AAAE,CAAA,cAAF,EAAE,IAAK,MAAMC,CAAAA,GAAAA,QAAY,AAAkC,CAAA,aAAlC,CAAC;QAAEC,OAAO,EAAE,mBAAmB;KAAE,CAAC,CAACC,KAAK,CAAC,IAAM,KAAK;IAAA,CAAC,AAAC,CAAC,AAAC;IAEjG,IAAIJ,KAAK,EAAE;QACTzB,KAAK,CAAC,gCAAgC,EAAEoB,YAAY,CAAC,CAAC;QACtD,sCAAsC;QACtC,OAAOU,CAAAA,GAAAA,aAAgB,AAKrB,CAAA,iBALqB,CAAC5B,WAAW,EAAE;YACnCE,cAAc;YACdD,QAAQ,EAAEiB,YAAY;YACtBb,uBAAuB;YACvBwB,UAAU,EAAEtB,GAAG,CAACsB,UAAU;SAC3B,CAAC,CAAC;KACJ;IACD,2EAA2E;IAC3EhC,GAAG,CAACuB,IAAI,CAACL,MAAK,QAAA,CAACe,GAAG,CAAC,6BAA6B,CAAC,EAAE,CAAC,CAAC,CAAC;CACvD"}