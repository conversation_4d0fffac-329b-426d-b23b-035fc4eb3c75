{"version": 3, "sources": ["../../../src/install/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../utils/args';\n\nexport const expoInstall: Command = async (argv) => {\n  const args = assertWithOptionsArgs(\n    {\n      // Other options are parsed manually.\n      '--help': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n    },\n    {\n      argv,\n      // Allow other options, we'll throw an error if unexpected values are passed.\n      permissive: true,\n    }\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Install a module or other package to a project`,\n      `npx expo install`,\n      [\n        `--check     Check which installed packages need to be updated`,\n        `--fix       Automatically update any invalid package versions`,\n        chalk`--npm       Use npm to install dependencies. {dim Default when package-lock.json exists}`,\n        chalk`--yarn      Use Yarn to install dependencies. {dim Default when yarn.lock exists}`,\n        chalk`--bun       Use bun to install dependencies. {dim Default when bun.lockb exists}`,\n        chalk`--pnpm      Use pnpm to install dependencies. {dim Default when pnpm-lock.yaml exists}`,\n        `-h, --help  Usage info`,\n      ].join('\\n'),\n      [\n        '',\n        chalk`  Additional options can be passed to the underlying install command by using {bold --}`,\n        chalk`    {dim $} npx expo install react -- --verbose`,\n        chalk`    {dim >} yarn add react --verbose`,\n        '',\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo install -h` shows as fast as possible.\n  const { installAsync } = require('./installAsync') as typeof import('./installAsync');\n  const { logCmdError } = require('../utils/errors') as typeof import('../utils/errors');\n  const { resolveArgsAsync } = require('./resolveOptions') as typeof import('./resolveOptions');\n\n  const { variadic, options, extras } = await resolveArgsAsync(process.argv.slice(3)).catch(\n    logCmdError\n  );\n  return installAsync(variadic, options, extras).catch(logCmdError);\n};\n"], "names": ["expoInstall", "argv", "args", "assertWithOptionsArgs", "Boolean", "permissive", "printHelp", "chalk", "join", "installAsync", "require", "logCmdError", "resolveArgsAsync", "variadic", "options", "extras", "process", "slice", "catch"], "mappings": "AAAA;;;;;;AACkB,IAAA,MAAO,kCAAP,OAAO,EAAA;AAGwB,IAAA,KAAe,WAAf,eAAe,CAAA;;;;;;AAEzD,MAAMA,WAAW,GAAY,OAAOC,IAAI,GAAK;IAClD,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,KAAqB,AAYjC,CAAA,sBAZiC,CAChC;QACE,qCAAqC;QACrC,QAAQ,EAAEC,OAAO;QACjB,UAAU;QACV,IAAI,EAAE,QAAQ;KACf,EACD;QACEH,IAAI;QACJ,6EAA6E;QAC7EI,UAAU,EAAE,IAAI;KACjB,CACF,AAAC;IAEF,IAAIH,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBI,CAAAA,GAAAA,KAAS,AAmBR,CAAA,UAnBQ,CACP,CAAC,8CAA8C,CAAC,EAChD,CAAC,gBAAgB,CAAC,EAClB;YACE,CAAC,6DAA6D,CAAC;YAC/D,CAAC,6DAA6D,CAAC;YAC/DC,MAAK,QAAA,CAAC,wFAAwF,CAAC;YAC/FA,MAAK,QAAA,CAAC,iFAAiF,CAAC;YACxFA,MAAK,QAAA,CAAC,gFAAgF,CAAC;YACvFA,MAAK,QAAA,CAAC,sFAAsF,CAAC;YAC7F,CAAC,sBAAsB,CAAC;SACzB,CAACC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,EAAE;YACFD,MAAK,QAAA,CAAC,uFAAuF,CAAC;YAC9FA,MAAK,QAAA,CAAC,+CAA+C,CAAC;YACtDA,MAAK,QAAA,CAAC,oCAAoC,CAAC;YAC3C,EAAE;SACH,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;KACH;IAED,yFAAyF;IACzF,MAAM,EAAEC,YAAY,CAAA,EAAE,GAAGC,OAAO,CAAC,gBAAgB,CAAC,AAAmC,AAAC;IACtF,MAAM,EAAEC,WAAW,CAAA,EAAE,GAAGD,OAAO,CAAC,iBAAiB,CAAC,AAAoC,AAAC;IACvF,MAAM,EAAEE,gBAAgB,CAAA,EAAE,GAAGF,OAAO,CAAC,kBAAkB,CAAC,AAAqC,AAAC;IAE9F,MAAM,EAAEG,QAAQ,CAAA,EAAEC,OAAO,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAG,MAAMH,gBAAgB,CAACI,OAAO,CAACf,IAAI,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CACvFP,WAAW,CACZ,AAAC;IACF,OAAOF,YAAY,CAACI,QAAQ,EAAEC,OAAO,EAAEC,MAAM,CAAC,CAACG,KAAK,CAACP,WAAW,CAAC,CAAC;CACnE,AAAC;QA/CWX,WAAW,GAAXA,WAAW"}