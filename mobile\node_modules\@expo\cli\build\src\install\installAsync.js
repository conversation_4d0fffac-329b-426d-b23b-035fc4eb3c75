"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.installAsync = installAsync;
exports.installPackagesAsync = installPackagesAsync;
exports.fixPackagesAsync = fixPackagesAsync;
var _config = require("@expo/config");
var PackageManager = _interopRequireWildcard(require("@expo/package-manager"));
var _chalk = _interopRequireDefault(require("chalk"));
var Log = _interopRequireWildcard(require("../log"));
var _getVersionedPackages = require("../start/doctor/dependencies/getVersionedPackages");
var _array = require("../utils/array");
var _findUp = require("../utils/findUp");
var _link = require("../utils/link");
var _nodeEnv = require("../utils/nodeEnv");
var _strings = require("../utils/strings");
var _checkPackages = require("./checkPackages");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _interopRequireWildcard(obj) {
    if (obj && obj.__esModule) {
        return obj;
    } else {
        var newObj = {};
        if (obj != null) {
            for(var key in obj){
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};
                    if (desc.get || desc.set) {
                        Object.defineProperty(newObj, key, desc);
                    } else {
                        newObj[key] = obj[key];
                    }
                }
            }
        }
        newObj.default = obj;
        return newObj;
    }
}
async function installAsync(packages, options, packageManagerArguments = []) {
    (0, _nodeEnv).setNodeEnv("development");
    var _projectRoot;
    // Locate the project root based on the process current working directory.
    // This enables users to run `npx expo install` from a subdirectory of the project.
    const projectRoot = (_projectRoot = options.projectRoot) != null ? _projectRoot : (0, _findUp).findUpProjectRootOrAssert(process.cwd());
    require("@expo/env").load(projectRoot);
    // Resolve the package manager used by the project, or based on the provided arguments.
    const packageManager = PackageManager.createForProject(projectRoot, {
        npm: options.npm,
        yarn: options.yarn,
        bun: options.bun,
        pnpm: options.pnpm,
        silent: options.silent,
        log: Log.log
    });
    if (options.check || options.fix) {
        return await (0, _checkPackages).checkPackagesAsync(projectRoot, {
            packages,
            options,
            packageManager,
            packageManagerArguments
        });
    }
    // Read the project Expo config without plugins.
    const { exp  } = (0, _config).getConfig(projectRoot, {
        // Sometimes users will add a plugin to the config before installing the library,
        // this wouldn't work unless we dangerously disable plugin serialization.
        skipPlugins: true
    });
    // Resolve the versioned packages, then install them.
    return installPackagesAsync(projectRoot, {
        packageManager,
        packages,
        packageManagerArguments,
        sdkVersion: exp.sdkVersion
    });
}
async function installPackagesAsync(projectRoot, { packages , packageManager , sdkVersion , packageManagerArguments  }) {
    // Read the project Expo config without plugins.
    const { pkg  } = (0, _config).getConfig(projectRoot, {
        // Sometimes users will add a plugin to the config before installing the library,
        // this wouldn't work unless we dangerously disable plugin serialization.
        skipPlugins: true
    });
    //assertNotInstallingExcludedPackages(projectRoot, packages, pkg);
    const versioning = await (0, _getVersionedPackages).getVersionedPackagesAsync(projectRoot, {
        packages,
        // sdkVersion is always defined because we don't skipSDKVersionRequirement in getConfig.
        sdkVersion,
        pkg
    });
    Log.log(_chalk.default`\u203A Installing ${versioning.messages.length ? versioning.messages.join(" and ") + " " : ""}using {bold ${packageManager.name}}`);
    if (versioning.excludedNativeModules.length) {
        Log.log(_chalk.default`\u203A Using latest version instead of ${(0, _strings).joinWithCommasAnd(versioning.excludedNativeModules.map(({ bundledNativeVersion , name  })=>`${bundledNativeVersion} for ${name}`
        ))} because ${versioning.excludedNativeModules.length > 1 ? "they are" : "it is"} listed in {bold expo.install.exclude} in package.json. ${(0, _link).learnMore("https://expo.dev/more/expo-cli/#configuring-dependency-validation")}`);
    }
    await packageManager.addAsync([
        ...packageManagerArguments,
        ...versioning.packages
    ]);
    await applyPluginsAsync(projectRoot, versioning.packages);
}
async function fixPackagesAsync(projectRoot, { packages , packageManager , sdkVersion , packageManagerArguments  }) {
    if (!packages.length) {
        return;
    }
    const { dependencies =[] , devDependencies =[]  } = (0, _array).groupBy(packages, (dep)=>dep.packageType
    );
    const versioningMessages = (0, _getVersionedPackages).getOperationLog({
        othersCount: 0,
        nativeModulesCount: packages.length,
        sdkVersion
    });
    Log.log(_chalk.default`\u203A Installing ${versioningMessages.length ? versioningMessages.join(" and ") + " " : ""}using {bold ${packageManager.name}}`);
    if (dependencies.length) {
        const versionedPackages = dependencies.map((dep)=>`${dep.packageName}@${dep.expectedVersionOrRange}`
        );
        await packageManager.addAsync([
            ...packageManagerArguments,
            ...versionedPackages
        ]);
        await applyPluginsAsync(projectRoot, versionedPackages);
    }
    if (devDependencies.length) {
        await packageManager.addDevAsync([
            ...packageManagerArguments,
            ...devDependencies.map((dep)=>`${dep.packageName}@${dep.expectedVersionOrRange}`
            ), 
        ]);
    }
}
/**
 * A convenience feature for automatically applying Expo Config Plugins to the `app.json` after installing them.
 * This should be dropped in favor of autolinking in the future.
 */ async function applyPluginsAsync(projectRoot, packages) {
    const { autoAddConfigPluginsAsync  } = await Promise.resolve().then(function() {
        return _interopRequireWildcard(require("./utils/autoAddConfigPlugins"));
    });
    try {
        const { exp  } = (0, _config).getConfig(projectRoot, {
            skipSDKVersionRequirement: true
        });
        // Only auto add plugins if the plugins array is defined or if the project is using SDK +42.
        await autoAddConfigPluginsAsync(projectRoot, exp, // Split any possible NPM tags. i.e. `expo@latest` -> `expo`
        packages.map((pkg)=>pkg.split("@")[0]
        ).filter(Boolean));
    } catch (error) {
        // If we fail to apply plugins, the log a warning and continue.
        if (error.isPluginError) {
            Log.warn(`Skipping config plugin check: ` + error.message);
            return;
        }
        // Any other error, rethrow.
        throw error;
    }
}

//# sourceMappingURL=installAsync.js.map