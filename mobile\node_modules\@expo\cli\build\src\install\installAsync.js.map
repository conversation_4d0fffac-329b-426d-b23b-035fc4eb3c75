{"version": 3, "sources": ["../../../src/install/installAsync.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport * as PackageManager from '@expo/package-manager';\nimport chalk from 'chalk';\n\nimport * as Log from '../log';\nimport {\n  getOperationLog,\n  getVersionedPackagesAsync,\n} from '../start/doctor/dependencies/getVersionedPackages';\nimport { getVersionedDependenciesAsync } from '../start/doctor/dependencies/validateDependenciesVersions';\nimport { groupBy } from '../utils/array';\nimport { findUpProjectRootOrAssert } from '../utils/findUp';\nimport { learnMore } from '../utils/link';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { joinWithCommasAnd } from '../utils/strings';\nimport { checkPackagesAsync } from './checkPackages';\nimport { Options } from './resolveOptions';\n\nexport async function installAsync(\n  packages: string[],\n  options: Options & { projectRoot?: string },\n  packageManagerArguments: string[] = []\n) {\n  setNodeEnv('development');\n  // Locate the project root based on the process current working directory.\n  // This enables users to run `npx expo install` from a subdirectory of the project.\n  const projectRoot = options.projectRoot ?? findUpProjectRootOrAssert(process.cwd());\n  require('@expo/env').load(projectRoot);\n\n  // Resolve the package manager used by the project, or based on the provided arguments.\n  const packageManager = PackageManager.createForProject(projectRoot, {\n    npm: options.npm,\n    yarn: options.yarn,\n    bun: options.bun,\n    pnpm: options.pnpm,\n    silent: options.silent,\n    log: Log.log,\n  });\n\n  if (options.check || options.fix) {\n    return await checkPackagesAsync(projectRoot, {\n      packages,\n      options,\n      packageManager,\n      packageManagerArguments,\n    });\n  }\n\n  // Read the project Expo config without plugins.\n  const { exp } = getConfig(projectRoot, {\n    // Sometimes users will add a plugin to the config before installing the library,\n    // this wouldn't work unless we dangerously disable plugin serialization.\n    skipPlugins: true,\n  });\n\n  // Resolve the versioned packages, then install them.\n  return installPackagesAsync(projectRoot, {\n    packageManager,\n    packages,\n    packageManagerArguments,\n    sdkVersion: exp.sdkVersion!,\n  });\n}\n\n/** Version packages and install in a project. */\nexport async function installPackagesAsync(\n  projectRoot: string,\n  {\n    packages,\n    packageManager,\n    sdkVersion,\n    packageManagerArguments,\n  }: {\n    /**\n     * List of packages to version, grouped by the type of dependency.\n     * @example ['uuid', 'react-native-reanimated@latest']\n     */\n    packages: string[];\n    /** Package manager to use when installing the versioned packages. */\n    packageManager: PackageManager.NodePackageManager;\n    /**\n     * SDK to version `packages` for.\n     * @example '44.0.0'\n     */\n    sdkVersion: string;\n    /**\n     * Extra parameters to pass to the `packageManager` when installing versioned packages.\n     * @example ['--no-save']\n     */\n    packageManagerArguments: string[];\n  }\n): Promise<void> {\n  // Read the project Expo config without plugins.\n  const { pkg } = getConfig(projectRoot, {\n    // Sometimes users will add a plugin to the config before installing the library,\n    // this wouldn't work unless we dangerously disable plugin serialization.\n    skipPlugins: true,\n  });\n\n  //assertNotInstallingExcludedPackages(projectRoot, packages, pkg);\n\n  const versioning = await getVersionedPackagesAsync(projectRoot, {\n    packages,\n    // sdkVersion is always defined because we don't skipSDKVersionRequirement in getConfig.\n    sdkVersion,\n    pkg,\n  });\n\n  Log.log(\n    chalk`\\u203A Installing ${\n      versioning.messages.length ? versioning.messages.join(' and ') + ' ' : ''\n    }using {bold ${packageManager.name}}`\n  );\n\n  if (versioning.excludedNativeModules.length) {\n    Log.log(\n      chalk`\\u203A Using latest version instead of ${joinWithCommasAnd(\n        versioning.excludedNativeModules.map(\n          ({ bundledNativeVersion, name }) => `${bundledNativeVersion} for ${name}`\n        )\n      )} because ${\n        versioning.excludedNativeModules.length > 1 ? 'they are' : 'it is'\n      } listed in {bold expo.install.exclude} in package.json. ${learnMore(\n        'https://expo.dev/more/expo-cli/#configuring-dependency-validation'\n      )}`\n    );\n  }\n\n  await packageManager.addAsync([...packageManagerArguments, ...versioning.packages]);\n\n  await applyPluginsAsync(projectRoot, versioning.packages);\n}\n\nexport async function fixPackagesAsync(\n  projectRoot: string,\n  {\n    packages,\n    packageManager,\n    sdkVersion,\n    packageManagerArguments,\n  }: {\n    packages: Awaited<ReturnType<typeof getVersionedDependenciesAsync>>;\n    /** Package manager to use when installing the versioned packages. */\n    packageManager: PackageManager.NodePackageManager;\n    /**\n     * SDK to version `packages` for.\n     * @example '44.0.0'\n     */\n    sdkVersion: string;\n    /**\n     * Extra parameters to pass to the `packageManager` when installing versioned packages.\n     * @example ['--no-save']\n     */\n    packageManagerArguments: string[];\n  }\n): Promise<void> {\n  if (!packages.length) {\n    return;\n  }\n\n  const { dependencies = [], devDependencies = [] } = groupBy(packages, (dep) => dep.packageType);\n  const versioningMessages = getOperationLog({\n    othersCount: 0, // All fixable packages are versioned\n    nativeModulesCount: packages.length,\n    sdkVersion,\n  });\n\n  Log.log(\n    chalk`\\u203A Installing ${\n      versioningMessages.length ? versioningMessages.join(' and ') + ' ' : ''\n    }using {bold ${packageManager.name}}`\n  );\n\n  if (dependencies.length) {\n    const versionedPackages = dependencies.map(\n      (dep) => `${dep.packageName}@${dep.expectedVersionOrRange}`\n    );\n\n    await packageManager.addAsync([...packageManagerArguments, ...versionedPackages]);\n\n    await applyPluginsAsync(projectRoot, versionedPackages);\n  }\n\n  if (devDependencies.length) {\n    await packageManager.addDevAsync([\n      ...packageManagerArguments,\n      ...devDependencies.map((dep) => `${dep.packageName}@${dep.expectedVersionOrRange}`),\n    ]);\n  }\n}\n\n/**\n * A convenience feature for automatically applying Expo Config Plugins to the `app.json` after installing them.\n * This should be dropped in favor of autolinking in the future.\n */\nasync function applyPluginsAsync(projectRoot: string, packages: string[]) {\n  const { autoAddConfigPluginsAsync } = await import('./utils/autoAddConfigPlugins');\n\n  try {\n    const { exp } = getConfig(projectRoot, { skipSDKVersionRequirement: true });\n\n    // Only auto add plugins if the plugins array is defined or if the project is using SDK +42.\n    await autoAddConfigPluginsAsync(\n      projectRoot,\n      exp,\n      // Split any possible NPM tags. i.e. `expo@latest` -> `expo`\n      packages.map((pkg) => pkg.split('@')[0]).filter(Boolean)\n    );\n  } catch (error: any) {\n    // If we fail to apply plugins, the log a warning and continue.\n    if (error.isPluginError) {\n      Log.warn(`Skipping config plugin check: ` + error.message);\n      return;\n    }\n    // Any other error, rethrow.\n    throw error;\n  }\n}\n"], "names": ["installAsync", "installPackagesAsync", "fixPackagesAsync", "PackageManager", "Log", "packages", "options", "packageManagerArguments", "setNodeEnv", "projectRoot", "findUpProjectRootOrAssert", "process", "cwd", "require", "load", "packageManager", "createForProject", "npm", "yarn", "bun", "pnpm", "silent", "log", "check", "fix", "checkPackagesAsync", "exp", "getConfig", "skip<PERSON>lug<PERSON>", "sdkVersion", "pkg", "versioning", "getVersionedPackagesAsync", "chalk", "messages", "length", "join", "name", "excludedNativeModules", "joinWithCommasAnd", "map", "bundledNativeVersion", "learnMore", "addAsync", "applyPluginsAsync", "dependencies", "devDependencies", "groupBy", "dep", "packageType", "versioningMessages", "getOperationLog", "othersCount", "nativeModulesCount", "versionedPackages", "packageName", "expectedVersionOrRange", "addDevAsync", "autoAddConfigPluginsAsync", "skipSDKVersionRequirement", "split", "filter", "Boolean", "error", "isPluginError", "warn", "message"], "mappings": "AAAA;;;;QAkBsBA,YAAY,GAAZA,YAAY;QA+CZC,oBAAoB,GAApBA,oBAAoB;QAoEpBC,gBAAgB,GAAhBA,gBAAgB;AArIZ,IAAA,OAAc,WAAd,cAAc,CAAA;AAC5BC,IAAAA,cAAc,mCAAM,uBAAuB,EAA7B;AACR,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEbC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AAIR,IAAA,qBAAmD,WAAnD,mDAAmD,CAAA;AAElC,IAAA,MAAgB,WAAhB,gBAAgB,CAAA;AACE,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AACjC,IAAA,KAAe,WAAf,eAAe,CAAA;AACd,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACX,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACjB,IAAA,cAAiB,WAAjB,iBAAiB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG7C,eAAeJ,YAAY,CAChCK,QAAkB,EAClBC,OAA2C,EAC3CC,uBAAiC,GAAG,EAAE,EACtC;IACAC,CAAAA,GAAAA,QAAU,AAAe,CAAA,WAAf,CAAC,aAAa,CAAC,CAAC;QAGNF,YAAmB;IAFvC,0EAA0E;IAC1E,mFAAmF;IACnF,MAAMG,WAAW,GAAGH,CAAAA,YAAmB,GAAnBA,OAAO,CAACG,WAAW,YAAnBH,YAAmB,GAAII,CAAAA,GAAAA,OAAyB,AAAe,CAAA,0BAAf,CAACC,OAAO,CAACC,GAAG,EAAE,CAAC,AAAC;IACpFC,OAAO,CAAC,WAAW,CAAC,CAACC,IAAI,CAACL,WAAW,CAAC,CAAC;IAEvC,uFAAuF;IACvF,MAAMM,cAAc,GAAGZ,cAAc,CAACa,gBAAgB,CAACP,WAAW,EAAE;QAClEQ,GAAG,EAAEX,OAAO,CAACW,GAAG;QAChBC,IAAI,EAAEZ,OAAO,CAACY,IAAI;QAClBC,GAAG,EAAEb,OAAO,CAACa,GAAG;QAChBC,IAAI,EAAEd,OAAO,CAACc,IAAI;QAClBC,MAAM,EAAEf,OAAO,CAACe,MAAM;QACtBC,GAAG,EAAElB,GAAG,CAACkB,GAAG;KACb,CAAC,AAAC;IAEH,IAAIhB,OAAO,CAACiB,KAAK,IAAIjB,OAAO,CAACkB,GAAG,EAAE;QAChC,OAAO,MAAMC,CAAAA,GAAAA,cAAkB,AAK7B,CAAA,mBAL6B,CAAChB,WAAW,EAAE;YAC3CJ,QAAQ;YACRC,OAAO;YACPS,cAAc;YACdR,uBAAuB;SACxB,CAAC,CAAC;KACJ;IAED,gDAAgD;IAChD,MAAM,EAAEmB,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAIvB,CAAA,UAJuB,CAAClB,WAAW,EAAE;QACrC,iFAAiF;QACjF,yEAAyE;QACzEmB,WAAW,EAAE,IAAI;KAClB,CAAC,AAAC;IAEH,qDAAqD;IACrD,OAAO3B,oBAAoB,CAACQ,WAAW,EAAE;QACvCM,cAAc;QACdV,QAAQ;QACRE,uBAAuB;QACvBsB,UAAU,EAAEH,GAAG,CAACG,UAAU;KAC3B,CAAC,CAAC;CACJ;AAGM,eAAe5B,oBAAoB,CACxCQ,WAAmB,EACnB,EACEJ,QAAQ,CAAA,EACRU,cAAc,CAAA,EACdc,UAAU,CAAA,EACVtB,uBAAuB,CAAA,EAmBxB,EACc;IACf,gDAAgD;IAChD,MAAM,EAAEuB,GAAG,CAAA,EAAE,GAAGH,CAAAA,GAAAA,OAAS,AAIvB,CAAA,UAJuB,CAAClB,WAAW,EAAE;QACrC,iFAAiF;QACjF,yEAAyE;QACzEmB,WAAW,EAAE,IAAI;KAClB,CAAC,AAAC;IAEH,kEAAkE;IAElE,MAAMG,UAAU,GAAG,MAAMC,CAAAA,GAAAA,qBAAyB,AAKhD,CAAA,0BALgD,CAACvB,WAAW,EAAE;QAC9DJ,QAAQ;QACR,wFAAwF;QACxFwB,UAAU;QACVC,GAAG;KACJ,CAAC,AAAC;IAEH1B,GAAG,CAACkB,GAAG,CACLW,MAAK,QAAA,CAAC,kBAAkB,EACtBF,UAAU,CAACG,QAAQ,CAACC,MAAM,GAAGJ,UAAU,CAACG,QAAQ,CAACE,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,CAC1E,YAAY,EAAErB,cAAc,CAACsB,IAAI,CAAC,CAAC,CAAC,CACtC,CAAC;IAEF,IAAIN,UAAU,CAACO,qBAAqB,CAACH,MAAM,EAAE;QAC3C/B,GAAG,CAACkB,GAAG,CACLW,MAAK,QAAA,CAAC,uCAAuC,EAAEM,CAAAA,GAAAA,QAAiB,AAI/D,CAAA,kBAJ+D,CAC9DR,UAAU,CAACO,qBAAqB,CAACE,GAAG,CAClC,CAAC,EAAEC,oBAAoB,CAAA,EAAEJ,IAAI,CAAA,EAAE,GAAK,CAAC,EAAEI,oBAAoB,CAAC,KAAK,EAAEJ,IAAI,CAAC,CAAC;QAAA,CAC1E,CACF,CAAC,SAAS,EACTN,UAAU,CAACO,qBAAqB,CAACH,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,OAAO,CACnE,wDAAwD,EAAEO,CAAAA,GAAAA,KAAS,AAEnE,CAAA,UAFmE,CAClE,mEAAmE,CACpE,CAAC,CAAC,CACJ,CAAC;KACH;IAED,MAAM3B,cAAc,CAAC4B,QAAQ,CAAC;WAAIpC,uBAAuB;WAAKwB,UAAU,CAAC1B,QAAQ;KAAC,CAAC,CAAC;IAEpF,MAAMuC,iBAAiB,CAACnC,WAAW,EAAEsB,UAAU,CAAC1B,QAAQ,CAAC,CAAC;CAC3D;AAEM,eAAeH,gBAAgB,CACpCO,WAAmB,EACnB,EACEJ,QAAQ,CAAA,EACRU,cAAc,CAAA,EACdc,UAAU,CAAA,EACVtB,uBAAuB,CAAA,EAexB,EACc;IACf,IAAI,CAACF,QAAQ,CAAC8B,MAAM,EAAE;QACpB,OAAO;KACR;IAED,MAAM,EAAEU,YAAY,EAAG,EAAE,CAAA,EAAEC,eAAe,EAAG,EAAE,CAAA,EAAE,GAAGC,CAAAA,GAAAA,MAAO,AAAoC,CAAA,QAApC,CAAC1C,QAAQ,EAAE,CAAC2C,GAAG,GAAKA,GAAG,CAACC,WAAW;IAAA,CAAC,AAAC;IAChG,MAAMC,kBAAkB,GAAGC,CAAAA,GAAAA,qBAAe,AAIxC,CAAA,gBAJwC,CAAC;QACzCC,WAAW,EAAE,CAAC;QACdC,kBAAkB,EAAEhD,QAAQ,CAAC8B,MAAM;QACnCN,UAAU;KACX,CAAC,AAAC;IAEHzB,GAAG,CAACkB,GAAG,CACLW,MAAK,QAAA,CAAC,kBAAkB,EACtBiB,kBAAkB,CAACf,MAAM,GAAGe,kBAAkB,CAACd,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,CACxE,YAAY,EAAErB,cAAc,CAACsB,IAAI,CAAC,CAAC,CAAC,CACtC,CAAC;IAEF,IAAIQ,YAAY,CAACV,MAAM,EAAE;QACvB,MAAMmB,iBAAiB,GAAGT,YAAY,CAACL,GAAG,CACxC,CAACQ,GAAG,GAAK,CAAC,EAAEA,GAAG,CAACO,WAAW,CAAC,CAAC,EAAEP,GAAG,CAACQ,sBAAsB,CAAC,CAAC;QAAA,CAC5D,AAAC;QAEF,MAAMzC,cAAc,CAAC4B,QAAQ,CAAC;eAAIpC,uBAAuB;eAAK+C,iBAAiB;SAAC,CAAC,CAAC;QAElF,MAAMV,iBAAiB,CAACnC,WAAW,EAAE6C,iBAAiB,CAAC,CAAC;KACzD;IAED,IAAIR,eAAe,CAACX,MAAM,EAAE;QAC1B,MAAMpB,cAAc,CAAC0C,WAAW,CAAC;eAC5BlD,uBAAuB;eACvBuC,eAAe,CAACN,GAAG,CAAC,CAACQ,GAAG,GAAK,CAAC,EAAEA,GAAG,CAACO,WAAW,CAAC,CAAC,EAAEP,GAAG,CAACQ,sBAAsB,CAAC,CAAC;YAAA,CAAC;SACpF,CAAC,CAAC;KACJ;CACF;AAED;;;GAGG,CACH,eAAeZ,iBAAiB,CAACnC,WAAmB,EAAEJ,QAAkB,EAAE;IACxE,MAAM,EAAEqD,yBAAyB,CAAA,EAAE,GAAG,MAAM;+CAAO,8BAA8B;MAAC,AAAC;IAEnF,IAAI;QACF,MAAM,EAAEhC,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,OAAS,AAAkD,CAAA,UAAlD,CAAClB,WAAW,EAAE;YAAEkD,yBAAyB,EAAE,IAAI;SAAE,CAAC,AAAC;QAE5E,4FAA4F;QAC5F,MAAMD,yBAAyB,CAC7BjD,WAAW,EACXiB,GAAG,EACH,4DAA4D;QAC5DrB,QAAQ,CAACmC,GAAG,CAAC,CAACV,GAAG,GAAKA,GAAG,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CACzD,CAAC;KACH,CAAC,OAAOC,KAAK,EAAO;QACnB,+DAA+D;QAC/D,IAAIA,KAAK,CAACC,aAAa,EAAE;YACvB5D,GAAG,CAAC6D,IAAI,CAAC,CAAC,8BAA8B,CAAC,GAAGF,KAAK,CAACG,OAAO,CAAC,CAAC;YAC3D,OAAO;SACR;QACD,4BAA4B;QAC5B,MAAMH,KAAK,CAAC;KACb;CACF"}