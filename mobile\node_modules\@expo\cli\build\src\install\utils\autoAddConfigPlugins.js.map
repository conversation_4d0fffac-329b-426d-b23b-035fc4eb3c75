{"version": 3, "sources": ["../../../../src/install/utils/autoAddConfigPlugins.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport {\n  normalizeStaticPlugin,\n  resolveConfigPluginFunctionWithInfo,\n} from '@expo/config-plugins/build/utils/plugin-resolver';\nimport { getAutoPlugins } from '@expo/prebuild-config';\n\nimport { attemptAddingPluginsAsync } from '../../utils/modifyConfigPlugins';\n\nconst debug = require('debug')('expo:install:config-plugins') as typeof console.log;\n\nconst AUTO_PLUGINS = getAutoPlugins();\n\n/**\n * Resolve if a package has a config plugin.\n * For sanity, we'll only support config plugins that use the `app.config.js` entry file,\n * this is because a package like `lodash` could be a \"valid\" config plugin and break the prebuild process.\n *\n * @param projectRoot\n * @param packageName\n * @returns\n */\nfunction packageHasConfigPlugin(projectRoot: string, packageName: string) {\n  try {\n    const info = resolveConfigPluginFunctionWithInfo(projectRoot, packageName);\n    if (info.isPluginFile) {\n      return info.plugin;\n    }\n  } catch {}\n  return false;\n}\n\n/**\n * Get a list of plugins that were are supplied as string module IDs.\n * @example\n * ```json\n * {\n *   \"plugins\": [\n *     \"expo-camera\",\n *     [\"react-native-firebase\", ...]\n *   ]\n * }\n * ```\n *   ↓ ↓ ↓ ↓ ↓ ↓\n *\n * `['expo-camera', 'react-native-firebase']`\n *\n */\nexport function getNamedPlugins(plugins: NonNullable<ExpoConfig['plugins']>): string[] {\n  const namedPlugins: string[] = [];\n  for (const plugin of plugins) {\n    try {\n      // @ts-ignore\n      const [normal] = normalizeStaticPlugin(plugin);\n      if (typeof normal === 'string') {\n        namedPlugins.push(normal);\n      }\n    } catch {\n      // ignore assertions\n    }\n  }\n  return namedPlugins;\n}\n\n/** Attempts to ensure that non-auto plugins are added to the `app.json` `plugins` array when modules with Expo Config Plugins are installed. */\nexport async function autoAddConfigPluginsAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'plugins'>,\n  packages: string[]\n) {\n  debug('Checking config plugins...');\n\n  const currentPlugins = exp.plugins || [];\n  const normalized = getNamedPlugins(currentPlugins);\n\n  debug(`Existing plugins: ${normalized.join(', ')}`);\n\n  const plugins = packages.filter((pkg) => {\n    if (normalized.includes(pkg)) {\n      // already included in plugins array\n      return false;\n    }\n    // Check if the package has a valid plugin. Must be a well-made plugin for it to work with this.\n    const plugin = packageHasConfigPlugin(projectRoot, pkg);\n\n    debug(`Package \"${pkg}\" has plugin: ${!!plugin}` + (plugin ? ` (args: ${plugin.length})` : ''));\n\n    if (AUTO_PLUGINS.includes(pkg)) {\n      debug(`Package \"${pkg}\" is an auto plugin, skipping...`);\n      return false;\n    }\n\n    return !!plugin;\n  });\n\n  await attemptAddingPluginsAsync(projectRoot, exp, plugins);\n}\n"], "names": ["getNamed<PERSON><PERSON><PERSON>", "autoAddConfigPluginsAsync", "debug", "require", "AUTO_PLUGINS", "getAutoPlugins", "packageHasConfigPlugin", "projectRoot", "packageName", "info", "resolveConfigPluginFunctionWithInfo", "isPluginFile", "plugin", "plugins", "<PERSON><PERSON><PERSON><PERSON>", "normal", "normalizeStaticPlugin", "push", "exp", "packages", "currentPlugins", "normalized", "join", "filter", "pkg", "includes", "length", "attemptAddingPluginsAsync"], "mappings": "AAAA;;;;QAgDgBA,eAAe,GAAfA,eAAe;QAiBTC,yBAAyB,GAAzBA,yBAAyB;AA7DxC,IAAA,eAAkD,WAAlD,kDAAkD,CAAA;AAC1B,IAAA,eAAuB,WAAvB,uBAAuB,CAAA;AAEZ,IAAA,oBAAiC,WAAjC,iCAAiC,CAAA;AAE3E,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6BAA6B,CAAC,AAAsB,AAAC;AAEpF,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,eAAc,AAAE,CAAA,eAAF,EAAE,AAAC;AAEtC;;;;;;;;GAQG,CACH,SAASC,sBAAsB,CAACC,WAAmB,EAAEC,WAAmB,EAAE;IACxE,IAAI;QACF,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,eAAmC,AAA0B,CAAA,oCAA1B,CAACH,WAAW,EAAEC,WAAW,CAAC,AAAC;QAC3E,IAAIC,IAAI,CAACE,YAAY,EAAE;YACrB,OAAOF,IAAI,CAACG,MAAM,CAAC;SACpB;KACF,CAAC,OAAM,EAAE;IACV,OAAO,KAAK,CAAC;CACd;AAkBM,SAASZ,eAAe,CAACa,OAA2C,EAAY;IACrF,MAAMC,YAAY,GAAa,EAAE,AAAC;IAClC,KAAK,MAAMF,MAAM,IAAIC,OAAO,CAAE;QAC5B,IAAI;YACF,aAAa;YACb,MAAM,CAACE,MAAM,CAAC,GAAGC,CAAAA,GAAAA,eAAqB,AAAQ,CAAA,sBAAR,CAACJ,MAAM,CAAC,AAAC;YAC/C,IAAI,OAAOG,MAAM,KAAK,QAAQ,EAAE;gBAC9BD,YAAY,CAACG,IAAI,CAACF,MAAM,CAAC,CAAC;aAC3B;SACF,CAAC,OAAM;QACN,oBAAoB;SACrB;KACF;IACD,OAAOD,YAAY,CAAC;CACrB;AAGM,eAAeb,yBAAyB,CAC7CM,WAAmB,EACnBW,GAAgC,EAChCC,QAAkB,EAClB;IACAjB,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAEpC,MAAMkB,cAAc,GAAGF,GAAG,CAACL,OAAO,IAAI,EAAE,AAAC;IACzC,MAAMQ,UAAU,GAAGrB,eAAe,CAACoB,cAAc,CAAC,AAAC;IAEnDlB,KAAK,CAAC,CAAC,kBAAkB,EAAEmB,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpD,MAAMT,OAAO,GAAGM,QAAQ,CAACI,MAAM,CAAC,CAACC,GAAG,GAAK;QACvC,IAAIH,UAAU,CAACI,QAAQ,CAACD,GAAG,CAAC,EAAE;YAC5B,oCAAoC;YACpC,OAAO,KAAK,CAAC;SACd;QACD,gGAAgG;QAChG,MAAMZ,MAAM,GAAGN,sBAAsB,CAACC,WAAW,EAAEiB,GAAG,CAAC,AAAC;QAExDtB,KAAK,CAAC,CAAC,SAAS,EAAEsB,GAAG,CAAC,cAAc,EAAE,CAAC,CAACZ,MAAM,CAAC,CAAC,GAAG,CAACA,MAAM,GAAG,CAAC,QAAQ,EAAEA,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAEhG,IAAItB,YAAY,CAACqB,QAAQ,CAACD,GAAG,CAAC,EAAE;YAC9BtB,KAAK,CAAC,CAAC,SAAS,EAAEsB,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;SACd;QAED,OAAO,CAAC,CAACZ,MAAM,CAAC;KACjB,CAAC,AAAC;IAEH,MAAMe,CAAAA,GAAAA,oBAAyB,AAA2B,CAAA,0BAA3B,CAACpB,WAAW,EAAEW,GAAG,EAAEL,OAAO,CAAC,CAAC;CAC5D"}