{"version": 3, "sources": ["../../../src/prebuild/prebuildAsync.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\n\nimport { installAsync } from '../install/installAsync';\nimport { env } from '../utils/env';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { clearNodeModulesAsync } from '../utils/nodeModules';\nimport { logNewSection } from '../utils/ora';\nimport { profile } from '../utils/profile';\nimport { clearNativeFolder, promptToClearMalformedNativeProjectsAsync } from './clearNativeFolder';\nimport { configureProjectAsync } from './configureProjectAsync';\nimport { ensureConfigAsync } from './ensureConfigAsync';\nimport { assertPlatforms, ensureValidPlatforms, resolveTemplateOption } from './resolveOptions';\nimport { updateFromTemplateAsync } from './updateFromTemplate';\n\nconst debug = require('debug')('expo:prebuild') as typeof console.log;\n\nexport type PrebuildResults = {\n  /** Expo config. */\n  exp: ExpoConfig;\n  /** Indicates if the process created new files. */\n  hasNewProjectFiles: boolean;\n  /** The platforms that were prebuilt. */\n  platforms: ModPlatform[];\n  /** Indicates if pod install was run. */\n  podInstall: boolean;\n  /** Indicates if node modules were installed. */\n  nodeInstall: boolean;\n};\n\n/**\n * Entry point into the prebuild process, delegates to other helpers to perform various steps.\n *\n * 0. Attempt to clean the project folders.\n * 1. Create native projects (ios, android).\n * 2. Install node modules.\n * 3. Apply config to native projects.\n * 4. Install CocoaPods.\n */\nexport async function prebuildAsync(\n  projectRoot: string,\n  options: {\n    /** Should install node modules and cocoapods. */\n    install?: boolean;\n    /** List of platforms to prebuild. */\n    platforms: ModPlatform[];\n    /** Should delete the native folders before attempting to prebuild. */\n    clean?: boolean;\n    /** URL or file path to the prebuild template. */\n    template?: string;\n    /** Name of the node package manager to install with. */\n    packageManager?: {\n      npm?: boolean;\n      yarn?: boolean;\n      pnpm?: boolean;\n      bun?: boolean;\n    };\n    /** List of node modules to skip updating. */\n    skipDependencyUpdate?: string[];\n  }\n): Promise<PrebuildResults | null> {\n  setNodeEnv('development');\n  require('@expo/env').load(projectRoot);\n\n  if (options.clean) {\n    const { maybeBailOnGitStatusAsync } = await import('../utils/git');\n    // Clean the project folders...\n    if (await maybeBailOnGitStatusAsync()) {\n      return null;\n    }\n    // Clear the native folders before syncing\n    await clearNativeFolder(projectRoot, options.platforms);\n  } else {\n    // Check if the existing project folders are malformed.\n    await promptToClearMalformedNativeProjectsAsync(projectRoot, options.platforms);\n  }\n\n  // Warn if the project is attempting to prebuild an unsupported platform (iOS on Windows).\n  options.platforms = ensureValidPlatforms(options.platforms);\n  // Assert if no platforms are left over after filtering.\n  assertPlatforms(options.platforms);\n\n  // Get the Expo config, create it if missing.\n  const { exp, pkg } = await ensureConfigAsync(projectRoot, { platforms: options.platforms });\n\n  // Create native projects from template.\n  const { hasNewProjectFiles, needsPodInstall, hasNewDependencies } = await updateFromTemplateAsync(\n    projectRoot,\n    {\n      exp,\n      pkg,\n      template: options.template != null ? resolveTemplateOption(options.template) : undefined,\n      platforms: options.platforms,\n      skipDependencyUpdate: options.skipDependencyUpdate,\n    }\n  );\n\n  // Install node modules\n  if (options.install) {\n    if (hasNewDependencies && options.packageManager?.npm) {\n      await clearNodeModulesAsync(projectRoot);\n    }\n\n    await installAsync([], {\n      npm: !!options.packageManager?.npm,\n      yarn: !!options.packageManager?.yarn,\n      pnpm: !!options.packageManager?.pnpm,\n      bun: !!options.packageManager?.bun,\n      silent: !(env.EXPO_DEBUG || env.CI),\n    });\n  }\n\n  // Apply Expo config to native projects\n  const configSyncingStep = logNewSection('Config syncing');\n  try {\n    await profile(configureProjectAsync)(projectRoot, {\n      platforms: options.platforms,\n    });\n    configSyncingStep.succeed('Config synced');\n  } catch (error) {\n    configSyncingStep.fail('Config sync failed');\n    throw error;\n  }\n\n  // Install CocoaPods\n  let podsInstalled: boolean = false;\n  // err towards running pod install less because it's slow and users can easily run npx pod-install afterwards.\n  if (options.platforms.includes('ios') && options.install && needsPodInstall) {\n    const { installCocoaPodsAsync } = await import('../utils/cocoapods');\n\n    podsInstalled = await installCocoaPodsAsync(projectRoot);\n  } else {\n    debug('Skipped pod install');\n  }\n\n  return {\n    nodeInstall: !!options.install,\n    podInstall: !podsInstalled,\n    platforms: options.platforms,\n    hasNewProjectFiles,\n    exp,\n  };\n}\n"], "names": ["prebuildAsync", "debug", "require", "projectRoot", "options", "setNodeEnv", "load", "clean", "maybeBailOnGitStatusAsync", "clearNativeFolder", "platforms", "promptToClearMalformedNativeProjectsAsync", "ensureValidPlatforms", "assertPlatforms", "exp", "pkg", "ensureConfigAsync", "hasNewProjectFiles", "needsPodInstall", "hasNewDependencies", "updateFromTemplateAsync", "template", "resolveTemplateOption", "undefined", "skipDependencyUpdate", "install", "packageManager", "npm", "clearNodeModulesAsync", "installAsync", "yarn", "pnpm", "bun", "silent", "env", "EXPO_DEBUG", "CI", "configSyncingStep", "logNewSection", "profile", "configureProjectAsync", "succeed", "error", "fail", "podsInstalled", "includes", "installCocoaPodsAsync", "nodeInstall", "podInstall"], "mappings": "AAAA;;;;QAuCsBA,aAAa,GAAbA,aAAa;AApCN,IAAA,aAAyB,WAAzB,yBAAyB,CAAA;AAClC,IAAA,IAAc,WAAd,cAAc,CAAA;AACP,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACP,IAAA,YAAsB,WAAtB,sBAAsB,CAAA;AAC9B,IAAA,IAAc,WAAd,cAAc,CAAA;AACpB,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACmC,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AAC5D,IAAA,sBAAyB,WAAzB,yBAAyB,CAAA;AAC7B,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AACsB,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AACvD,IAAA,mBAAsB,WAAtB,sBAAsB,CAAA;;;;;;;;;;;;;;;;;;;;;;AAE9D,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,AAAsB,AAAC;AAwB/D,eAAeF,aAAa,CACjCG,WAAmB,EACnBC,OAkBC,EACgC;IACjCC,CAAAA,GAAAA,QAAU,AAAe,CAAA,WAAf,CAAC,aAAa,CAAC,CAAC;IAC1BH,OAAO,CAAC,WAAW,CAAC,CAACI,IAAI,CAACH,WAAW,CAAC,CAAC;IAEvC,IAAIC,OAAO,CAACG,KAAK,EAAE;QACjB,MAAM,EAAEC,yBAAyB,CAAA,EAAE,GAAG,MAAM;mDAAO,cAAc;UAAC,AAAC;QACnE,+BAA+B;QAC/B,IAAI,MAAMA,yBAAyB,EAAE,EAAE;YACrC,OAAO,IAAI,CAAC;SACb;QACD,0CAA0C;QAC1C,MAAMC,CAAAA,GAAAA,kBAAiB,AAAgC,CAAA,kBAAhC,CAACN,WAAW,EAAEC,OAAO,CAACM,SAAS,CAAC,CAAC;KACzD,MAAM;QACL,uDAAuD;QACvD,MAAMC,CAAAA,GAAAA,kBAAyC,AAAgC,CAAA,0CAAhC,CAACR,WAAW,EAAEC,OAAO,CAACM,SAAS,CAAC,CAAC;KACjF;IAED,0FAA0F;IAC1FN,OAAO,CAACM,SAAS,GAAGE,CAAAA,GAAAA,eAAoB,AAAmB,CAAA,qBAAnB,CAACR,OAAO,CAACM,SAAS,CAAC,CAAC;IAC5D,wDAAwD;IACxDG,CAAAA,GAAAA,eAAe,AAAmB,CAAA,gBAAnB,CAACT,OAAO,CAACM,SAAS,CAAC,CAAC;IAEnC,6CAA6C;IAC7C,MAAM,EAAEI,GAAG,CAAA,EAAEC,GAAG,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,kBAAiB,AAA+C,CAAA,kBAA/C,CAACb,WAAW,EAAE;QAAEO,SAAS,EAAEN,OAAO,CAACM,SAAS;KAAE,CAAC,AAAC;IAE5F,wCAAwC;IACxC,MAAM,EAAEO,kBAAkB,CAAA,EAAEC,eAAe,CAAA,EAAEC,kBAAkB,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,mBAAuB,AAShG,CAAA,wBATgG,CAC/FjB,WAAW,EACX;QACEW,GAAG;QACHC,GAAG;QACHM,QAAQ,EAAEjB,OAAO,CAACiB,QAAQ,IAAI,IAAI,GAAGC,CAAAA,GAAAA,eAAqB,AAAkB,CAAA,sBAAlB,CAAClB,OAAO,CAACiB,QAAQ,CAAC,GAAGE,SAAS;QACxFb,SAAS,EAAEN,OAAO,CAACM,SAAS;QAC5Bc,oBAAoB,EAAEpB,OAAO,CAACoB,oBAAoB;KACnD,CACF,AAAC;IAEF,uBAAuB;IACvB,IAAIpB,OAAO,CAACqB,OAAO,EAAE;YACOrB,GAAsB,EAKvCA,IAAsB,EACrBA,IAAsB,EACtBA,IAAsB,EACvBA,IAAsB;QAR/B,IAAIe,kBAAkB,IAAIf,CAAAA,CAAAA,GAAsB,GAAtBA,OAAO,CAACsB,cAAc,SAAK,GAA3BtB,KAAAA,CAA2B,GAA3BA,GAAsB,CAAEuB,GAAG,CAAA,EAAE;YACrD,MAAMC,CAAAA,GAAAA,YAAqB,AAAa,CAAA,sBAAb,CAACzB,WAAW,CAAC,CAAC;SAC1C;QAED,MAAM0B,CAAAA,GAAAA,aAAY,AAMhB,CAAA,aANgB,CAAC,EAAE,EAAE;YACrBF,GAAG,EAAE,CAAC,CAACvB,CAAAA,CAAAA,IAAsB,GAAtBA,OAAO,CAACsB,cAAc,SAAK,GAA3BtB,KAAAA,CAA2B,GAA3BA,IAAsB,CAAEuB,GAAG,CAAA;YAClCG,IAAI,EAAE,CAAC,CAAC1B,CAAAA,CAAAA,IAAsB,GAAtBA,OAAO,CAACsB,cAAc,SAAM,GAA5BtB,KAAAA,CAA4B,GAA5BA,IAAsB,CAAE0B,IAAI,CAAA;YACpCC,IAAI,EAAE,CAAC,CAAC3B,CAAAA,CAAAA,IAAsB,GAAtBA,OAAO,CAACsB,cAAc,SAAM,GAA5BtB,KAAAA,CAA4B,GAA5BA,IAAsB,CAAE2B,IAAI,CAAA;YACpCC,GAAG,EAAE,CAAC,CAAC5B,CAAAA,CAAAA,IAAsB,GAAtBA,OAAO,CAACsB,cAAc,SAAK,GAA3BtB,KAAAA,CAA2B,GAA3BA,IAAsB,CAAE4B,GAAG,CAAA;YAClCC,MAAM,EAAE,CAAC,CAACC,IAAG,IAAA,CAACC,UAAU,IAAID,IAAG,IAAA,CAACE,EAAE,CAAC;SACpC,CAAC,CAAC;KACJ;IAED,uCAAuC;IACvC,MAAMC,iBAAiB,GAAGC,CAAAA,GAAAA,IAAa,AAAkB,CAAA,cAAlB,CAAC,gBAAgB,CAAC,AAAC;IAC1D,IAAI;QACF,MAAMC,CAAAA,GAAAA,QAAO,AAAuB,CAAA,QAAvB,CAACC,sBAAqB,sBAAA,CAAC,CAACrC,WAAW,EAAE;YAChDO,SAAS,EAAEN,OAAO,CAACM,SAAS;SAC7B,CAAC,CAAC;QACH2B,iBAAiB,CAACI,OAAO,CAAC,eAAe,CAAC,CAAC;KAC5C,CAAC,OAAOC,KAAK,EAAE;QACdL,iBAAiB,CAACM,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC7C,MAAMD,KAAK,CAAC;KACb;IAED,oBAAoB;IACpB,IAAIE,aAAa,GAAY,KAAK,AAAC;IACnC,8GAA8G;IAC9G,IAAIxC,OAAO,CAACM,SAAS,CAACmC,QAAQ,CAAC,KAAK,CAAC,IAAIzC,OAAO,CAACqB,OAAO,IAAIP,eAAe,EAAE;QAC3E,MAAM,EAAE4B,qBAAqB,CAAA,EAAE,GAAG,MAAM;mDAAO,oBAAoB;UAAC,AAAC;QAErEF,aAAa,GAAG,MAAME,qBAAqB,CAAC3C,WAAW,CAAC,CAAC;KAC1D,MAAM;QACLF,KAAK,CAAC,qBAAqB,CAAC,CAAC;KAC9B;IAED,OAAO;QACL8C,WAAW,EAAE,CAAC,CAAC3C,OAAO,CAACqB,OAAO;QAC9BuB,UAAU,EAAE,CAACJ,aAAa;QAC1BlC,SAAS,EAAEN,OAAO,CAACM,SAAS;QAC5BO,kBAAkB;QAClBH,GAAG;KACJ,CAAC;CACH"}