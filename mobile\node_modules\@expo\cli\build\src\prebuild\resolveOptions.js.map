{"version": 3, "sources": ["../../../src/prebuild/resolveOptions.ts"], "sourcesContent": ["import { ModPlatform } from '@expo/config-plugins';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { CommandError } from '../utils/errors';\nimport { validateUrl } from '../utils/url';\n\nexport function resolvePackageManagerOptions(args: any) {\n  const managers: Record<string, boolean> = {\n    npm: args['--npm'],\n    yarn: args['--yarn'],\n    pnpm: args['--pnpm'],\n    bun: args['--bun'],\n  };\n\n  if (\n    [managers.npm, managers.pnpm, managers.yarn, managers.bun, !!args['--no-install']].filter(\n      Boolean\n    ).length > 1\n  ) {\n    throw new CommandError(\n      'BAD_ARGS',\n      'Specify at most one of: --no-install, --npm, --pnpm, --yarn, --bun'\n    );\n  }\n\n  return managers;\n}\n\n/** Resolves a template option as a URL or file path pointing to a tar file. */\nexport function resolveTemplateOption(template: string) {\n  if (validateUrl(template)) {\n    return template;\n  }\n  const templatePath = path.resolve(template);\n  assert(fs.existsSync(templatePath), 'template file does not exist: ' + templatePath);\n  assert(\n    fs.statSync(templatePath).isFile(),\n    'template must be a tar file created by running `npm pack` in a project: ' + templatePath\n  );\n\n  return templatePath;\n}\n\n/** Resolves dependencies to skip from a string joined by `,`. Example: `react-native,expo,lodash` */\nexport function resolveSkipDependencyUpdate(value: any) {\n  if (!value || typeof value !== 'string') {\n    return [];\n  }\n  return value.split(',');\n}\n\n/** Returns an array of platforms based on the input platform identifier and runtime constraints. */\nexport function resolvePlatformOption(\n  platform: string = 'all',\n  { loose }: { loose?: boolean } = {}\n): ModPlatform[] {\n  switch (platform) {\n    case 'ios':\n      return ['ios'];\n    case 'android':\n      return ['android'];\n    case 'all':\n      return loose || process.platform !== 'win32' ? ['android', 'ios'] : ['android'];\n    default:\n      return [platform as ModPlatform];\n  }\n}\n\n/** Warns and filters out unsupported platforms based on the runtime constraints. Essentially this means no iOS on Windows devices. */\nexport function ensureValidPlatforms(platforms: ModPlatform[]): ModPlatform[] {\n  // Skip ejecting for iOS on Windows\n  if (process.platform === 'win32' && platforms.includes('ios')) {\n    Log.warn(\n      chalk`⚠️  Skipping generating the iOS native project files. Run {bold expo eject} again from macOS or Linux to generate the iOS project.\\n`\n    );\n    return platforms.filter((platform) => platform !== 'ios');\n  }\n  return platforms;\n}\n\n/** Asserts platform length must be greater than zero. */\nexport function assertPlatforms(platforms: ModPlatform[]) {\n  if (!platforms?.length) {\n    throw new CommandError('At least one platform must be enabled when syncing');\n  }\n}\n"], "names": ["resolvePackageManagerOptions", "resolveTemplateOption", "resolveSkipDependencyUpdate", "resolvePlatformOption", "ensureValidPlatforms", "assertPlatforms", "Log", "args", "managers", "npm", "yarn", "pnpm", "bun", "filter", "Boolean", "length", "CommandError", "template", "validateUrl", "templatePath", "path", "resolve", "assert", "fs", "existsSync", "statSync", "isFile", "value", "split", "platform", "loose", "process", "platforms", "includes", "warn", "chalk"], "mappings": "AAAA;;;;QAUgBA,4BAA4B,GAA5BA,4BAA4B;QAuB5BC,qBAAqB,GAArBA,qBAAqB;QAerBC,2BAA2B,GAA3BA,2BAA2B;QAQ3BC,qBAAqB,GAArBA,qBAAqB;QAiBrBC,oBAAoB,GAApBA,oBAAoB;QAYpBC,eAAe,GAAfA,eAAe;AApFZ,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACT,IAAA,MAAO,kCAAP,OAAO,EAAA;AACV,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEXC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACc,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AAClB,IAAA,IAAc,WAAd,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,SAASN,4BAA4B,CAACO,IAAS,EAAE;IACtD,MAAMC,QAAQ,GAA4B;QACxCC,GAAG,EAAEF,IAAI,CAAC,OAAO,CAAC;QAClBG,IAAI,EAAEH,IAAI,CAAC,QAAQ,CAAC;QACpBI,IAAI,EAAEJ,IAAI,CAAC,QAAQ,CAAC;QACpBK,GAAG,EAAEL,IAAI,CAAC,OAAO,CAAC;KACnB,AAAC;IAEF,IACE;QAACC,QAAQ,CAACC,GAAG;QAAED,QAAQ,CAACG,IAAI;QAAEH,QAAQ,CAACE,IAAI;QAAEF,QAAQ,CAACI,GAAG;QAAE,CAAC,CAACL,IAAI,CAAC,cAAc,CAAC;KAAC,CAACM,MAAM,CACvFC,OAAO,CACR,CAACC,MAAM,GAAG,CAAC,EACZ;QACA,MAAM,IAAIC,OAAY,aAAA,CACpB,UAAU,EACV,oEAAoE,CACrE,CAAC;KACH;IAED,OAAOR,QAAQ,CAAC;CACjB;AAGM,SAASP,qBAAqB,CAACgB,QAAgB,EAAE;IACtD,IAAIC,CAAAA,GAAAA,IAAW,AAAU,CAAA,YAAV,CAACD,QAAQ,CAAC,EAAE;QACzB,OAAOA,QAAQ,CAAC;KACjB;IACD,MAAME,YAAY,GAAGC,KAAI,QAAA,CAACC,OAAO,CAACJ,QAAQ,CAAC,AAAC;IAC5CK,CAAAA,GAAAA,OAAM,AAA8E,CAAA,QAA9E,CAACC,GAAE,QAAA,CAACC,UAAU,CAACL,YAAY,CAAC,EAAE,gCAAgC,GAAGA,YAAY,CAAC,CAAC;IACrFG,CAAAA,GAAAA,OAAM,AAGL,CAAA,QAHK,CACJC,GAAE,QAAA,CAACE,QAAQ,CAACN,YAAY,CAAC,CAACO,MAAM,EAAE,EAClC,0EAA0E,GAAGP,YAAY,CAC1F,CAAC;IAEF,OAAOA,YAAY,CAAC;CACrB;AAGM,SAASjB,2BAA2B,CAACyB,KAAU,EAAE;IACtD,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACvC,OAAO,EAAE,CAAC;KACX;IACD,OAAOA,KAAK,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC;CACzB;AAGM,SAASzB,qBAAqB,CACnC0B,QAAgB,GAAG,KAAK,EACxB,EAAEC,KAAK,CAAA,EAAuB,GAAG,EAAE,EACpB;IACf,OAAQD,QAAQ;QACd,KAAK,KAAK;YACR,OAAO;gBAAC,KAAK;aAAC,CAAC;QACjB,KAAK,SAAS;YACZ,OAAO;gBAAC,SAAS;aAAC,CAAC;QACrB,KAAK,KAAK;YACR,OAAOC,KAAK,IAAIC,OAAO,CAACF,QAAQ,KAAK,OAAO,GAAG;gBAAC,SAAS;gBAAE,KAAK;aAAC,GAAG;gBAAC,SAAS;aAAC,CAAC;QAClF;YACE,OAAO;gBAACA,QAAQ;aAAgB,CAAC;KACpC;CACF;AAGM,SAASzB,oBAAoB,CAAC4B,SAAwB,EAAiB;IAC5E,mCAAmC;IACnC,IAAID,OAAO,CAACF,QAAQ,KAAK,OAAO,IAAIG,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7D3B,GAAG,CAAC4B,IAAI,CACNC,MAAK,QAAA,CAAC,wIAAoI,CAAC,CAC5I,CAAC;QACF,OAAOH,SAAS,CAACnB,MAAM,CAAC,CAACgB,QAAQ,GAAKA,QAAQ,KAAK,KAAK;QAAA,CAAC,CAAC;KAC3D;IACD,OAAOG,SAAS,CAAC;CAClB;AAGM,SAAS3B,eAAe,CAAC2B,SAAwB,EAAE;IACxD,IAAI,CAACA,CAAAA,SAAS,QAAQ,GAAjBA,KAAAA,CAAiB,GAAjBA,SAAS,CAAEjB,MAAM,CAAA,EAAE;QACtB,MAAM,IAAIC,OAAY,aAAA,CAAC,oDAAoD,CAAC,CAAC;KAC9E;CACF"}