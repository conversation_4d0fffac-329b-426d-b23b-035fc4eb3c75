"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.cloneTemplateAsync = cloneTemplateAsync;
exports.resolveTemplateArgAsync = resolveTemplateArgAsync;
var _chalk = _interopRequireDefault(require("chalk"));
var _fs = _interopRequireDefault(require("fs"));
var _path = _interopRequireDefault(require("path"));
var _semver = _interopRequireDefault(require("semver"));
var _client = require("../api/rest/client");
var Log = _interopRequireWildcard(require("../log"));
var _errors = require("../utils/errors");
var _npm = require("../utils/npm");
var _url = require("../utils/url");
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _interopRequireWildcard(obj) {
    if (obj && obj.__esModule) {
        return obj;
    } else {
        var newObj = {};
        if (obj != null) {
            for(var key in obj){
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};
                    if (desc.get || desc.set) {
                        Object.defineProperty(newObj, key, desc);
                    } else {
                        newObj[key] = obj[key];
                    }
                }
            }
        }
        newObj.default = obj;
        return newObj;
    }
}
const debug = require("debug")("expo:prebuild:resolveTemplate");
async function cloneTemplateAsync({ templateDirectory , template , exp , ora  }) {
    if (template) {
        await resolveTemplateArgAsync(templateDirectory, ora, exp.name, template);
    } else {
        const templatePackageName = await getTemplateNpmPackageName(exp.sdkVersion);
        await (0, _npm).downloadAndExtractNpmModuleAsync(templatePackageName, {
            cwd: templateDirectory,
            name: exp.name
        });
    }
}
/** Given an `sdkVersion` like `44.0.0` return a fully qualified NPM package name like: `expo-template-bare-minimum@sdk-44` */ function getTemplateNpmPackageName(sdkVersion) {
    // When undefined or UNVERSIONED, we use the latest version.
    if (!sdkVersion || sdkVersion === "UNVERSIONED") {
        Log.log("Using an unspecified Expo SDK version. The latest template will be used.");
        return `expo-template-bare-minimum@latest`;
    }
    return `expo-template-bare-minimum@sdk-${_semver.default.major(sdkVersion)}`;
}
async function getRepoInfo(url, examplePath) {
    const [, username, name, t, _branch, ...file] = url.pathname.split("/");
    const filePath = examplePath ? examplePath.replace(/^\//, "") : file.join("/");
    // Support repos whose entire purpose is to be an example, e.g.
    // https://github.com/:username/:my-cool-example-repo-name.
    if (t === undefined) {
        const infoResponse = await (0, _client).fetchAsync(`https://api.github.com/repos/${username}/${name}`);
        if (infoResponse.status !== 200) {
            return;
        }
        const info = await infoResponse.json();
        return {
            username,
            name,
            branch: info["default_branch"],
            filePath
        };
    }
    // If examplePath is available, the branch name takes the entire path
    const branch = examplePath ? `${_branch}/${file.join("/")}`.replace(new RegExp(`/${filePath}|/$`), "") : _branch;
    if (username && name && branch && t === "tree") {
        return {
            username,
            name,
            branch,
            filePath
        };
    }
    return undefined;
}
function hasRepo({ username , name , branch , filePath  }) {
    const contentsUrl = `https://api.github.com/repos/${username}/${name}/contents`;
    const packagePath = `${filePath ? `/${filePath}` : ""}/package.json`;
    return (0, _url).isUrlOk(contentsUrl + packagePath + `?ref=${branch}`);
}
async function downloadAndExtractRepoAsync(root, { username , name , branch , filePath  }) {
    const projectName = _path.default.basename(root);
    const strip = filePath ? filePath.split("/").length + 1 : 1;
    const url = `https://codeload.github.com/${username}/${name}/tar.gz/${branch}`;
    debug("Downloading tarball from:", url);
    await (0, _npm).extractNpmTarballFromUrlAsync(url, {
        cwd: root,
        name: projectName,
        strip,
        fileList: [
            `${name}-${branch}${filePath ? `/${filePath}` : ""}`
        ]
    });
}
async function resolveTemplateArgAsync(templateDirectory, oraInstance, appName, template, templatePath) {
    let repoInfo;
    if (template) {
        // @ts-ignore
        let repoUrl;
        try {
            // @ts-ignore
            repoUrl = new URL(template);
        } catch (error) {
            if (error.code !== "ERR_INVALID_URL") {
                oraInstance.fail(error);
                throw error;
            }
        }
        // On Windows, we can actually create a URL from a local path
        // Double-check if the created URL is not a path to avoid mixing up URLs and paths
        if (process.platform === "win32" && repoUrl && _path.default.isAbsolute(repoUrl.toString())) {
            repoUrl = undefined;
        }
        if (!repoUrl) {
            const templatePath = _path.default.resolve(template);
            if (!_fs.default.existsSync(templatePath)) {
                throw new _errors.CommandError(`template file does not exist: ${templatePath}`);
            }
            await (0, _npm).extractLocalNpmTarballAsync(templatePath, {
                cwd: templateDirectory,
                name: appName
            });
            return templateDirectory;
        }
        if (repoUrl.origin !== "https://github.com") {
            oraInstance.fail(`Invalid URL: ${_chalk.default.red(`"${template}"`)}. Only GitHub repositories are supported. Please use a GitHub URL and try again.`);
            throw new _errors.AbortCommandError();
        }
        repoInfo = await getRepoInfo(repoUrl, templatePath);
        if (!repoInfo) {
            oraInstance.fail(`Found invalid GitHub URL: ${_chalk.default.red(`"${template}"`)}. Please fix the URL and try again.`);
            throw new _errors.AbortCommandError();
        }
        const found = await hasRepo(repoInfo);
        if (!found) {
            oraInstance.fail(`Could not locate the repository for ${_chalk.default.red(`"${template}"`)}. Please check that the repository exists and try again.`);
            throw new _errors.AbortCommandError();
        }
    }
    if (repoInfo) {
        oraInstance.text = _chalk.default.bold(`Downloading files from repo ${_chalk.default.cyan(template)}. This might take a moment.`);
        await downloadAndExtractRepoAsync(templateDirectory, repoInfo);
    }
    return true;
}

//# sourceMappingURL=resolveTemplate.js.map