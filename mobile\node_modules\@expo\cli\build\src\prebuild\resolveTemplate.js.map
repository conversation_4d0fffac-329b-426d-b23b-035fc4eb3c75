{"version": 3, "sources": ["../../../src/prebuild/resolveTemplate.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport { Ora } from 'ora';\nimport path from 'path';\nimport semver from 'semver';\n\nimport { fetchAsync } from '../api/rest/client';\nimport * as Log from '../log';\nimport { AbortCommandError, CommandError } from '../utils/errors';\nimport {\n  downloadAndExtractNpmModuleAsync,\n  extractLocalNpmTarballAsync,\n  extractNpmTarballFromUrlAsync,\n} from '../utils/npm';\nimport { isUrlOk } from '../utils/url';\n\nconst debug = require('debug')('expo:prebuild:resolveTemplate') as typeof console.log;\n\ntype RepoInfo = {\n  username: string;\n  name: string;\n  branch: string;\n  filePath: string;\n};\n\nexport async function cloneTemplateAsync({\n  templateDirectory,\n  template,\n  exp,\n  ora,\n}: {\n  templateDirectory: string;\n  template?: string;\n  exp: Pick<ExpoConfig, 'name' | 'sdkVersion'>;\n  ora: Ora;\n}) {\n  if (template) {\n    await resolveTemplateArgAsync(templateDirectory, ora, exp.name, template);\n  } else {\n    const templatePackageName = await getTemplateNpmPackageName(exp.sdkVersion);\n    await downloadAndExtractNpmModuleAsync(templatePackageName, {\n      cwd: templateDirectory,\n      name: exp.name,\n    });\n  }\n}\n\n/** Given an `sdkVersion` like `44.0.0` return a fully qualified NPM package name like: `expo-template-bare-minimum@sdk-44` */\nfunction getTemplateNpmPackageName(sdkVersion?: string): string {\n  // When undefined or UNVERSIONED, we use the latest version.\n  if (!sdkVersion || sdkVersion === 'UNVERSIONED') {\n    Log.log('Using an unspecified Expo SDK version. The latest template will be used.');\n    return `expo-template-bare-minimum@latest`;\n  }\n  return `expo-template-bare-minimum@sdk-${semver.major(sdkVersion)}`;\n}\n\nasync function getRepoInfo(url: any, examplePath?: string): Promise<RepoInfo | undefined> {\n  const [, username, name, t, _branch, ...file] = url.pathname.split('/');\n  const filePath = examplePath ? examplePath.replace(/^\\//, '') : file.join('/');\n\n  // Support repos whose entire purpose is to be an example, e.g.\n  // https://github.com/:username/:my-cool-example-repo-name.\n  if (t === undefined) {\n    const infoResponse = await fetchAsync(`https://api.github.com/repos/${username}/${name}`);\n    if (infoResponse.status !== 200) {\n      return;\n    }\n    const info = await infoResponse.json();\n    return { username, name, branch: info['default_branch'], filePath };\n  }\n\n  // If examplePath is available, the branch name takes the entire path\n  const branch = examplePath\n    ? `${_branch}/${file.join('/')}`.replace(new RegExp(`/${filePath}|/$`), '')\n    : _branch;\n\n  if (username && name && branch && t === 'tree') {\n    return { username, name, branch, filePath };\n  }\n  return undefined;\n}\n\nfunction hasRepo({ username, name, branch, filePath }: RepoInfo) {\n  const contentsUrl = `https://api.github.com/repos/${username}/${name}/contents`;\n  const packagePath = `${filePath ? `/${filePath}` : ''}/package.json`;\n\n  return isUrlOk(contentsUrl + packagePath + `?ref=${branch}`);\n}\n\nasync function downloadAndExtractRepoAsync(\n  root: string,\n  { username, name, branch, filePath }: RepoInfo\n): Promise<void> {\n  const projectName = path.basename(root);\n\n  const strip = filePath ? filePath.split('/').length + 1 : 1;\n\n  const url = `https://codeload.github.com/${username}/${name}/tar.gz/${branch}`;\n  debug('Downloading tarball from:', url);\n  await extractNpmTarballFromUrlAsync(url, {\n    cwd: root,\n    name: projectName,\n    strip,\n    fileList: [`${name}-${branch}${filePath ? `/${filePath}` : ''}`],\n  });\n}\n\nexport async function resolveTemplateArgAsync(\n  templateDirectory: string,\n  oraInstance: Ora,\n  appName: string,\n  template: string,\n  templatePath?: string\n) {\n  let repoInfo: RepoInfo | undefined;\n\n  if (template) {\n    // @ts-ignore\n    let repoUrl: URL | undefined;\n\n    try {\n      // @ts-ignore\n      repoUrl = new URL(template);\n    } catch (error: any) {\n      if (error.code !== 'ERR_INVALID_URL') {\n        oraInstance.fail(error);\n        throw error;\n      }\n    }\n\n    // On Windows, we can actually create a URL from a local path\n    // Double-check if the created URL is not a path to avoid mixing up URLs and paths\n    if (process.platform === 'win32' && repoUrl && path.isAbsolute(repoUrl.toString())) {\n      repoUrl = undefined;\n    }\n\n    if (!repoUrl) {\n      const templatePath = path.resolve(template);\n      if (!fs.existsSync(templatePath)) {\n        throw new CommandError(`template file does not exist: ${templatePath}`);\n      }\n\n      await extractLocalNpmTarballAsync(templatePath, { cwd: templateDirectory, name: appName });\n      return templateDirectory;\n    }\n\n    if (repoUrl.origin !== 'https://github.com') {\n      oraInstance.fail(\n        `Invalid URL: ${chalk.red(\n          `\"${template}\"`\n        )}. Only GitHub repositories are supported. Please use a GitHub URL and try again.`\n      );\n      throw new AbortCommandError();\n    }\n\n    repoInfo = await getRepoInfo(repoUrl, templatePath);\n\n    if (!repoInfo) {\n      oraInstance.fail(\n        `Found invalid GitHub URL: ${chalk.red(`\"${template}\"`)}. Please fix the URL and try again.`\n      );\n      throw new AbortCommandError();\n    }\n\n    const found = await hasRepo(repoInfo);\n\n    if (!found) {\n      oraInstance.fail(\n        `Could not locate the repository for ${chalk.red(\n          `\"${template}\"`\n        )}. Please check that the repository exists and try again.`\n      );\n      throw new AbortCommandError();\n    }\n  }\n\n  if (repoInfo) {\n    oraInstance.text = chalk.bold(\n      `Downloading files from repo ${chalk.cyan(template)}. This might take a moment.`\n    );\n\n    await downloadAndExtractRepoAsync(templateDirectory, repoInfo);\n  }\n\n  return true;\n}\n"], "names": ["cloneTemplateAsync", "resolveTemplateArgAsync", "Log", "debug", "require", "templateDirectory", "template", "exp", "ora", "name", "templatePackageName", "getTemplateNpmPackageName", "sdkVersion", "downloadAndExtractNpmModuleAsync", "cwd", "log", "semver", "major", "getRepoInfo", "url", "examplePath", "username", "t", "_branch", "file", "pathname", "split", "filePath", "replace", "join", "undefined", "infoResponse", "fetchAsync", "status", "info", "json", "branch", "RegExp", "hasRepo", "contentsUrl", "packagePath", "isUrlOk", "downloadAndExtractRepoAsync", "root", "projectName", "path", "basename", "strip", "length", "extractNpmTarballFromUrlAsync", "fileList", "oraInstance", "appName", "templatePath", "repoInfo", "repoUrl", "URL", "error", "code", "fail", "process", "platform", "isAbsolute", "toString", "resolve", "fs", "existsSync", "CommandError", "extractLocalNpmTarballAsync", "origin", "chalk", "red", "AbortCommandError", "found", "text", "bold", "cyan"], "mappings": "AAAA;;;;QA0BsBA,kBAAkB,GAAlBA,kBAAkB;QAmFlBC,uBAAuB,GAAvBA,uBAAuB;AA5G3B,IAAA,MAAO,kCAAP,OAAO,EAAA;AACV,IAAA,GAAI,kCAAJ,IAAI,EAAA;AAEF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACJ,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AAEA,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AACnCC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACiC,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AAK1D,IAAA,IAAc,WAAd,cAAc,CAAA;AACG,IAAA,IAAc,WAAd,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC,AAAsB,AAAC;AAS/E,eAAeJ,kBAAkB,CAAC,EACvCK,iBAAiB,CAAA,EACjBC,QAAQ,CAAA,EACRC,GAAG,CAAA,EACHC,GAAG,CAAA,EAMJ,EAAE;IACD,IAAIF,QAAQ,EAAE;QACZ,MAAML,uBAAuB,CAACI,iBAAiB,EAAEG,GAAG,EAAED,GAAG,CAACE,IAAI,EAAEH,QAAQ,CAAC,CAAC;KAC3E,MAAM;QACL,MAAMI,mBAAmB,GAAG,MAAMC,yBAAyB,CAACJ,GAAG,CAACK,UAAU,CAAC,AAAC;QAC5E,MAAMC,CAAAA,GAAAA,IAAgC,AAGpC,CAAA,iCAHoC,CAACH,mBAAmB,EAAE;YAC1DI,GAAG,EAAET,iBAAiB;YACtBI,IAAI,EAAEF,GAAG,CAACE,IAAI;SACf,CAAC,CAAC;KACJ;CACF;AAED,8HAA8H,CAC9H,SAASE,yBAAyB,CAACC,UAAmB,EAAU;IAC9D,4DAA4D;IAC5D,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,aAAa,EAAE;QAC/CV,GAAG,CAACa,GAAG,CAAC,0EAA0E,CAAC,CAAC;QACpF,OAAO,CAAC,iCAAiC,CAAC,CAAC;KAC5C;IACD,OAAO,CAAC,+BAA+B,EAAEC,OAAM,QAAA,CAACC,KAAK,CAACL,UAAU,CAAC,CAAC,CAAC,CAAC;CACrE;AAED,eAAeM,WAAW,CAACC,GAAQ,EAAEC,WAAoB,EAAiC;IACxF,MAAM,GAAGC,QAAQ,EAAEZ,IAAI,EAAEa,CAAC,EAAEC,OAAO,EAAE,GAAGC,IAAI,CAAC,GAAGL,GAAG,CAACM,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,AAAC;IACxE,MAAMC,QAAQ,GAAGP,WAAW,GAAGA,WAAW,CAACQ,OAAO,QAAQ,EAAE,CAAC,GAAGJ,IAAI,CAACK,IAAI,CAAC,GAAG,CAAC,AAAC;IAE/E,+DAA+D;IAC/D,2DAA2D;IAC3D,IAAIP,CAAC,KAAKQ,SAAS,EAAE;QACnB,MAAMC,YAAY,GAAG,MAAMC,CAAAA,GAAAA,OAAU,AAAoD,CAAA,WAApD,CAAC,CAAC,6BAA6B,EAAEX,QAAQ,CAAC,CAAC,EAAEZ,IAAI,CAAC,CAAC,CAAC,AAAC;QAC1F,IAAIsB,YAAY,CAACE,MAAM,KAAK,GAAG,EAAE;YAC/B,OAAO;SACR;QACD,MAAMC,IAAI,GAAG,MAAMH,YAAY,CAACI,IAAI,EAAE,AAAC;QACvC,OAAO;YAAEd,QAAQ;YAAEZ,IAAI;YAAE2B,MAAM,EAAEF,IAAI,CAAC,gBAAgB,CAAC;YAAEP,QAAQ;SAAE,CAAC;KACrE;IAED,qEAAqE;IACrE,MAAMS,MAAM,GAAGhB,WAAW,GACtB,CAAC,EAAEG,OAAO,CAAC,CAAC,EAAEC,IAAI,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACD,OAAO,CAAC,IAAIS,MAAM,CAAC,CAAC,CAAC,EAAEV,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GACzEJ,OAAO,AAAC;IAEZ,IAAIF,QAAQ,IAAIZ,IAAI,IAAI2B,MAAM,IAAId,CAAC,KAAK,MAAM,EAAE;QAC9C,OAAO;YAAED,QAAQ;YAAEZ,IAAI;YAAE2B,MAAM;YAAET,QAAQ;SAAE,CAAC;KAC7C;IACD,OAAOG,SAAS,CAAC;CAClB;AAED,SAASQ,OAAO,CAAC,EAAEjB,QAAQ,CAAA,EAAEZ,IAAI,CAAA,EAAE2B,MAAM,CAAA,EAAET,QAAQ,CAAA,EAAY,EAAE;IAC/D,MAAMY,WAAW,GAAG,CAAC,6BAA6B,EAAElB,QAAQ,CAAC,CAAC,EAAEZ,IAAI,CAAC,SAAS,CAAC,AAAC;IAChF,MAAM+B,WAAW,GAAG,CAAC,EAAEb,QAAQ,GAAG,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,AAAC;IAErE,OAAOc,CAAAA,GAAAA,IAAO,AAA8C,CAAA,QAA9C,CAACF,WAAW,GAAGC,WAAW,GAAG,CAAC,KAAK,EAAEJ,MAAM,CAAC,CAAC,CAAC,CAAC;CAC9D;AAED,eAAeM,2BAA2B,CACxCC,IAAY,EACZ,EAAEtB,QAAQ,CAAA,EAAEZ,IAAI,CAAA,EAAE2B,MAAM,CAAA,EAAET,QAAQ,CAAA,EAAY,EAC/B;IACf,MAAMiB,WAAW,GAAGC,KAAI,QAAA,CAACC,QAAQ,CAACH,IAAI,CAAC,AAAC;IAExC,MAAMI,KAAK,GAAGpB,QAAQ,GAAGA,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAACsB,MAAM,GAAG,CAAC,GAAG,CAAC,AAAC;IAE5D,MAAM7B,GAAG,GAAG,CAAC,4BAA4B,EAAEE,QAAQ,CAAC,CAAC,EAAEZ,IAAI,CAAC,QAAQ,EAAE2B,MAAM,CAAC,CAAC,AAAC;IAC/EjC,KAAK,CAAC,2BAA2B,EAAEgB,GAAG,CAAC,CAAC;IACxC,MAAM8B,CAAAA,GAAAA,IAA6B,AAKjC,CAAA,8BALiC,CAAC9B,GAAG,EAAE;QACvCL,GAAG,EAAE6B,IAAI;QACTlC,IAAI,EAAEmC,WAAW;QACjBG,KAAK;QACLG,QAAQ,EAAE;YAAC,CAAC,EAAEzC,IAAI,CAAC,CAAC,EAAE2B,MAAM,CAAC,EAAET,QAAQ,GAAG,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;SAAC;KACjE,CAAC,CAAC;CACJ;AAEM,eAAe1B,uBAAuB,CAC3CI,iBAAyB,EACzB8C,WAAgB,EAChBC,OAAe,EACf9C,QAAgB,EAChB+C,YAAqB,EACrB;IACA,IAAIC,QAAQ,AAAsB,AAAC;IAEnC,IAAIhD,QAAQ,EAAE;QACZ,aAAa;QACb,IAAIiD,OAAO,AAAiB,AAAC;QAE7B,IAAI;YACF,aAAa;YACbA,OAAO,GAAG,IAAIC,GAAG,CAAClD,QAAQ,CAAC,CAAC;SAC7B,CAAC,OAAOmD,KAAK,EAAO;YACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,iBAAiB,EAAE;gBACpCP,WAAW,CAACQ,IAAI,CAACF,KAAK,CAAC,CAAC;gBACxB,MAAMA,KAAK,CAAC;aACb;SACF;QAED,6DAA6D;QAC7D,kFAAkF;QAClF,IAAIG,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIN,OAAO,IAAIV,KAAI,QAAA,CAACiB,UAAU,CAACP,OAAO,CAACQ,QAAQ,EAAE,CAAC,EAAE;YAClFR,OAAO,GAAGzB,SAAS,CAAC;SACrB;QAED,IAAI,CAACyB,OAAO,EAAE;YACZ,MAAMF,YAAY,GAAGR,KAAI,QAAA,CAACmB,OAAO,CAAC1D,QAAQ,CAAC,AAAC;YAC5C,IAAI,CAAC2D,GAAE,QAAA,CAACC,UAAU,CAACb,YAAY,CAAC,EAAE;gBAChC,MAAM,IAAIc,OAAY,aAAA,CAAC,CAAC,8BAA8B,EAAEd,YAAY,CAAC,CAAC,CAAC,CAAC;aACzE;YAED,MAAMe,CAAAA,GAAAA,IAA2B,AAAyD,CAAA,4BAAzD,CAACf,YAAY,EAAE;gBAAEvC,GAAG,EAAET,iBAAiB;gBAAEI,IAAI,EAAE2C,OAAO;aAAE,CAAC,CAAC;YAC3F,OAAO/C,iBAAiB,CAAC;SAC1B;QAED,IAAIkD,OAAO,CAACc,MAAM,KAAK,oBAAoB,EAAE;YAC3ClB,WAAW,CAACQ,IAAI,CACd,CAAC,aAAa,EAAEW,MAAK,QAAA,CAACC,GAAG,CACvB,CAAC,CAAC,EAAEjE,QAAQ,CAAC,CAAC,CAAC,CAChB,CAAC,gFAAgF,CAAC,CACpF,CAAC;YACF,MAAM,IAAIkE,OAAiB,kBAAA,EAAE,CAAC;SAC/B;QAEDlB,QAAQ,GAAG,MAAMpC,WAAW,CAACqC,OAAO,EAAEF,YAAY,CAAC,CAAC;QAEpD,IAAI,CAACC,QAAQ,EAAE;YACbH,WAAW,CAACQ,IAAI,CACd,CAAC,0BAA0B,EAAEW,MAAK,QAAA,CAACC,GAAG,CAAC,CAAC,CAAC,EAAEjE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC,CAAC,CAC7F,CAAC;YACF,MAAM,IAAIkE,OAAiB,kBAAA,EAAE,CAAC;SAC/B;QAED,MAAMC,KAAK,GAAG,MAAMnC,OAAO,CAACgB,QAAQ,CAAC,AAAC;QAEtC,IAAI,CAACmB,KAAK,EAAE;YACVtB,WAAW,CAACQ,IAAI,CACd,CAAC,oCAAoC,EAAEW,MAAK,QAAA,CAACC,GAAG,CAC9C,CAAC,CAAC,EAAEjE,QAAQ,CAAC,CAAC,CAAC,CAChB,CAAC,wDAAwD,CAAC,CAC5D,CAAC;YACF,MAAM,IAAIkE,OAAiB,kBAAA,EAAE,CAAC;SAC/B;KACF;IAED,IAAIlB,QAAQ,EAAE;QACZH,WAAW,CAACuB,IAAI,GAAGJ,MAAK,QAAA,CAACK,IAAI,CAC3B,CAAC,4BAA4B,EAAEL,MAAK,QAAA,CAACM,IAAI,CAACtE,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CACjF,CAAC;QAEF,MAAMoC,2BAA2B,CAACrC,iBAAiB,EAAEiD,QAAQ,CAAC,CAAC;KAChE;IAED,OAAO,IAAI,CAAC;CACb"}