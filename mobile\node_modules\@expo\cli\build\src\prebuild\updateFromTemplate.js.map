{"version": 3, "sources": ["../../../src/prebuild/updateFromTemplate.ts"], "sourcesContent": ["import { ExpoConfig, PackageJSONConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\n\nimport * as Log from '../log';\nimport { AbortCommandError, SilentError } from '../utils/errors';\nimport { logNewSection } from '../utils/ora';\nimport { profile } from '../utils/profile';\nimport { copyTemplateFilesAsync, createCopyFilesSuccessMessage } from './copyTemplateFiles';\nimport { cloneTemplateAsync } from './resolveTemplate';\nimport { DependenciesModificationResults, updatePackageJSONAsync } from './updatePackageJson';\nimport { validateTemplatePlatforms } from './validateTemplatePlatforms';\n\n/**\n * Creates local native files from an input template file path.\n *\n * @return `true` if the project is ejecting, and `false` if it's syncing.\n */\nexport async function updateFromTemplateAsync(\n  projectRoot: string,\n  {\n    exp,\n    pkg,\n    template,\n    templateDirectory,\n    platforms,\n    skipDependencyUpdate,\n  }: {\n    /** Expo Config */\n    exp: ExpoConfig;\n    /** package.json as JSON */\n    pkg: PackageJSONConfig;\n    /** Template reference ID. */\n    template?: string;\n    /** Directory to write the template to before copying into the project. */\n    templateDirectory?: string;\n    /** List of platforms to clone. */\n    platforms: ModPlatform[];\n    /** List of dependencies to skip updating. */\n    skipDependencyUpdate?: string[];\n  }\n): Promise<\n  {\n    /** Indicates if new files were created in the project. */\n    hasNewProjectFiles: boolean;\n    /** Indicates that the project needs to run `pod install` */\n    needsPodInstall: boolean;\n  } & DependenciesModificationResults\n> {\n  if (!templateDirectory) {\n    const temporary = await import('tempy');\n    templateDirectory = temporary.directory();\n  }\n\n  const copiedPaths = await profile(cloneTemplateAndCopyToProjectAsync)({\n    projectRoot,\n    template,\n    templateDirectory,\n    exp,\n    platforms,\n  });\n\n  const depsResults = await profile(updatePackageJSONAsync)(projectRoot, {\n    templateDirectory,\n    pkg,\n    skipDependencyUpdate,\n  });\n\n  return {\n    hasNewProjectFiles: !!copiedPaths.length,\n    // If the iOS folder changes or new packages are added, we should rerun pod install.\n    needsPodInstall:\n      copiedPaths.includes('ios') ||\n      depsResults.hasNewDependencies ||\n      depsResults.hasNewDevDependencies,\n    ...depsResults,\n  };\n}\n\n/**\n * Extract the template and copy the ios and android directories over to the project directory.\n *\n * @return `true` if any project files were created.\n */\nasync function cloneTemplateAndCopyToProjectAsync({\n  projectRoot,\n  templateDirectory,\n  template,\n  exp,\n  platforms: unknownPlatforms,\n}: {\n  projectRoot: string;\n  templateDirectory: string;\n  template?: string;\n  exp: Pick<ExpoConfig, 'name' | 'sdkVersion'>;\n  platforms: ModPlatform[];\n}): Promise<string[]> {\n  const ora = logNewSection(\n    'Creating native project directories (./ios and ./android) and updating .gitignore'\n  );\n\n  try {\n    await cloneTemplateAsync({ templateDirectory, template, exp, ora });\n\n    const platforms = await validateTemplatePlatforms({\n      templateDirectory,\n      platforms: unknownPlatforms,\n    });\n\n    const results = await copyTemplateFilesAsync(projectRoot, {\n      templateDirectory,\n      platforms,\n    });\n\n    ora.succeed(createCopyFilesSuccessMessage(platforms, results));\n\n    return results.copiedPaths;\n  } catch (e: any) {\n    if (!(e instanceof AbortCommandError)) {\n      Log.error(e.message);\n    }\n    ora.fail('Failed to create the native project.');\n    Log.log(\n      chalk.yellow(\n        'You may want to delete the `./ios` and/or `./android` directories before trying again.'\n      )\n    );\n    throw new SilentError(e);\n  }\n}\n"], "names": ["updateFromTemplateAsync", "Log", "projectRoot", "exp", "pkg", "template", "templateDirectory", "platforms", "skipDependencyUpdate", "temporary", "directory", "copiedPaths", "profile", "cloneTemplateAndCopyToProjectAsync", "depsResults", "updatePackageJSONAsync", "hasNewProjectFiles", "length", "needsPodInstall", "includes", "hasNewDependencies", "hasNewDevDependencies", "unknownPlatforms", "ora", "logNewSection", "cloneTemplateAsync", "validateTemplatePlatforms", "results", "copyTemplateFilesAsync", "succeed", "createCopyFilesSuccessMessage", "e", "AbortCommandError", "error", "message", "fail", "log", "chalk", "yellow", "SilentError"], "mappings": "AAAA;;;;QAkBsBA,uBAAuB,GAAvBA,uBAAuB;AAhB3B,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEbC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACgC,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AAClC,IAAA,IAAc,WAAd,cAAc,CAAA;AACpB,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AAC4B,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AACxD,IAAA,gBAAmB,WAAnB,mBAAmB,CAAA;AACkB,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AACnD,IAAA,0BAA6B,WAA7B,6BAA6B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhE,eAAeD,uBAAuB,CAC3CE,WAAmB,EACnB,EACEC,GAAG,CAAA,EACHC,GAAG,CAAA,EACHC,QAAQ,CAAA,EACRC,iBAAiB,CAAA,EACjBC,SAAS,CAAA,EACTC,oBAAoB,CAAA,EAcrB,EAQD;IACA,IAAI,CAACF,iBAAiB,EAAE;QACtB,MAAMG,SAAS,GAAG,MAAM;mDAAO,OAAO;UAAC,AAAC;QACxCH,iBAAiB,GAAGG,SAAS,CAACC,SAAS,EAAE,CAAC;KAC3C;IAED,MAAMC,WAAW,GAAG,MAAMC,CAAAA,GAAAA,QAAO,AAAoC,CAAA,QAApC,CAACC,kCAAkC,CAAC,CAAC;QACpEX,WAAW;QACXG,QAAQ;QACRC,iBAAiB;QACjBH,GAAG;QACHI,SAAS;KACV,CAAC,AAAC;IAEH,MAAMO,WAAW,GAAG,MAAMF,CAAAA,GAAAA,QAAO,AAAwB,CAAA,QAAxB,CAACG,kBAAsB,uBAAA,CAAC,CAACb,WAAW,EAAE;QACrEI,iBAAiB;QACjBF,GAAG;QACHI,oBAAoB;KACrB,CAAC,AAAC;IAEH,OAAO;QACLQ,kBAAkB,EAAE,CAAC,CAACL,WAAW,CAACM,MAAM;QACxC,oFAAoF;QACpFC,eAAe,EACbP,WAAW,CAACQ,QAAQ,CAAC,KAAK,CAAC,IAC3BL,WAAW,CAACM,kBAAkB,IAC9BN,WAAW,CAACO,qBAAqB;QACnC,GAAGP,WAAW;KACf,CAAC;CACH;AAED;;;;GAIG,CACH,eAAeD,kCAAkC,CAAC,EAChDX,WAAW,CAAA,EACXI,iBAAiB,CAAA,EACjBD,QAAQ,CAAA,EACRF,GAAG,CAAA,EACHI,SAAS,EAAEe,gBAAgB,CAAA,EAO5B,EAAqB;IACpB,MAAMC,GAAG,GAAGC,CAAAA,GAAAA,IAAa,AAExB,CAAA,cAFwB,CACvB,mFAAmF,CACpF,AAAC;IAEF,IAAI;QACF,MAAMC,CAAAA,GAAAA,gBAAkB,AAA2C,CAAA,mBAA3C,CAAC;YAAEnB,iBAAiB;YAAED,QAAQ;YAAEF,GAAG;YAAEoB,GAAG;SAAE,CAAC,CAAC;QAEpE,MAAMhB,SAAS,GAAG,MAAMmB,CAAAA,GAAAA,0BAAyB,AAG/C,CAAA,0BAH+C,CAAC;YAChDpB,iBAAiB;YACjBC,SAAS,EAAEe,gBAAgB;SAC5B,CAAC,AAAC;QAEH,MAAMK,OAAO,GAAG,MAAMC,CAAAA,GAAAA,kBAAsB,AAG1C,CAAA,uBAH0C,CAAC1B,WAAW,EAAE;YACxDI,iBAAiB;YACjBC,SAAS;SACV,CAAC,AAAC;QAEHgB,GAAG,CAACM,OAAO,CAACC,CAAAA,GAAAA,kBAA6B,AAAoB,CAAA,8BAApB,CAACvB,SAAS,EAAEoB,OAAO,CAAC,CAAC,CAAC;QAE/D,OAAOA,OAAO,CAAChB,WAAW,CAAC;KAC5B,CAAC,OAAOoB,CAAC,EAAO;QACf,IAAI,CAAC,CAACA,CAAC,YAAYC,OAAiB,kBAAA,CAAC,EAAE;YACrC/B,GAAG,CAACgC,KAAK,CAACF,CAAC,CAACG,OAAO,CAAC,CAAC;SACtB;QACDX,GAAG,CAACY,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACjDlC,GAAG,CAACmC,GAAG,CACLC,MAAK,QAAA,CAACC,MAAM,CACV,wFAAwF,CACzF,CACF,CAAC;QACF,MAAM,IAAIC,OAAW,YAAA,CAACR,CAAC,CAAC,CAAC;KAC1B;CACF"}