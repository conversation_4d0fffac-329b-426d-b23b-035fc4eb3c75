{"version": 3, "sources": ["../../../src/prebuild/updatePackageJson.ts"], "sourcesContent": ["import { getPackageJson, PackageJSONConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport crypto from 'crypto';\nimport fs from 'fs';\nimport path from 'path';\nimport { intersects as semverIntersects, Range as SemverRange } from 'semver';\n\nimport * as Log from '../log';\nimport { isModuleSymlinked } from '../utils/isModuleSymlinked';\nimport { logNewSection } from '../utils/ora';\n\nexport type DependenciesMap = { [key: string]: string | number };\n\nexport type DependenciesModificationResults = {\n  /** Indicates that new values were added to the `dependencies` object in the `package.json`. */\n  hasNewDependencies: boolean;\n  /** Indicates that new values were added to the `devDependencies` object in the `package.json`. */\n  hasNewDevDependencies: boolean;\n};\n\n/** Modifies the `package.json` with `modifyPackageJson` and format/displays the results. */\nexport async function updatePackageJSONAsync(\n  projectRoot: string,\n  {\n    templateDirectory,\n    pkg,\n    skipDependencyUpdate,\n  }: {\n    templateDirectory: string;\n    pkg: PackageJSONConfig;\n    skipDependencyUpdate?: string[];\n  }\n): Promise<DependenciesModificationResults> {\n  const updatingPackageJsonStep = logNewSection(\n    'Updating your package.json scripts, dependencies, and main file'\n  );\n\n  const templatePkg = getPackageJson(templateDirectory);\n\n  const results = modifyPackageJson(projectRoot, {\n    templatePkg,\n    pkg,\n    skipDependencyUpdate,\n  });\n\n  await fs.promises.writeFile(\n    path.resolve(projectRoot, 'package.json'),\n    // Add new line to match the format of running yarn.\n    // This prevents the `package.json` from changing when running `prebuild --no-install` multiple times.\n    JSON.stringify(pkg, null, 2) + '\\n'\n  );\n\n  updatingPackageJsonStep.succeed(\n    'Updated package.json and added index.js entry point for iOS and Android'\n  );\n\n  return results;\n}\n\n/**\n * Make required modifications to the `package.json` file as a JSON object.\n *\n * 1. Update `package.json` `scripts`.\n * 2. Update `package.json` `dependencies` and `devDependencies`.\n * 3. Update `package.json` `main`.\n *\n * @param projectRoot The root directory of the project.\n * @param props.templatePkg Template project package.json as JSON.\n * @param props.pkg Current package.json as JSON.\n * @param props.skipDependencyUpdate Array of dependencies to skip updating.\n * @returns\n */\nfunction modifyPackageJson(\n  projectRoot: string,\n  {\n    templatePkg,\n    pkg,\n    skipDependencyUpdate,\n  }: {\n    templatePkg: PackageJSONConfig;\n    pkg: PackageJSONConfig;\n    skipDependencyUpdate?: string[];\n  }\n) {\n  updatePkgScripts({ pkg });\n\n  // TODO: Move to `npx expo-doctor`\n  return updatePkgDependencies(projectRoot, {\n    pkg,\n    templatePkg,\n    skipDependencyUpdate,\n  });\n}\n\n/**\n * Update package.json dependencies by combining the dependencies in the project we are ejecting\n * with the dependencies in the template project. Does the same for devDependencies.\n *\n * - The template may have some dependencies beyond react/react-native/react-native-unimodules,\n *   for example RNGH and Reanimated. We should prefer the version that is already being used\n *   in the project for those, but swap the react/react-native/react-native-unimodules versions\n *   with the ones in the template.\n * - The same applies to expo-updates -- since some native project configuration may depend on the\n *   version, we should always use the version of expo-updates in the template.\n *\n * > Exposed for testing.\n */\nexport function updatePkgDependencies(\n  projectRoot: string,\n  {\n    pkg,\n    templatePkg,\n    skipDependencyUpdate = [],\n  }: {\n    pkg: PackageJSONConfig;\n    templatePkg: PackageJSONConfig;\n    /** @deprecated Required packages are not overwritten, only added when missing */\n    skipDependencyUpdate?: string[];\n  }\n): DependenciesModificationResults {\n  if (!pkg.devDependencies) {\n    pkg.devDependencies = {};\n  }\n  const { dependencies, devDependencies } = templatePkg;\n  const defaultDependencies = createDependenciesMap(dependencies);\n  const defaultDevDependencies = createDependenciesMap(devDependencies);\n\n  const combinedDependencies: DependenciesMap = createDependenciesMap({\n    ...defaultDependencies,\n    ...pkg.dependencies,\n  });\n\n  // These dependencies are only added, not overwritten from the project\n  const requiredDependencies = ['expo', 'expo-splash-screen', 'react', 'react-native'].filter(\n    (depKey) => !!defaultDependencies[depKey]\n  );\n\n  const symlinkedPackages: string[] = [];\n  const nonRecommendedPackages: string[] = [];\n\n  for (const dependenciesKey of requiredDependencies) {\n    // If the local package.json defined the dependency that we want to overwrite...\n    if (pkg.dependencies?.[dependenciesKey]) {\n      // Then ensure it isn't symlinked (i.e. the user has a custom version in their yarn workspace).\n      if (isModuleSymlinked(projectRoot, { moduleId: dependenciesKey, isSilent: true })) {\n        // If the package is in the project's package.json and it's symlinked, then skip overwriting it.\n        symlinkedPackages.push(dependenciesKey);\n        continue;\n      }\n\n      // Do not modify manually skipped dependencies\n      if (skipDependencyUpdate.includes(dependenciesKey)) {\n        continue;\n      }\n\n      // Warn users for outdated dependencies when prebuilding\n      const hasRecommendedVersion = versionRangesIntersect(\n        pkg.dependencies[dependenciesKey],\n        String(defaultDependencies[dependenciesKey])\n      );\n      if (!hasRecommendedVersion) {\n        nonRecommendedPackages.push(`${dependenciesKey}@${defaultDependencies[dependenciesKey]}`);\n      }\n    }\n  }\n\n  if (symlinkedPackages.length) {\n    Log.log(\n      `\\u203A Using symlinked ${symlinkedPackages\n        .map((pkg) => chalk.bold(pkg))\n        .join(', ')} instead of recommended version(s).`\n    );\n  }\n\n  if (nonRecommendedPackages.length) {\n    Log.warn(\n      `\\u203A Using current versions instead of recommended ${nonRecommendedPackages\n        .map((pkg) => chalk.bold(pkg))\n        .join(', ')}.`\n    );\n  }\n\n  const combinedDevDependencies: DependenciesMap = createDependenciesMap({\n    ...defaultDevDependencies,\n    ...pkg.devDependencies,\n  });\n\n  // Only change the dependencies if the normalized hash changes, this helps to reduce meaningless changes.\n  const hasNewDependencies =\n    hashForDependencyMap(pkg.dependencies) !== hashForDependencyMap(combinedDependencies);\n  const hasNewDevDependencies =\n    hashForDependencyMap(pkg.devDependencies) !== hashForDependencyMap(combinedDevDependencies);\n  // Save the dependencies\n  if (hasNewDependencies) {\n    // Use Object.assign to preserve the original order of dependencies, this makes it easier to see what changed in the git diff.\n    pkg.dependencies = Object.assign(pkg.dependencies ?? {}, combinedDependencies);\n  }\n  if (hasNewDevDependencies) {\n    // Same as with dependencies\n    pkg.devDependencies = Object.assign(pkg.devDependencies ?? {}, combinedDevDependencies);\n  }\n\n  return {\n    hasNewDependencies,\n    hasNewDevDependencies,\n  };\n}\n\n/**\n * Create an object of type DependenciesMap a dependencies object or throw if not valid.\n *\n * @param dependencies - ideally an object of type {[key]: string} - if not then this will error.\n */\nexport function createDependenciesMap(dependencies: any): DependenciesMap {\n  if (typeof dependencies !== 'object') {\n    throw new Error(`Dependency map is invalid, expected object but got ${typeof dependencies}`);\n  } else if (!dependencies) {\n    return {};\n  }\n\n  const outputMap: DependenciesMap = {};\n\n  for (const key of Object.keys(dependencies)) {\n    const value = dependencies[key];\n    if (typeof value === 'string') {\n      outputMap[key] = value;\n    } else {\n      throw new Error(\n        `Dependency for key \\`${key}\\` should be a \\`string\\`, instead got: \\`{ ${key}: ${JSON.stringify(\n          value\n        )} }\\``\n      );\n    }\n  }\n  return outputMap;\n}\n\n/**\n * Update package.json scripts - `npm start` should default to `expo\n * start --dev-client` rather than `expo start` after ejecting, for example.\n */\nfunction updatePkgScripts({ pkg }: { pkg: PackageJSONConfig }) {\n  if (!pkg.scripts) {\n    pkg.scripts = {};\n  }\n  if (!pkg.scripts.android?.includes('run')) {\n    pkg.scripts.android = 'expo run:android';\n  }\n  if (!pkg.scripts.ios?.includes('run')) {\n    pkg.scripts.ios = 'expo run:ios';\n  }\n}\n\nfunction normalizeDependencyMap(deps: DependenciesMap): string[] {\n  return Object.keys(deps)\n    .map((dependency) => `${dependency}@${deps[dependency]}`)\n    .sort();\n}\n\nexport function hashForDependencyMap(deps: DependenciesMap = {}): string {\n  const depsList = normalizeDependencyMap(deps);\n  const depsString = depsList.join('\\n');\n  return createFileHash(depsString);\n}\n\nexport function createFileHash(contents: string): string {\n  // this doesn't need to be secure, the shorter the better.\n  return crypto.createHash('sha1').update(contents).digest('hex');\n}\n\n/**\n * Determine if two semver ranges are overlapping or intersecting.\n * This is a safe version of `semver.intersects` that does not throw.\n */\nfunction versionRangesIntersect(rangeA: string | SemverRange, rangeB: string | SemverRange) {\n  try {\n    return semverIntersects(rangeA, rangeB);\n  } catch {\n    return false;\n  }\n}\n"], "names": ["updatePackageJSONAsync", "updatePkgDependencies", "createDependenciesMap", "hashForDependencyMap", "createFileHash", "Log", "projectRoot", "templateDirectory", "pkg", "skipDependencyUpdate", "updatingPackageJsonStep", "logNewSection", "templatePkg", "getPackageJson", "results", "modifyPackageJson", "fs", "promises", "writeFile", "path", "resolve", "JSON", "stringify", "succeed", "updatePkgScripts", "devDependencies", "dependencies", "defaultDependencies", "defaultDevDependencies", "combinedDependencies", "requiredDependencies", "filter", "<PERSON><PERSON><PERSON><PERSON>", "symlinkedPackages", "nonRecommendedPackages", "dependenciesKey", "isModuleSymlinked", "moduleId", "isSilent", "push", "includes", "hasRecommendedVersion", "versionRangesIntersect", "String", "length", "log", "map", "chalk", "bold", "join", "warn", "combinedDevDependencies", "hasNewDependencies", "hasNewDevDependencies", "Object", "assign", "Error", "outputMap", "key", "keys", "value", "scripts", "android", "ios", "normalizeDependencyMap", "deps", "dependency", "sort", "depsList", "depsString", "contents", "crypto", "createHash", "update", "digest", "rangeA", "rangeB", "semverIntersects"], "mappings": "AAAA;;;;QAqBsBA,sBAAsB,GAAtBA,sBAAsB;QAsF5BC,qBAAqB,GAArBA,qBAAqB;QA0GrBC,qBAAqB,GAArBA,qBAAqB;QA8CrBC,oBAAoB,GAApBA,oBAAoB;QAMpBC,cAAc,GAAdA,cAAc;AAzQoB,IAAA,OAAc,WAAd,cAAc,CAAA;AAC9C,IAAA,MAAO,kCAAP,OAAO,EAAA;AACN,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACZ,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AAC8C,IAAA,OAAQ,WAAR,QAAQ,CAAA;AAEjEC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACmB,IAAA,kBAA4B,WAA5B,4BAA4B,CAAA;AAChC,IAAA,IAAc,WAAd,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrC,eAAeL,sBAAsB,CAC1CM,WAAmB,EACnB,EACEC,iBAAiB,CAAA,EACjBC,GAAG,CAAA,EACHC,oBAAoB,CAAA,EAKrB,EACyC;IAC1C,MAAMC,uBAAuB,GAAGC,CAAAA,GAAAA,IAAa,AAE5C,CAAA,cAF4C,CAC3C,iEAAiE,CAClE,AAAC;IAEF,MAAMC,WAAW,GAAGC,CAAAA,GAAAA,OAAc,AAAmB,CAAA,eAAnB,CAACN,iBAAiB,CAAC,AAAC;IAEtD,MAAMO,OAAO,GAAGC,iBAAiB,CAACT,WAAW,EAAE;QAC7CM,WAAW;QACXJ,GAAG;QACHC,oBAAoB;KACrB,CAAC,AAAC;IAEH,MAAMO,GAAE,QAAA,CAACC,QAAQ,CAACC,SAAS,CACzBC,KAAI,QAAA,CAACC,OAAO,CAACd,WAAW,EAAE,cAAc,CAAC,EACzC,oDAAoD;IACpD,sGAAsG;IACtGe,IAAI,CAACC,SAAS,CAACd,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CACpC,CAAC;IAEFE,uBAAuB,CAACa,OAAO,CAC7B,yEAAyE,CAC1E,CAAC;IAEF,OAAOT,OAAO,CAAC;CAChB;AAED;;;;;;;;;;;;GAYG,CACH,SAASC,iBAAiB,CACxBT,WAAmB,EACnB,EACEM,WAAW,CAAA,EACXJ,GAAG,CAAA,EACHC,oBAAoB,CAAA,EAKrB,EACD;IACAe,gBAAgB,CAAC;QAAEhB,GAAG;KAAE,CAAC,CAAC;IAE1B,kCAAkC;IAClC,OAAOP,qBAAqB,CAACK,WAAW,EAAE;QACxCE,GAAG;QACHI,WAAW;QACXH,oBAAoB;KACrB,CAAC,CAAC;CACJ;AAeM,SAASR,qBAAqB,CACnCK,WAAmB,EACnB,EACEE,GAAG,EAAHA,IAAG,CAAA,EACHI,WAAW,CAAA,EACXH,oBAAoB,EAAG,EAAE,CAAA,EAM1B,EACgC;IACjC,IAAI,CAACD,IAAG,CAACiB,eAAe,EAAE;QACxBjB,IAAG,CAACiB,eAAe,GAAG,EAAE,CAAC;KAC1B;IACD,MAAM,EAAEC,YAAY,CAAA,EAAED,eAAe,CAAA,EAAE,GAAGb,WAAW,AAAC;IACtD,MAAMe,mBAAmB,GAAGzB,qBAAqB,CAACwB,YAAY,CAAC,AAAC;IAChE,MAAME,sBAAsB,GAAG1B,qBAAqB,CAACuB,eAAe,CAAC,AAAC;IAEtE,MAAMI,oBAAoB,GAAoB3B,qBAAqB,CAAC;QAClE,GAAGyB,mBAAmB;QACtB,GAAGnB,IAAG,CAACkB,YAAY;KACpB,CAAC,AAAC;IAEH,sEAAsE;IACtE,MAAMI,oBAAoB,GAAG;QAAC,MAAM;QAAE,oBAAoB;QAAE,OAAO;QAAE,cAAc;KAAC,CAACC,MAAM,CACzF,CAACC,MAAM,GAAK,CAAC,CAACL,mBAAmB,CAACK,MAAM,CAAC;IAAA,CAC1C,AAAC;IAEF,MAAMC,iBAAiB,GAAa,EAAE,AAAC;IACvC,MAAMC,sBAAsB,GAAa,EAAE,AAAC;IAE5C,KAAK,MAAMC,eAAe,IAAIL,oBAAoB,CAAE;YAE9CtB,GAAgB;QADpB,gFAAgF;QAChF,IAAIA,CAAAA,GAAgB,GAAhBA,IAAG,CAACkB,YAAY,SAAmB,GAAnClB,KAAAA,CAAmC,GAAnCA,GAAgB,AAAE,CAAC2B,eAAe,CAAC,EAAE;YACvC,+FAA+F;YAC/F,IAAIC,CAAAA,GAAAA,kBAAiB,AAA4D,CAAA,kBAA5D,CAAC9B,WAAW,EAAE;gBAAE+B,QAAQ,EAAEF,eAAe;gBAAEG,QAAQ,EAAE,IAAI;aAAE,CAAC,EAAE;gBACjF,gGAAgG;gBAChGL,iBAAiB,CAACM,IAAI,CAACJ,eAAe,CAAC,CAAC;gBACxC,SAAS;aACV;YAED,8CAA8C;YAC9C,IAAI1B,oBAAoB,CAAC+B,QAAQ,CAACL,eAAe,CAAC,EAAE;gBAClD,SAAS;aACV;YAED,wDAAwD;YACxD,MAAMM,qBAAqB,GAAGC,sBAAsB,CAClDlC,IAAG,CAACkB,YAAY,CAACS,eAAe,CAAC,EACjCQ,MAAM,CAAChB,mBAAmB,CAACQ,eAAe,CAAC,CAAC,CAC7C,AAAC;YACF,IAAI,CAACM,qBAAqB,EAAE;gBAC1BP,sBAAsB,CAACK,IAAI,CAAC,CAAC,EAAEJ,eAAe,CAAC,CAAC,EAAER,mBAAmB,CAACQ,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3F;SACF;KACF;IAED,IAAIF,iBAAiB,CAACW,MAAM,EAAE;QAC5BvC,GAAG,CAACwC,GAAG,CACL,CAAC,uBAAuB,EAAEZ,iBAAiB,CACxCa,GAAG,CAAC,CAACtC,GAAG,GAAKuC,MAAK,QAAA,CAACC,IAAI,CAACxC,GAAG,CAAC;QAAA,CAAC,CAC7ByC,IAAI,CAAC,IAAI,CAAC,CAAC,mCAAmC,CAAC,CACnD,CAAC;KACH;IAED,IAAIf,sBAAsB,CAACU,MAAM,EAAE;QACjCvC,GAAG,CAAC6C,IAAI,CACN,CAAC,qDAAqD,EAAEhB,sBAAsB,CAC3EY,GAAG,CAAC,CAACtC,GAAG,GAAKuC,MAAK,QAAA,CAACC,IAAI,CAACxC,GAAG,CAAC;QAAA,CAAC,CAC7ByC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CACjB,CAAC;KACH;IAED,MAAME,uBAAuB,GAAoBjD,qBAAqB,CAAC;QACrE,GAAG0B,sBAAsB;QACzB,GAAGpB,IAAG,CAACiB,eAAe;KACvB,CAAC,AAAC;IAEH,yGAAyG;IACzG,MAAM2B,kBAAkB,GACtBjD,oBAAoB,CAACK,IAAG,CAACkB,YAAY,CAAC,KAAKvB,oBAAoB,CAAC0B,oBAAoB,CAAC,AAAC;IACxF,MAAMwB,qBAAqB,GACzBlD,oBAAoB,CAACK,IAAG,CAACiB,eAAe,CAAC,KAAKtB,oBAAoB,CAACgD,uBAAuB,CAAC,AAAC;IAC9F,wBAAwB;IACxB,IAAIC,kBAAkB,EAAE;YAEW5C,aAAgB;QADjD,8HAA8H;QAC9HA,IAAG,CAACkB,YAAY,GAAG4B,MAAM,CAACC,MAAM,CAAC/C,CAAAA,aAAgB,GAAhBA,IAAG,CAACkB,YAAY,YAAhBlB,aAAgB,GAAI,EAAE,EAAEqB,oBAAoB,CAAC,CAAC;KAChF;IACD,IAAIwB,qBAAqB,EAAE;YAEW7C,gBAAmB;QADvD,4BAA4B;QAC5BA,IAAG,CAACiB,eAAe,GAAG6B,MAAM,CAACC,MAAM,CAAC/C,CAAAA,gBAAmB,GAAnBA,IAAG,CAACiB,eAAe,YAAnBjB,gBAAmB,GAAI,EAAE,EAAE2C,uBAAuB,CAAC,CAAC;KACzF;IAED,OAAO;QACLC,kBAAkB;QAClBC,qBAAqB;KACtB,CAAC;CACH;AAOM,SAASnD,qBAAqB,CAACwB,YAAiB,EAAmB;IACxE,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;QACpC,MAAM,IAAI8B,KAAK,CAAC,CAAC,mDAAmD,EAAE,OAAO9B,YAAY,CAAC,CAAC,CAAC,CAAC;KAC9F,MAAM,IAAI,CAACA,YAAY,EAAE;QACxB,OAAO,EAAE,CAAC;KACX;IAED,MAAM+B,SAAS,GAAoB,EAAE,AAAC;IAEtC,KAAK,MAAMC,GAAG,IAAIJ,MAAM,CAACK,IAAI,CAACjC,YAAY,CAAC,CAAE;QAC3C,MAAMkC,KAAK,GAAGlC,YAAY,CAACgC,GAAG,CAAC,AAAC;QAChC,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;YAC7BH,SAAS,CAACC,GAAG,CAAC,GAAGE,KAAK,CAAC;SACxB,MAAM;YACL,MAAM,IAAIJ,KAAK,CACb,CAAC,qBAAqB,EAAEE,GAAG,CAAC,4CAA4C,EAAEA,GAAG,CAAC,EAAE,EAAErC,IAAI,CAACC,SAAS,CAC9FsC,KAAK,CACN,CAAC,IAAI,CAAC,CACR,CAAC;SACH;KACF;IACD,OAAOH,SAAS,CAAC;CAClB;AAED;;;GAGG,CACH,SAASjC,gBAAgB,CAAC,EAAEhB,GAAG,CAAA,EAA8B,EAAE;QAIxDA,GAAmB,EAGnBA,IAAe;IANpB,IAAI,CAACA,GAAG,CAACqD,OAAO,EAAE;QAChBrD,GAAG,CAACqD,OAAO,GAAG,EAAE,CAAC;KAClB;IACD,IAAI,EAACrD,CAAAA,GAAmB,GAAnBA,GAAG,CAACqD,OAAO,CAACC,OAAO,SAAU,GAA7BtD,KAAAA,CAA6B,GAA7BA,GAAmB,CAAEgC,QAAQ,CAAC,KAAK,CAAC,CAAA,EAAE;QACzChC,GAAG,CAACqD,OAAO,CAACC,OAAO,GAAG,kBAAkB,CAAC;KAC1C;IACD,IAAI,EAACtD,CAAAA,IAAe,GAAfA,GAAG,CAACqD,OAAO,CAACE,GAAG,SAAU,GAAzBvD,KAAAA,CAAyB,GAAzBA,IAAe,CAAEgC,QAAQ,CAAC,KAAK,CAAC,CAAA,EAAE;QACrChC,GAAG,CAACqD,OAAO,CAACE,GAAG,GAAG,cAAc,CAAC;KAClC;CACF;AAED,SAASC,sBAAsB,CAACC,IAAqB,EAAY;IAC/D,OAAOX,MAAM,CAACK,IAAI,CAACM,IAAI,CAAC,CACrBnB,GAAG,CAAC,CAACoB,UAAU,GAAK,CAAC,EAAEA,UAAU,CAAC,CAAC,EAAED,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IAAA,CAAC,CACxDC,IAAI,EAAE,CAAC;CACX;AAEM,SAAShE,oBAAoB,CAAC8D,IAAqB,GAAG,EAAE,EAAU;IACvE,MAAMG,QAAQ,GAAGJ,sBAAsB,CAACC,IAAI,CAAC,AAAC;IAC9C,MAAMI,UAAU,GAAGD,QAAQ,CAACnB,IAAI,CAAC,IAAI,CAAC,AAAC;IACvC,OAAO7C,cAAc,CAACiE,UAAU,CAAC,CAAC;CACnC;AAEM,SAASjE,cAAc,CAACkE,QAAgB,EAAU;IACvD,0DAA0D;IAC1D,OAAOC,OAAM,QAAA,CAACC,UAAU,CAAC,MAAM,CAAC,CAACC,MAAM,CAACH,QAAQ,CAAC,CAACI,MAAM,CAAC,KAAK,CAAC,CAAC;CACjE;AAED;;;GAGG,CACH,SAAShC,sBAAsB,CAACiC,MAA4B,EAAEC,MAA4B,EAAE;IAC1F,IAAI;QACF,OAAOC,CAAAA,GAAAA,OAAgB,AAAgB,CAAA,WAAhB,CAACF,MAAM,EAAEC,MAAM,CAAC,CAAC;KACzC,CAAC,OAAM;QACN,OAAO,KAAK,CAAC;KACd;CACF"}