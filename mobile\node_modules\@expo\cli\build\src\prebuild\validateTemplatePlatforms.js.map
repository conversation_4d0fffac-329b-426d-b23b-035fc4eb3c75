{"version": 3, "sources": ["../../../src/prebuild/validateTemplatePlatforms.ts"], "sourcesContent": ["import { ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { directoryExistsAsync } from '../utils/dir';\n\nexport async function validateTemplatePlatforms({\n  templateDirectory,\n  platforms,\n}: {\n  templateDirectory: string;\n  platforms: ModPlatform[];\n}) {\n  const existingPlatforms: ModPlatform[] = [];\n\n  for (const platform of platforms) {\n    if (await directoryExistsAsync(path.join(templateDirectory, platform))) {\n      existingPlatforms.push(platform);\n    } else {\n      Log.warn(\n        chalk`⚠️  Skipping platform ${platform}. Use a template that contains native files for ${platform} (./${platform}).`\n      );\n    }\n  }\n\n  return existingPlatforms;\n}\n"], "names": ["validateTemplatePlatforms", "Log", "templateDirectory", "platforms", "existingPlatforms", "platform", "directoryExistsAsync", "path", "join", "push", "warn", "chalk"], "mappings": "AAAA;;;;QAOsBA,yBAAyB,GAAzBA,yBAAyB;AAN7B,IAAA,MAAO,kCAAP,OAAO,EAAA;AACR,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEXC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACsB,IAAA,IAAc,WAAd,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5C,eAAeD,yBAAyB,CAAC,EAC9CE,iBAAiB,CAAA,EACjBC,SAAS,CAAA,EAIV,EAAE;IACD,MAAMC,iBAAiB,GAAkB,EAAE,AAAC;IAE5C,KAAK,MAAMC,QAAQ,IAAIF,SAAS,CAAE;QAChC,IAAI,MAAMG,CAAAA,GAAAA,IAAoB,AAAwC,CAAA,qBAAxC,CAACC,KAAI,QAAA,CAACC,IAAI,CAACN,iBAAiB,EAAEG,QAAQ,CAAC,CAAC,EAAE;YACtED,iBAAiB,CAACK,IAAI,CAACJ,QAAQ,CAAC,CAAC;SAClC,MAAM;YACLJ,GAAG,CAACS,IAAI,CACNC,MAAK,QAAA,CAAC,0BAAsB,EAAEN,QAAQ,CAAC,gDAAgD,EAAEA,QAAQ,CAAC,IAAI,EAAEA,QAAQ,CAAC,EAAE,CAAC,CACrH,CAAC;SACH;KACF;IAED,OAAOD,iBAAiB,CAAC;CAC1B"}