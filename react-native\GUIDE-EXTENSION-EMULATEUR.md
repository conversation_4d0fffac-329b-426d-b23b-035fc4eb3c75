# Guide d'utilisation - Extension Android iOS Emulator

## 📱 Introduction

Ce guide vous explique comment utiliser l'extension **Android iOS Emulator** pour VS Code afin de lancer votre projet AquaTrack Mobile sur un émulateur Android.

## 🔧 Prérequis

### 1. Logiciels requis
- **VS Code** (Visual Studio Code)
- **Node.js** (version 16 ou supérieure)
- **Android Studio** (pour les émulateurs Android)
- **Extension Android iOS Emulator** pour VS Code

### 2. Installation de l'extension
1. Ouvrez VS Code
2. Appuyez sur `Ctrl + Shift + X` pour ouvrir les extensions
3. Recherchez "Android iOS Emulator"
4. Installez l'extension de **Diemas <PERSON>els**
5. Redémarrez VS Code

## 🚀 Utilisation rapide

### Méthode 1: Scripts automatisés
```bash
# Depuis la racine du projet
start-mobile-emulator.bat

# Ou depuis le dossier react-native
start-emulator-extension.bat
```

### Méthode 2: PowerShell (recommandé)
```powershell
# Depuis le dossier react-native
.\start-emulator-extension.ps1

# Avec démarrage automatique
.\start-emulator-extension.ps1 -AutoStart

# Sans vérification des dépendances
.\start-emulator-extension.ps1 -SkipDependencies
```

## 📋 Étapes détaillées

### 1. Préparation de l'environnement
1. Ouvrez VS Code dans le dossier `react-native`
2. Vérifiez que les dépendances sont installées:
   ```bash
   npm install
   ```

### 2. Configuration des émulateurs Android
1. Ouvrez Android Studio
2. Allez dans **Tools > AVD Manager**
3. Créez un ou plusieurs émulateurs Android
4. Testez qu'ils se lancent correctement

### 3. Utilisation de l'extension

#### Méthode A: Palette de commandes
1. Dans VS Code, appuyez sur `Ctrl + Shift + P`
2. Tapez "Emulate"
3. Sélectionnez "**Emulate: Run Android**"
4. Choisissez l'émulateur dans la liste
5. Attendez que l'émulateur se lance

#### Méthode B: Raccourci clavier
1. Appuyez sur `Ctrl + Shift + R`
2. Sélectionnez l'émulateur Android
3. L'émulateur se lance automatiquement

### 4. Démarrage de l'application
1. Une fois l'émulateur démarré, ouvrez le terminal VS Code (`Ctrl + \``)
2. Exécutez:
   ```bash
   npm start
   ```
3. Dans l'interface Expo, appuyez sur `a` pour Android
4. L'application se lance sur l'émulateur

## 🔑 Compte de test

Pour tester l'application, utilisez ces identifiants:
- **Email**: <EMAIL>
- **Mot de passe**: Tech123

## ⚡ Raccourcis utiles

| Raccourci | Action |
|-----------|--------|
| `Ctrl + Shift + P` | Palette de commandes |
| `Ctrl + Shift + R` | Lancer émulateur rapide |
| `Ctrl + \`` | Ouvrir terminal VS Code |
| `Ctrl + Shift + X` | Extensions VS Code |

## 🛠️ Commandes npm disponibles

```bash
# Démarrer Expo
npm start

# Démarrer sur Android
npm run android

# Démarrer sur iOS (Mac uniquement)
npm run ios

# Démarrer sur Web
npm run web
```

## 🔧 Résolution de problèmes

### Problème: Extension non trouvée
**Solution**: Installez l'extension manuellement
```bash
code --install-extension DiemasMichiels.emulate
```

### Problème: Émulateur ne se lance pas
**Solutions**:
1. Vérifiez qu'Android Studio est installé
2. Créez un émulateur dans AVD Manager
3. Vérifiez les variables d'environnement Android

### Problème: Application ne se connecte pas
**Solutions**:
1. Vérifiez que le serveur backend est démarré (port 4000)
2. Vérifiez l'adresse IP dans la configuration
3. Redémarrez Expo avec `npm start --clear`

### Problème: Dépendances manquantes
**Solution**:
```bash
# Nettoyer et réinstaller
rm -rf node_modules
npm install

# Ou avec le cache
npm install --force
```

## 📱 Configuration des émulateurs

### Émulateurs recommandés
- **Pixel 3a API 30** (Android 11)
- **Pixel 4 API 31** (Android 12)
- **Nexus 5X API 29** (Android 10)

### Configuration optimale
- **RAM**: 2048 MB minimum
- **Stockage**: 2 GB minimum
- **GPU**: Hardware acceleration activée

## 🔄 Workflow de développement

1. **Démarrage**:
   ```bash
   .\start-emulator-extension.ps1 -AutoStart
   ```

2. **Développement**:
   - Modifiez le code
   - L'application se recharge automatiquement
   - Utilisez les outils de debug React Native

3. **Test**:
   - Testez sur différents émulateurs
   - Vérifiez les fonctionnalités (caméra, GPS, etc.)

4. **Debug**:
   - Utilisez React Native Debugger
   - Consultez les logs dans VS Code
   - Utilisez Flipper pour le debug avancé

## 📞 Support

Si vous rencontrez des problèmes:
1. Consultez la documentation de l'extension
2. Vérifiez les logs dans VS Code
3. Redémarrez VS Code et l'émulateur
4. Consultez la documentation React Native/Expo

## 🎯 Conseils d'optimisation

1. **Performance**:
   - Fermez les émulateurs inutilisés
   - Utilisez un SSD pour de meilleures performances
   - Allouez suffisamment de RAM à l'émulateur

2. **Productivité**:
   - Créez des raccourcis pour vos émulateurs favoris
   - Utilisez les scripts automatisés
   - Configurez des profils d'émulateurs spécifiques

3. **Debug**:
   - Activez le mode développeur sur l'émulateur
   - Utilisez les outils de debug intégrés
   - Configurez les logs appropriés
