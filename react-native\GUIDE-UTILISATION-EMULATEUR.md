# 📱 Guide d'utilisation de l'extension Android iOS Emulator

## 🎯 Objectif
Ouvri<PERSON> et exécuter votre projet AquaTrack (React Native + Node.js) sur l'extension Android iOS Emulator dans VS Code.

## 📋 Prérequis
- ✅ Extension "Android iOS Emulator" installée dans VS Code
- ✅ Projet React Native dans le dossier `react-native`
- ✅ Serveur Node.js dans le dossier `server`

## 🚀 Étapes détaillées

### 1. Lancer l'émulateur Android
1. **Ouvrez VS Code** dans le dossier de votre projet
2. **Appuyez sur `Ctrl + Shift + P`** (Palette de commandes)
3. **Tapez "Emulate"** et sélectionnez **"Emulate: Run Android"**
4. **Choisissez un émulateur** dans la liste (ex: Pixel 4, Galaxy S10, etc.)
5. **Attendez** que l'émulateur démarre complètement

### 2. Démarrer le serveur backend
```bash
cd server
npm start
```

### 3. Démarrer l'application React Native
```bash
cd react-native
npx expo start --android
```

### 4. Déployer sur l'émulateur
Une fois Expo démarré :
- **Appuyez sur `a`** pour déployer automatiquement sur Android
- Ou **scannez le QR code** avec l'émulateur

## 🔧 Commandes utiles

### Démarrage rapide
```bash
# Dans le dossier react-native
.\start-on-emulator.bat
```

### Commandes manuelles
```bash
# Démarrer Expo
npx expo start

# Démarrer avec Android
npx expo start --android

# Démarrer en mode tunnel (pour réseau)
npx expo start --tunnel
```

## 🎮 Contrôles de l'émulateur
- **R** : Recharger l'application
- **D** : Ouvrir le menu développeur
- **Ctrl + M** : Menu développeur (alternative)

## 🔑 Compte de test
- **Email** : `<EMAIL>`
- **Mot de passe** : `Tech123`

## 🛠️ Résolution de problèmes

### Problème : Émulateur ne démarre pas
**Solution** :
1. Vérifiez que l'extension est bien installée
2. Redémarrez VS Code
3. Essayez un autre émulateur dans la liste

### Problème : Application ne se connecte pas
**Solution** :
1. Vérifiez que le serveur backend est démarré
2. Vérifiez l'adresse IP dans la configuration
3. Utilisez `npx expo start --tunnel` si nécessaire

### Problème : Expo ne démarre pas
**Solution** :
```bash
# Nettoyer le cache
npx expo start --clear

# Réinstaller les dépendances
npm install
```

## 📱 Fonctionnalités de l'application
1. **Authentification** avec compte technicien
2. **Liste des clients** avec cartes interactives
3. **Scanner QR** pour identifier les clients
4. **Saisie de consommation** d'eau
5. **Génération de factures** PDF
6. **Localisation** des clients sur carte

## 🎯 Prochaines étapes
1. Lancez l'émulateur avec l'extension
2. Démarrez le serveur backend
3. Lancez Expo avec `npx expo start --android`
4. Testez l'application avec le compte de test
