@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    AQUATRACK MOBILE - EMULATEUR
echo    Extension Android iOS Emulator
echo ========================================
echo.

REM Couleurs pour les messages
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

echo %BLUE%[INFO]%RESET% Démarrage du projet AquaTrack Mobile...
echo.

REM Étape 1: Vérification de VS Code
echo %BLUE%[1/5]%RESET% Vérification de VS Code...
where code >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[ERREUR]%RESET% VS Code n'est pas installé ou pas dans le PATH
    echo Veuillez installer VS Code et l'ajouter au PATH
    pause
    exit /b 1
)
echo %GREEN%✓%RESET% VS Code détecté

REM Étape 2: Vérification de l'extension Android iOS Emulator
echo.
echo %BLUE%[2/5]%RESET% Vérification de l'extension Android iOS Emulator...
code --list-extensions | findstr "DiemasMichiels.emulate" >nul 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%[ATTENTION]%RESET% Extension Android iOS Emulator non détectée
    echo Installation automatique de l'extension...
    code --install-extension DiemasMichiels.emulate
    if %errorlevel% neq 0 (
        echo %RED%[ERREUR]%RESET% Échec de l'installation de l'extension
        echo Veuillez installer manuellement l'extension "Android iOS Emulator"
        pause
        exit /b 1
    )
    echo %GREEN%✓%RESET% Extension installée avec succès
) else (
    echo %GREEN%✓%RESET% Extension Android iOS Emulator détectée
)

REM Étape 3: Vérification du projet
echo.
echo %BLUE%[3/5]%RESET% Vérification du projet React Native...
if not exist "package.json" (
    echo %RED%[ERREUR]%RESET% Fichier package.json non trouvé!
    echo Assurez-vous d'être dans le dossier react-native
    pause
    exit /b 1
)
echo %GREEN%✓%RESET% Projet React Native détecté

REM Étape 4: Installation des dépendances
echo.
echo %BLUE%[4/5]%RESET% Vérification des dépendances...
if not exist "node_modules" (
    echo %YELLOW%[INFO]%RESET% Installation des dépendances npm...
    npm install
    if %errorlevel% neq 0 (
        echo %RED%[ERREUR]%RESET% Échec de l'installation des dépendances
        pause
        exit /b 1
    )
    echo %GREEN%✓%RESET% Dépendances installées
) else (
    echo %GREEN%✓%RESET% Dépendances déjà installées
)

REM Étape 5: Ouverture de VS Code
echo.
echo %BLUE%[5/5]%RESET% Ouverture du projet dans VS Code...
start "" code .
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo %GREEN%PROJET OUVERT AVEC SUCCÈS!%RESET%
echo ========================================
echo.
echo %YELLOW%ÉTAPES SUIVANTES:%RESET%
echo.
echo %BLUE%1. LANCER L'ÉMULATEUR:%RESET%
echo    • Dans VS Code: Ctrl + Shift + P
echo    • Tapez: "Emulate"
echo    • Sélectionnez: "Emulate: Run Android"
echo    • Choisissez votre émulateur préféré
echo.
echo %BLUE%2. DÉMARRER L'APPLICATION:%RESET%
echo    • Attendez que l'émulateur soit complètement démarré
echo    • Dans le terminal VS Code: npm start
echo    • Ou appuyez sur 'a' pour Android dans Expo
echo.
echo %BLUE%3. COMPTE DE TEST:%RESET%
echo    • Email: <EMAIL>
echo    • Mot de passe: Tech123
echo.
echo ========================================
echo %YELLOW%RACCOURCIS UTILES:%RESET%
echo ========================================
echo • Ctrl + Shift + R : Lancer émulateur rapide
echo • Ctrl + ` : Ouvrir terminal VS Code
echo • npm start : Démarrer Expo
echo • npm run android : Démarrer sur Android
echo ========================================

echo.
echo %BLUE%Voulez-vous démarrer Expo automatiquement? (O/N)%RESET%
set /p choice="Votre choix: "
if /i "!choice!"=="O" (
    echo.
    echo %GREEN%Démarrage d'Expo...%RESET%
    start cmd /k "cd /d %cd% && npm start"
    echo %GREEN%✓%RESET% Expo démarré dans une nouvelle fenêtre
) else (
    echo.
    echo %BLUE%Projet prêt!%RESET% Utilisez VS Code pour lancer l'émulateur.
)

echo.
echo %GREEN%Configuration terminée avec succès!%RESET%
pause
