# Script PowerShell pour lancer AquaTrack Mobile avec l'extension Android iOS Emulator
param(
    [switch]$AutoStart,
    [switch]$SkipDependencies
)

# Configuration des couleurs
$Host.UI.RawUI.ForegroundColor = "White"

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success($message) {
    Write-ColorOutput Green "✓ $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "✗ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠ $message"
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ $message"
}

function Write-Header($message) {
    Write-ColorOutput Magenta "========================================`n   $message`n========================================"
}

# Début du script
Clear-Host
Write-Header "AQUATRACK MOBILE - EMULATEUR EXTENSION"

Write-Info "Démarrage du projet AquaTrack Mobile avec l'extension Android iOS Emulator..."
Write-Output ""

# Étape 1: Vérification de VS Code
Write-Info "[1/6] Vérification de VS Code..."
try {
    $vscode = Get-Command code -ErrorAction Stop
    Write-Success "VS Code détecté: $($vscode.Source)"
} catch {
    Write-Error "VS Code n'est pas installé ou pas dans le PATH"
    Write-Warning "Veuillez installer VS Code et l'ajouter au PATH système"
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Étape 2: Vérification de Node.js et npm
Write-Output ""
Write-Info "[2/6] Vérification de Node.js et npm..."
try {
    $node = Get-Command node -ErrorAction Stop
    $npm = Get-Command npm -ErrorAction Stop
    Write-Success "Node.js détecté: $(node --version)"
    Write-Success "npm détecté: $(npm --version)"
} catch {
    Write-Error "Node.js ou npm n'est pas installé"
    Write-Warning "Veuillez installer Node.js depuis https://nodejs.org/"
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Étape 3: Vérification de l'extension Android iOS Emulator
Write-Output ""
Write-Info "[3/6] Vérification de l'extension Android iOS Emulator..."
$extensions = & code --list-extensions
if ($extensions -contains "DiemasMichiels.emulate") {
    Write-Success "Extension Android iOS Emulator détectée"
} else {
    Write-Warning "Extension Android iOS Emulator non détectée"
    Write-Info "Installation automatique de l'extension..."
    try {
        & code --install-extension DiemasMichiels.emulate
        Write-Success "Extension installée avec succès"
    } catch {
        Write-Error "Échec de l'installation de l'extension"
        Write-Warning "Veuillez installer manuellement l'extension 'Android iOS Emulator' dans VS Code"
        Read-Host "Appuyez sur Entrée pour continuer quand même"
    }
}

# Étape 4: Vérification du projet
Write-Output ""
Write-Info "[4/6] Vérification du projet React Native..."
if (Test-Path "package.json") {
    $packageJson = Get-Content "package.json" | ConvertFrom-Json
    Write-Success "Projet détecté: $($packageJson.name) v$($packageJson.version)"
} else {
    Write-Error "Fichier package.json non trouvé!"
    Write-Warning "Assurez-vous d'être dans le dossier react-native"
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Étape 5: Installation des dépendances
Write-Output ""
Write-Info "[5/6] Vérification des dépendances..."
if (-not $SkipDependencies) {
    if (-not (Test-Path "node_modules")) {
        Write-Warning "Dossier node_modules non trouvé"
        Write-Info "Installation des dépendances npm..."
        try {
            & npm install
            Write-Success "Dépendances installées avec succès"
        } catch {
            Write-Error "Échec de l'installation des dépendances"
            Read-Host "Appuyez sur Entrée pour continuer quand même"
        }
    } else {
        Write-Success "Dépendances déjà installées"
    }
} else {
    Write-Info "Vérification des dépendances ignorée (--SkipDependencies)"
}

# Étape 6: Ouverture de VS Code
Write-Output ""
Write-Info "[6/6] Ouverture du projet dans VS Code..."
try {
    Start-Process code -ArgumentList "." -NoNewWindow
    Start-Sleep -Seconds 2
    Write-Success "VS Code ouvert avec succès"
} catch {
    Write-Error "Échec de l'ouverture de VS Code"
    Read-Host "Appuyez sur Entrée pour continuer"
}

# Instructions finales
Write-Output ""
Write-Header "CONFIGURATION TERMINÉE!"

Write-ColorOutput Green @"

✅ PROJET PRÊT À UTILISER!

📱 ÉTAPES SUIVANTES:

1. LANCER L'ÉMULATEUR:
   • Dans VS Code: Ctrl + Shift + P
   • Tapez: "Emulate"
   • Sélectionnez: "Emulate: Run Android"
   • Choisissez votre émulateur Android préféré

2. DÉMARRER L'APPLICATION:
   • Attendez que l'émulateur soit complètement démarré
   • Dans le terminal VS Code: npm start
   • Ou appuyez sur 'a' pour Android dans Expo

3. COMPTE DE TEST:
   • Email: <EMAIL>
   • Mot de passe: Tech123

🔧 RACCOURCIS UTILES:
   • Ctrl + Shift + R : Lancer émulateur rapide
   • Ctrl + ` : Ouvrir terminal VS Code
   • npm start : Démarrer Expo
   • npm run android : Démarrer sur Android

"@

# Option de démarrage automatique
if ($AutoStart) {
    Write-Info "Démarrage automatique d'Expo..."
    Start-Process cmd -ArgumentList "/k", "cd /d $PWD && npm start"
    Write-Success "Expo démarré dans une nouvelle fenêtre"
} else {
    Write-Output ""
    $choice = Read-Host "Voulez-vous démarrer Expo automatiquement? (O/N)"
    if ($choice -eq "O" -or $choice -eq "o") {
        Write-Info "Démarrage d'Expo..."
        Start-Process cmd -ArgumentList "/k", "cd /d $PWD && npm start"
        Write-Success "Expo démarré dans une nouvelle fenêtre"
    }
}

Write-Output ""
Write-ColorOutput Magenta "🎉 Configuration terminée avec succès!"
Write-Output ""
Read-Host "Appuyez sur Entrée pour fermer"
