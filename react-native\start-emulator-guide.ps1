# Script PowerShell pour démarrer AquaTrack avec l'émulateur Android iOS
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    AQUATRACK MOBILE - ÉMULATEUR" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 GUIDE D'UTILISATION DE L'EXTENSION ANDROID iOS EMULATOR" -ForegroundColor Yellow
Write-Host ""

Write-Host "🔧 ÉTAPE 1: INSTALLATION DE L'EXTENSION" -ForegroundColor Blue
Write-Host "  1. Ouvrez VS Code" -ForegroundColor White
Write-Host "  2. Appuyez sur Ctrl + Shift + X (Extensions)" -ForegroundColor White
Write-Host "  3. Recherchez 'Android iOS Emulator' par DiemasMichiels" -ForegroundColor White
Write-Host "  4. Cliquez sur 'Installer'" -ForegroundColor White
Write-Host ""

Write-Host "🚀 ÉTAPE 2: LANCEMENT DE L'ÉMULATEUR" -ForegroundColor Blue
Write-Host "  1. Dans VS Code, appuyez sur Ctrl + Shift + P" -ForegroundColor White
Write-Host "  2. Tapez 'Emulate' et sélectionnez 'Emulate: Run Android'" -ForegroundColor White
Write-Host "  3. Choisissez votre émulateur Android dans la liste" -ForegroundColor White
Write-Host "  4. Attendez que l'émulateur démarre complètement" -ForegroundColor White
Write-Host ""

Write-Host "📱 ÉTAPE 3: DÉMARRAGE DE L'APPLICATION" -ForegroundColor Blue
Write-Host "  1. Ouvrez un terminal dans VS Code (Ctrl + `)" -ForegroundColor White
Write-Host "  2. Tapez: npm start" -ForegroundColor Green
Write-Host "  3. Appuyez sur 'a' pour Android quand Expo démarre" -ForegroundColor White
Write-Host ""

Write-Host "🔑 COMPTE DE TEST:" -ForegroundColor Magenta
Write-Host "  Email: <EMAIL>" -ForegroundColor Green
Write-Host "  Mot de passe: Tech123" -ForegroundColor Green
Write-Host ""

Write-Host "⚡ RACCOURCIS UTILES:" -ForegroundColor Yellow
Write-Host "  • Ctrl + Shift + P : Palette de commandes" -ForegroundColor White
Write-Host "  • Ctrl + ` : Terminal VS Code" -ForegroundColor White
Write-Host "  • R : Recharger l'app dans l'émulateur" -ForegroundColor White
Write-Host "  • D : Ouvrir le menu développeur" -ForegroundColor White
Write-Host ""

Write-Host "🔄 DÉMARRAGE AUTOMATIQUE DU SERVEUR BACKEND..." -ForegroundColor Blue
Write-Host ""

# Démarrer le serveur backend
$serverPath = "..\server"
if (Test-Path $serverPath) {
    Write-Host "✓ Démarrage du serveur backend..." -ForegroundColor Green
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$serverPath'; npm start" -WindowStyle Normal
    Start-Sleep -Seconds 3
} else {
    Write-Host "⚠ Serveur backend non trouvé dans $serverPath" -ForegroundColor Yellow
}

Write-Host "🚀 DÉMARRAGE D'EXPO..." -ForegroundColor Blue
Write-Host ""

# Vérifier si nous sommes dans le bon dossier
if (-not (Test-Path "package.json")) {
    Write-Host "❌ ERREUR: Fichier package.json non trouvé!" -ForegroundColor Red
    Write-Host "Assurez-vous d'être dans le dossier react-native" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

Write-Host "✓ Projet React Native détecté" -ForegroundColor Green
Write-Host ""

# Démarrer Expo
Write-Host "📱 Démarrage d'Expo..." -ForegroundColor Blue
Write-Host "Une fois Expo démarré, appuyez sur 'a' pour Android" -ForegroundColor Yellow
Write-Host ""

try {
    & npm start
} catch {
    Write-Host "❌ ERREUR: Échec du démarrage d'Expo" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

Write-Host ""
Write-Host "✅ CONFIGURATION TERMINÉE!" -ForegroundColor Green
Read-Host "Appuyez sur Entrée pour fermer"
