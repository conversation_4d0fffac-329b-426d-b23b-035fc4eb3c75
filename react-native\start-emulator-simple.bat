@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    AQUATRACK MOBILE - EMULATEUR
echo ========================================
echo.

echo [INFO] Guide pour utiliser l'extension Android iOS Emulator
echo.

echo ETAPE 1: INSTALLATION DE L'EXTENSION
echo   1. Ouvrez VS Code
echo   2. Appuyez sur Ctrl + Shift + X (Extensions)
echo   3. Recherchez "Android iOS Emulator" par DiemasMichiels
echo   4. C<PERSON>z sur "Installer"
echo.

echo ETAPE 2: LANCEMENT DE L'EMULATEUR
echo   1. Dans VS Code, appuyez sur Ctrl + Shift + P
echo   2. Tapez "Emulate" et selectionnez "Emulate: Run Android"
echo   3. Choisissez votre emulateur Android dans la liste
echo   4. Attendez que l'emulateur demarre completement
echo.

echo ETAPE 3: <PERSON>MAR<PERSON><PERSON> DE L'APPLICATION
echo   1. Ouvrez un terminal dans VS Code (Ctrl + `)
echo   2. Tapez: npm start
echo   3. Appuyez sur 'a' pour Android quand Expo demarre
echo.

echo COMPTE DE TEST:
echo   Email: <EMAIL>
echo   Mot de passe: Tech123
echo.

echo ========================================
echo DEMARRAGE AUTOMATIQUE...
echo ========================================
echo.

REM Démarrer le serveur backend
echo [1/3] Demarrage du serveur backend...
if exist "..\server\package.json" (
    start "Backend Server" cmd /k "cd ..\server && npm start"
    echo Backend demarre dans une nouvelle fenetre
    timeout /t 3 /nobreak >nul
) else (
    echo Serveur backend non trouve
)

echo.
echo [2/3] Ouverture de VS Code...
if exist "package.json" (
    start "" code .
    timeout /t 3 /nobreak >nul
    echo VS Code ouvert
) else (
    echo Erreur: package.json non trouve!
    pause
    exit /b 1
)

echo.
echo [3/3] Demarrage d'Expo...
echo.
echo INSTRUCTIONS:
echo 1. Dans VS Code, appuyez sur Ctrl + Shift + P
echo 2. Tapez "Emulate" et selectionnez "Emulate: Run Android"
echo 3. Choisissez votre emulateur
echo 4. Dans le terminal VS Code, tapez: npm start
echo 5. Appuyez sur 'a' pour Android
echo.

echo Voulez-vous demarrer Expo maintenant? (O/N)
set /p choice="Votre choix: "
if /i "!choice!"=="O" (
    echo.
    echo Demarrage d'Expo...
    npm start
) else (
    echo.
    echo Utilisez VS Code pour lancer l'emulateur et Expo.
)

echo.
echo Configuration terminee!
pause
