@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    AQUATRACK - EMULATEUR ANDROID iOS
echo ========================================
echo.

echo [INFO] Demarrage du projet sur l'extension Android iOS Emulator
echo.

REM Vérification du projet
if not exist "package.json" (
    echo [ERREUR] Fichier package.json non trouve!
    echo Assurez-vous d'etre dans le dossier react-native
    pause
    exit /b 1
)

echo [1/4] Verification du projet React Native...
echo ✓ Projet detecte
echo.

echo [2/4] Demarrage du serveur backend...
if exist "..\server\package.json" (
    start "Backend Server" cmd /k "cd ..\server && npm start"
    echo ✓ Serveur backend demarre
    timeout /t 3 /nobreak >nul
) else (
    echo ⚠ Serveur backend non trouve
)

echo.
echo [3/4] Instructions pour l'emulateur:
echo.
echo DANS VS CODE:
echo 1. Appuyez sur Ctrl + Shift + P
echo 2. Tapez "Emulate" 
echo 3. Selectionnez "Emulate: Run Android"
echo 4. Choisissez votre emulateur Android
echo 5. Attendez que l'emulateur demarre
echo.

echo [4/4] Demarrage d'Expo...
echo.
echo Une fois l'emulateur Android lance:
echo - Appuyez sur 'a' pour deployer sur Android
echo - Ou scannez le QR code avec l'emulateur
echo.

REM Démarrer Expo
echo Demarrage d'Expo en cours...
npx expo start --android

echo.
echo ========================================
echo PROJET DEMARRE!
echo ========================================
echo.
echo COMPTE DE TEST:
echo Email: <EMAIL>
echo Mot de passe: Tech123
echo.
pause
