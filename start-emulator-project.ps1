# Script PowerShell pour lancer le projet React Native avec émulateur
# Utilisation: .\start-emulator-project.ps1

Write-Host "🚀 Démarrage du projet AquaTrack Mobile avec émulateur..." -ForegroundColor Green

# Vérification des prérequis
Write-Host "📋 Vérification des prérequis..." -ForegroundColor Yellow

# Vérifier si Android SDK est installé
$androidHome = $env:ANDROID_HOME
if (-not $androidHome) {
    $androidHome = "$env:LOCALAPPDATA\Android\Sdk"
}

if (-not (Test-Path $androidHome)) {
    Write-Host "❌ Android SDK non trouvé. Veuillez installer Android Studio." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Android SDK trouvé: $androidHome" -ForegroundColor Green

# Vérifier si l'émulateur existe
$emulatorPath = "$androidHome\emulator\emulator.exe"
if (-not (Test-Path $emulatorPath)) {
    Write-Host "❌ Émulateur Android non trouvé." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Émulateur Android trouvé" -ForegroundColor Green

# Aller dans le dossier react-native
Set-Location "react-native"

# Vérifier si les dépendances sont installées
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installation des dépendances..." -ForegroundColor Yellow
    npm install
}

# Démarrer Metro bundler en arrière-plan
Write-Host "🔄 Démarrage du Metro bundler..." -ForegroundColor Yellow
Start-Process -FilePath "cmd" -ArgumentList "/c", "npx expo start --clear" -WindowStyle Minimized

# Attendre que Metro soit prêt
Start-Sleep -Seconds 5

Write-Host "📱 Projet prêt ! Utilisez l'extension Android iOS Emulator dans VS Code pour:" -ForegroundColor Green
Write-Host "   1. Ouvrir la palette de commandes (Ctrl+Shift+P)" -ForegroundColor Cyan
Write-Host "   2. Taper 'Android Emulator'" -ForegroundColor Cyan
Write-Host "   3. Sélectionner votre émulateur" -ForegroundColor Cyan
Write-Host "   4. Une fois l'émulateur ouvert, appuyer sur 'a' dans le terminal Metro" -ForegroundColor Cyan

Write-Host "🎯 URL du projet: http://localhost:8081" -ForegroundColor Magenta
Write-Host "📂 Dossier du projet: $(Get-Location)" -ForegroundColor Magenta

# Garder le script ouvert
Write-Host "Appuyez sur une touche pour fermer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
